import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Plus, Trash2, Upload, Download } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface InvoiceItem {
  id: string;
  description: string;
  quantity: number;
  rate: number;
}

interface InvoiceData {
  invoiceNumber: string;
  date: string;
  clientName: string;
  clientEmail: string;
  clientAddress: string;
  items: InvoiceItem[];
  paymentTerms: string;
  notes: string;
}

interface InvoiceFormProps {
  onInvoiceChange: (data: InvoiceData) => void;
  onDownloadPDF: () => void;
}

const InvoiceForm: React.FC<InvoiceFormProps> = ({ onInvoiceChange, onDownloadPDF }) => {
  const { toast } = useToast();
  const [invoiceData, setInvoiceData] = useState<InvoiceData>({
    invoiceNumber: `INV-${Date.now().toString().slice(-6)}`,
    date: new Date().toISOString().split('T')[0],
    clientName: '',
    clientEmail: '',
    clientAddress: '',
    items: [{ id: '1', description: '', quantity: 1, rate: 0 }],
    paymentTerms: '30 days',
    notes: ''
  });

  const updateInvoiceData = (updates: Partial<InvoiceData>) => {
    const newData = { ...invoiceData, ...updates };
    setInvoiceData(newData);
    onInvoiceChange(newData);
  };

  const addItem = () => {
    const newItem: InvoiceItem = {
      id: Date.now().toString(),
      description: '',
      quantity: 1,
      rate: 0
    };
    updateInvoiceData({ items: [...invoiceData.items, newItem] });
  };

  const updateItem = (id: string, field: keyof InvoiceItem, value: string | number) => {
    const updatedItems = invoiceData.items.map(item =>
      item.id === id ? { ...item, [field]: value } : item
    );
    updateInvoiceData({ items: updatedItems });
  };

  const removeItem = (id: string) => {
    if (invoiceData.items.length > 1) {
      const updatedItems = invoiceData.items.filter(item => item.id !== id);
      updateInvoiceData({ items: updatedItems });
    }
  };

  const handleLogoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (file.size > 2 * 1024 * 1024) {
        toast({
          title: "File too large",
          description: "Please select an image smaller than 2MB",
          variant: "destructive"
        });
        return;
      }
      toast({
        title: "Logo uploaded",
        description: "Your company logo has been uploaded successfully"
      });
    }
  };

  return (
    <div className="space-y-6">
      <Card className="shadow-sm">
        <CardHeader>
          <CardTitle className="font-poppins font-bold text-xl text-primary">Company Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="logo" className="text-sm font-medium text-foreground">Company Logo</Label>
            <div className="mt-2">
              <div className="flex items-center gap-4">
                <input
                  id="logo"
                  type="file"
                  accept="image/*"
                  onChange={handleLogoUpload}
                  className="hidden"
                />
                <Button
                  variant="outline"
                  onClick={() => document.getElementById('logo')?.click()}
                  className="flex items-center gap-2"
                >
                  <Upload className="w-4 h-4" />
                  Upload Logo
                </Button>
                <span className="text-sm text-muted-foreground">Max 2MB</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="shadow-sm">
        <CardHeader>
          <CardTitle className="font-poppins font-bold text-xl text-primary">Invoice Details</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="invoiceNumber" className="text-sm font-medium text-foreground">Invoice Number</Label>
              <Input
                id="invoiceNumber"
                value={invoiceData.invoiceNumber}
                onChange={(e) => updateInvoiceData({ invoiceNumber: e.target.value })}
                className="mt-1"
              />
            </div>
            <div>
              <Label htmlFor="date" className="text-sm font-medium text-foreground">Date of Issue</Label>
              <Input
                id="date"
                type="date"
                value={invoiceData.date}
                onChange={(e) => updateInvoiceData({ date: e.target.value })}
                className="mt-1"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="shadow-sm">
        <CardHeader>
          <CardTitle className="font-poppins font-bold text-xl text-primary">Client Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="clientName" className="text-sm font-medium text-foreground">Client Name</Label>
            <Input
              id="clientName"
              value={invoiceData.clientName}
              onChange={(e) => updateInvoiceData({ clientName: e.target.value })}
              className="mt-1"
              placeholder="Enter client name"
            />
          </div>
          <div>
            <Label htmlFor="clientEmail" className="text-sm font-medium text-foreground">Client Email</Label>
            <Input
              id="clientEmail"
              type="email"
              value={invoiceData.clientEmail}
              onChange={(e) => updateInvoiceData({ clientEmail: e.target.value })}
              className="mt-1"
              placeholder="<EMAIL>"
            />
          </div>
          <div>
            <Label htmlFor="clientAddress" className="text-sm font-medium text-foreground">Client Address</Label>
            <Textarea
              id="clientAddress"
              value={invoiceData.clientAddress}
              onChange={(e) => updateInvoiceData({ clientAddress: e.target.value })}
              className="mt-1"
              placeholder="Enter client address"
              rows={3}
            />
          </div>
        </CardContent>
      </Card>

      <Card className="shadow-sm">
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle className="font-poppins font-bold text-xl text-primary">Invoice Items</CardTitle>
          <Button onClick={addItem} variant="outline" size="sm" className="flex items-center gap-2">
            <Plus className="w-4 h-4" />
            Add Item
          </Button>
        </CardHeader>
        <CardContent className="space-y-4">
          {invoiceData.items.map((item, index) => (
            <div key={item.id} className="p-4 bg-secondary/50 rounded-lg space-y-3">
              <div className="flex justify-between items-center">
                <span className="font-medium text-foreground">Item {index + 1}</span>
                {invoiceData.items.length > 1 && (
                  <Button
                    onClick={() => removeItem(item.id)}
                    variant="ghost"
                    size="sm"
                    className="text-destructive hover:text-destructive"
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                )}
              </div>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                <div className="md:col-span-1">
                  <Label className="text-sm font-medium text-foreground">Description</Label>
                  <Input
                    value={item.description}
                    onChange={(e) => updateItem(item.id, 'description', e.target.value)}
                    placeholder="Service/Product description"
                    className="mt-1"
                  />
                </div>
                <div>
                  <Label className="text-sm font-medium text-foreground">Quantity</Label>
                  <Input
                    type="number"
                    min="1"
                    value={item.quantity}
                    onChange={(e) => updateItem(item.id, 'quantity', parseInt(e.target.value) || 1)}
                    className="mt-1"
                  />
                </div>
                <div>
                  <Label className="text-sm font-medium text-foreground">Rate (₹)</Label>
                  <Input
                    type="number"
                    min="0"
                    step="0.01"
                    value={item.rate}
                    onChange={(e) => updateItem(item.id, 'rate', parseFloat(e.target.value) || 0)}
                    className="mt-1"
                  />
                </div>
              </div>
              <div className="text-right">
                <span className="text-sm font-medium text-muted-foreground">
                  Amount: ₹{(item.quantity * item.rate).toFixed(2)}
                </span>
              </div>
            </div>
          ))}
        </CardContent>
      </Card>

      <Card className="shadow-sm">
        <CardHeader>
          <CardTitle className="font-poppins font-bold text-xl text-primary">Additional Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="paymentTerms" className="text-sm font-medium text-foreground">Payment Terms</Label>
            <Input
              id="paymentTerms"
              value={invoiceData.paymentTerms}
              onChange={(e) => updateInvoiceData({ paymentTerms: e.target.value })}
              className="mt-1"
              placeholder="e.g., 30 days, Net 15"
            />
          </div>
          <div>
            <Label htmlFor="notes" className="text-sm font-medium text-foreground">Notes (Optional)</Label>
            <Textarea
              id="notes"
              value={invoiceData.notes}
              onChange={(e) => updateInvoiceData({ notes: e.target.value })}
              className="mt-1"
              placeholder="Any additional notes or terms"
              rows={3}
            />
          </div>
        </CardContent>
      </Card>

      <div className="flex justify-center">
        <Button 
          onClick={onDownloadPDF}
          className="bg-gradient-to-r from-accent to-accent/90 hover:from-accent/90 hover:to-accent text-accent-foreground font-poppins font-semibold px-8 py-6 text-lg shadow-lg"
          size="lg"
        >
          <Download className="w-5 h-5 mr-2" />
          Download Invoice PDF
        </Button>
      </div>
    </div>
  );
};

export default InvoiceForm;