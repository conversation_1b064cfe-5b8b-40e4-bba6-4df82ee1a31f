import React, { useState } from 'react';
import InvoiceForm from '@/components/InvoiceForm';
import InvoicePreview from '@/components/InvoicePreview';
import { generateInvoicePDF } from '@/utils/pdfGenerator';
import { useToast } from '@/hooks/use-toast';

interface InvoiceItem {
  id: string;
  description: string;
  quantity: number;
  rate: number;
}

interface InvoiceData {
  invoiceNumber: string;
  date: string;
  clientName: string;
  clientEmail: string;
  clientAddress: string;
  items: InvoiceItem[];
  paymentTerms: string;
  notes: string;
}

const Index = () => {
  const { toast } = useToast();
  const [invoiceData, setInvoiceData] = useState<InvoiceData>({
    invoiceNumber: `INV-${Date.now().toString().slice(-6)}`,
    date: new Date().toISOString().split('T')[0],
    clientName: '',
    clientEmail: '',
    clientAddress: '',
    items: [{ id: '1', description: '', quantity: 1, rate: 0 }],
    paymentTerms: '30 days',
    notes: ''
  });

  const handleInvoiceChange = (data: InvoiceData) => {
    setInvoiceData(data);
  };

  const handleDownloadPDF = async () => {
    if (!invoiceData.clientName.trim()) {
      toast({
        title: "Client name required",
        description: "Please enter a client name before generating the invoice",
        variant: "destructive"
      });
      return;
    }

    if (invoiceData.items.some(item => !item.description.trim())) {
      toast({
        title: "Item descriptions required",
        description: "Please fill in all item descriptions before generating the invoice",
        variant: "destructive"
      });
      return;
    }

    try {
      await generateInvoicePDF(invoiceData);
      toast({
        title: "Invoice generated!",
        description: "Your invoice PDF has been downloaded successfully"
      });
    } catch (error) {
      console.error('Error generating PDF:', error);
      toast({
        title: "Error generating PDF",
        description: "There was an error generating your invoice. Please try again.",
        variant: "destructive"
      });
    }
  };

  return (
    <div className="min-h-screen bg-background font-poppins">
      {/* Header */}
      <header className="bg-white shadow-soft border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center gap-4">
            <img 
              src="/lovable-uploads/7b54540a-e206-4ab4-b487-14e038e28808.png" 
              alt="ZestX Media Logo" 
              className="h-12 w-auto"
            />
            <div>
              <h1 className="text-2xl font-bold text-primary font-poppins">Invoice Generator</h1>
              <p className="text-sm text-muted-foreground">Create professional invoices for ZestX Media</p>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Form Section */}
          <div className="space-y-6">
            <div>
              <h2 className="text-xl font-poppins font-bold text-primary mb-2">Invoice Information</h2>
              <p className="text-muted-foreground">Fill in the details to generate your professional invoice</p>
            </div>
            <InvoiceForm 
              onInvoiceChange={handleInvoiceChange}
              onDownloadPDF={handleDownloadPDF}
            />
          </div>

          {/* Preview Section */}
          <div className="space-y-6">
            <div className="sticky top-8">
              <h2 className="text-xl font-poppins font-bold text-primary mb-4">Live Preview</h2>
              <InvoicePreview data={invoiceData} />
            </div>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-primary text-primary-foreground py-8 mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <p className="font-poppins font-medium">
            © 2024 ZestX Media. Professional invoice generation made simple.
          </p>
        </div>
      </footer>
    </div>
  );
};

export default Index;
