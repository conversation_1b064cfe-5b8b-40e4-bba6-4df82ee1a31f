import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';

interface InvoiceItem {
  id: string;
  description: string;
  quantity: number;
  rate: number;
}

interface InvoiceData {
  invoiceNumber: string;
  date: string;
  clientName: string;
  clientEmail: string;
  clientAddress: string;
  items: InvoiceItem[];
  paymentTerms: string;
  notes: string;
}

interface InvoicePreviewProps {
  data: InvoiceData;
}

const InvoicePreview: React.FC<InvoicePreviewProps> = ({ data }) => {
  const calculateSubtotal = () => {
    return data.items.reduce((sum, item) => sum + (item.quantity * item.rate), 0);
  };

  const subtotal = calculateSubtotal();

  const formatDate = (dateString: string) => {
    if (!dateString) return '';
    return new Date(dateString).toLocaleDateString('en-IN', {
      day: '2-digit',
      month: 'long',
      year: 'numeric'
    });
  };

  return (
    <Card className="shadow-lg border-0 bg-white" id="invoice-preview">
      <CardContent className="p-8">
        {/* Header */}
        <div className="flex flex-col md:flex-row justify-between items-start mb-8">
          <div className="mb-6 md:mb-0">
            <div className="flex items-center gap-4 mb-4">
              <img 
                src="/lovable-uploads/7b54540a-e206-4ab4-b487-14e038e28808.png" 
                alt="ZestX Media Logo" 
                className="h-16 w-auto"
              />
            </div>
            <div className="text-sm text-muted-foreground">
              <p className="font-medium">ZestX Media</p>
              <p>Professional Media Services</p>
            </div>
          </div>
          <div className="text-right">
            <h1 className="text-3xl font-poppins font-bold text-primary mb-2">INVOICE</h1>
            <div className="text-sm space-y-1">
              <p><span className="font-medium">Invoice #:</span> {data.invoiceNumber}</p>
              <p><span className="font-medium">Date:</span> {formatDate(data.date)}</p>
            </div>
          </div>
        </div>

        <Separator className="my-6" />

        {/* Bill To */}
        <div className="mb-8">
          <h3 className="text-lg font-poppins font-semibold text-primary mb-3">Bill To:</h3>
          <div className="text-sm space-y-1">
            <p className="font-medium text-foreground">{data.clientName || 'Client Name'}</p>
            {data.clientEmail && <p className="text-muted-foreground">{data.clientEmail}</p>}
            {data.clientAddress && (
              <div className="text-muted-foreground whitespace-pre-line">
                {data.clientAddress}
              </div>
            )}
          </div>
        </div>

        {/* Items Table */}
        <div className="mb-8">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b-2 border-primary/20">
                  <th className="text-left py-3 font-poppins font-semibold text-primary">Description</th>
                  <th className="text-center py-3 font-poppins font-semibold text-primary w-20">Qty</th>
                  <th className="text-right py-3 font-poppins font-semibold text-primary w-24">Rate</th>
                  <th className="text-right py-3 font-poppins font-semibold text-primary w-24">Amount</th>
                </tr>
              </thead>
              <tbody>
                {data.items.map((item, index) => (
                  <tr key={item.id} className="border-b border-border">
                    <td className="py-3 text-foreground">
                      {item.description || `Service/Product ${index + 1}`}
                    </td>
                    <td className="py-3 text-center text-foreground">{item.quantity}</td>
                    <td className="py-3 text-right text-foreground">₹{item.rate.toFixed(2)}</td>
                    <td className="py-3 text-right font-medium text-foreground">
                      ₹{(item.quantity * item.rate).toFixed(2)}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Total */}
        <div className="flex justify-end mb-8">
          <div className="w-64">
            <div className="bg-primary/5 p-4 rounded-lg">
              <div className="flex justify-between items-center mb-2">
                <span className="font-medium text-foreground">Subtotal:</span>
                <span className="text-foreground">₹{subtotal.toFixed(2)}</span>
              </div>
              <Separator className="my-2" />
              <div className="flex justify-between items-center">
                <span className="text-lg font-poppins font-bold text-primary">Total:</span>
                <span className="text-lg font-poppins font-bold text-primary">₹{subtotal.toFixed(2)}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Payment Terms */}
        {data.paymentTerms && (
          <div className="mb-6">
            <h4 className="font-poppins font-semibold text-primary mb-2">Payment Terms:</h4>
            <p className="text-sm text-muted-foreground">{data.paymentTerms}</p>
          </div>
        )}

        {/* Notes */}
        {data.notes && (
          <div className="mb-6">
            <h4 className="font-poppins font-semibold text-primary mb-2">Notes:</h4>
            <p className="text-sm text-muted-foreground whitespace-pre-line">{data.notes}</p>
          </div>
        )}

        {/* Footer */}
        <div className="text-center pt-6 border-t border-border">
          <p className="text-sm text-muted-foreground">
            Thank you for your business!
          </p>
          <div className="mt-2 text-xs text-muted-foreground">
            <p>ZestX Media - Professional Media Services</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default InvoicePreview;