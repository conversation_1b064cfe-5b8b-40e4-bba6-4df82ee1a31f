import { _ as _class_apply_descriptor_destructure } from "./_class_apply_descriptor_destructure.js";
import { _ as _class_extract_field_descriptor } from "./_class_extract_field_descriptor.js";

function _class_private_field_destructure(receiver, privateMap) {
    var descriptor = _class_extract_field_descriptor(receiver, privateMap, "set");
    return _class_apply_descriptor_destructure(receiver, descriptor);
}
export { _class_private_field_destructure as _ };
