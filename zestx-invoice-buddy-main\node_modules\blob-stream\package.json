{"name": "blob-stream", "version": "0.1.3", "description": "A Node-style writable stream for HTML5 Blobs", "main": "index.js", "directories": {"test": "test"}, "scripts": {"test": "browserify test/index.js | testling", "build": "browserify --standalone blobStream . -o blob-stream.js"}, "repository": {"type": "git", "url": "http://github.com/devongovett/blob-stream"}, "author": "<PERSON> Go<PERSON>t <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/devongovett/blob-stream/issues"}, "homepage": "https://github.com/devongovett/blob-stream", "dependencies": {"blob": "0.0.4"}, "devDependencies": {"tape": "^2.12.3"}, "testling": {"files": "test/*.js", "browsers": ["ie/10..latest", "chrome/22..latest", "firefox/16..latest", "safari/6..latest", "opera/15..latest", "iphone/6..latest", "ipad/6..latest", "android-browser/latest"]}}