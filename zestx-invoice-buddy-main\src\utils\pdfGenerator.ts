import jsPDF from 'jspdf';

interface InvoiceItem {
  id: string;
  description: string;
  quantity: number;
  rate: number;
}

interface InvoiceData {
  invoiceNumber: string;
  date: string;
  clientName: string;
  clientEmail: string;
  clientAddress: string;
  items: InvoiceItem[];
  paymentTerms: string;
  notes: string;
}

// Helper function to format currency
const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: 'INR',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(amount);
};

// Helper function to format date
const formatDate = (dateString: string): string => {
  if (!dateString) return '';
  return new Date(dateString).toLocaleDateString('en-IN', {
    day: '2-digit',
    month: 'long',
    year: 'numeric'
  });
};

// Helper function to wrap text for jsPDF
const wrapText = (pdf: jsPDF, text: string, maxWidth: number): string[] => {
  const words = text.split(' ');
  const lines: string[] = [];
  let currentLine = '';

  for (const word of words) {
    const testLine = currentLine ? `${currentLine} ${word}` : word;
    const testWidth = pdf.getTextWidth(testLine);

    if (testWidth <= maxWidth) {
      currentLine = testLine;
    } else {
      if (currentLine) {
        lines.push(currentLine);
        currentLine = word;
      } else {
        lines.push(word);
      }
    }
  }

  if (currentLine) {
    lines.push(currentLine);
  }

  return lines;
};

export const generateInvoicePDF = (data: InvoiceData): void => {
  try {
    // Create a new PDF document with jsPDF
    const doc = new jsPDF({
      orientation: 'portrait',
      unit: 'mm',
      format: 'a4'
    });

      // Define colors (RGB values for jsPDF)
    const primaryColor: [number, number, number] = [25, 42, 86]; // #192a56
    const accentColor: [number, number, number] = [249, 109, 49]; // #f96d31
    const textColor: [number, number, number] = [51, 51, 51]; // #333333
    const grayColor: [number, number, number] = [128, 128, 128]; // #808080

    // Page dimensions (A4 in mm)
    const pageWidth = 210;
    const pageHeight = 297;
    const margin = 20;
    const contentWidth = pageWidth - (margin * 2);

    let yPosition = margin;

    // Header Section
    doc.setFontSize(24);
    doc.setTextColor(...primaryColor);
    doc.setFont('helvetica', 'bold');
    doc.text('ZestX', margin, yPosition);

    const zestxWidth = doc.getTextWidth('ZestX');
    doc.setTextColor(...accentColor);
    doc.text('MEDIA', margin + zestxWidth + 2, yPosition);

    // Invoice title (right aligned)
    doc.setFontSize(28);
    doc.setTextColor(...primaryColor);
    doc.text('INVOICE', pageWidth - margin - 40, yPosition, { align: 'right' });

    yPosition += 15;

    // Company tagline
    doc.setFontSize(9);
    doc.setTextColor(...grayColor);
    doc.setFont('helvetica', 'normal');
    doc.text('Professional Media Services', margin, yPosition);

    // Invoice details (right aligned)
    doc.setFontSize(10);
    doc.setTextColor(...textColor);
    doc.text(`Invoice #: ${data.invoiceNumber}`, pageWidth - margin, yPosition, { align: 'right' });

    yPosition += 7;
    const formattedDate = formatDate(data.date);
    doc.text(`Date: ${formattedDate}`, pageWidth - margin, yPosition, { align: 'right' });

    yPosition += 15;

    // Horizontal line
    doc.setDrawColor(...primaryColor);
    doc.setLineWidth(0.5);
    doc.line(margin, yPosition, pageWidth - margin, yPosition);

    yPosition += 15;

    // Bill To Section
    doc.setFontSize(12);
    doc.setTextColor(...primaryColor);
    doc.setFont('helvetica', 'bold');
    doc.text('Bill To:', margin, yPosition);

    yPosition += 10;

    // Client information
    doc.setFontSize(11);
    doc.setTextColor(...textColor);
    doc.setFont('helvetica', 'bold');
    doc.text(data.clientName || 'Client Name', margin, yPosition);

    yPosition += 8;

    if (data.clientEmail) {
      doc.setFontSize(9);
      doc.setFont('helvetica', 'normal');
      doc.text(data.clientEmail, margin, yPosition);
      yPosition += 6;
    }

    if (data.clientAddress) {
      const addressLines = data.clientAddress.split('\n');
      doc.setFontSize(9);
      doc.setFont('helvetica', 'normal');

      addressLines.forEach(line => {
        if (line.trim()) {
          doc.text(line.trim(), margin, yPosition);
          yPosition += 6;
        }
      });
    }

    yPosition += 10;

    // Items Table
    const tableTop = yPosition;
    const descriptionX = margin;
    const quantityX = pageWidth - margin - 60;
    const rateX = pageWidth - margin - 40;
    const amountX = pageWidth - margin - 20;

    // Table header background
    doc.setFillColor(245, 245, 245); // Light gray background
    doc.rect(margin, tableTop, contentWidth, 8, 'F');

    // Table headers
    doc.setFontSize(10);
    doc.setTextColor(...primaryColor);
    doc.setFont('helvetica', 'bold');
    doc.text('Description', descriptionX, tableTop + 6);
    doc.text('Qty', quantityX, tableTop + 6, { align: 'center' });
    doc.text('Rate', rateX, tableTop + 6, { align: 'right' });
    doc.text('Amount', amountX, tableTop + 6, { align: 'right' });

    yPosition = tableTop + 15;

    // Table items
    let subtotal = 0;
    doc.setFontSize(9);
    doc.setTextColor(...textColor);
    doc.setFont('helvetica', 'normal');

    data.items.forEach((item, index) => {
      const itemAmount = item.quantity * item.rate;
      subtotal += itemAmount;

      // Alternate row background
      if (index % 2 === 1) {
        doc.setFillColor(250, 250, 250);
        doc.rect(margin, yPosition - 2, contentWidth, 8, 'F');
      }

      const description = item.description || `Service/Product ${index + 1}`;
      const wrappedDescription = wrapText(doc, description, 100);

      // Calculate row height based on description lines
      const rowHeight = Math.max(8, wrappedDescription.length * 4);

      doc.setTextColor(...textColor);

      // Description (with text wrapping)
      wrappedDescription.forEach((line, lineIndex) => {
        doc.text(line, descriptionX, yPosition + (lineIndex * 4));
      });

      // Quantity (centered)
      doc.text(item.quantity.toString(), quantityX, yPosition, { align: 'center' });

      // Rate (right aligned with proper currency formatting)
      doc.text(formatCurrency(item.rate), rateX, yPosition, { align: 'right' });

      // Amount (right aligned with proper currency formatting)
      doc.text(formatCurrency(itemAmount), amountX, yPosition, { align: 'right' });

      yPosition += rowHeight + 2;
    });

    yPosition += 10;

    // Total section
    const totalSectionX = pageWidth - margin - 50;

    // Subtotal line
    doc.setDrawColor(...grayColor);
    doc.setLineWidth(0.2);
    doc.line(totalSectionX, yPosition, pageWidth - margin, yPosition);

    yPosition += 8;

    doc.setFontSize(10);
    doc.setTextColor(...textColor);
    doc.setFont('helvetica', 'normal');
    doc.text('Subtotal:', totalSectionX, yPosition);
    doc.text(formatCurrency(subtotal), pageWidth - margin, yPosition, { align: 'right' });

    yPosition += 8;

    // Total line (bold and larger)
    doc.setFontSize(12);
    doc.setTextColor(...primaryColor);
    doc.setFont('helvetica', 'bold');
    doc.text('Total:', totalSectionX, yPosition);
    doc.text(formatCurrency(subtotal), pageWidth - margin, yPosition, { align: 'right' });

    yPosition += 20;

    // Payment terms and notes
    if (data.paymentTerms) {
      doc.setFontSize(10);
      doc.setTextColor(...primaryColor);
      doc.setFont('helvetica', 'bold');
      doc.text('Payment Terms:', margin, yPosition);

      yPosition += 8;

      doc.setFontSize(9);
      doc.setTextColor(...textColor);
      doc.setFont('helvetica', 'normal');
      const paymentTermsLines = wrapText(doc, data.paymentTerms, contentWidth);
      paymentTermsLines.forEach(line => {
        doc.text(line, margin, yPosition);
        yPosition += 5;
      });

      yPosition += 10;
    }

    if (data.notes) {
      doc.setFontSize(10);
      doc.setTextColor(...primaryColor);
      doc.setFont('helvetica', 'bold');
      doc.text('Notes:', margin, yPosition);

      yPosition += 8;

      doc.setFontSize(9);
      doc.setTextColor(...textColor);
      doc.setFont('helvetica', 'normal');
      const notesLines = wrapText(doc, data.notes, contentWidth);
      notesLines.forEach(line => {
        doc.text(line, margin, yPosition);
        yPosition += 5;
      });

      yPosition += 10;
    }

    // Footer
    const footerY = pageHeight - margin - 20;

    doc.setFontSize(8);
    doc.setTextColor(...grayColor);
    doc.setFont('helvetica', 'normal');
    doc.text('Thank you for your business!', pageWidth / 2, footerY, { align: 'center' });

    doc.setFontSize(7);
    doc.text('ZestX Media - Professional Media Services', pageWidth / 2, footerY + 5, { align: 'center' });

    // Save the PDF
    const filename = `ZestX-Invoice-${data.invoiceNumber}.pdf`;
    doc.save(filename);

  } catch (error) {
    console.error('Error generating PDF:', error);
    throw error;
  }
};