import jsPDF from 'jspdf';

interface InvoiceItem {
  id: string;
  description: string;
  quantity: number;
  rate: number;
}

interface InvoiceData {
  invoiceNumber: string;
  date: string;
  clientName: string;
  clientEmail: string;
  clientAddress: string;
  items: InvoiceItem[];
  paymentTerms: string;
  notes: string;
}

// Helper function to format date exactly like preview
const formatDate = (dateString: string): string => {
  if (!dateString) return '';
  return new Date(dateString).toLocaleDateString('en-IN', {
    day: '2-digit',
    month: 'long',
    year: 'numeric'
  });
};

// Helper function to wrap text for jsPDF
const wrapText = (pdf: jsPDF, text: string, maxWidth: number): string[] => {
  const words = text.split(' ');
  const lines: string[] = [];
  let currentLine = '';

  for (const word of words) {
    const testLine = currentLine ? `${currentLine} ${word}` : word;
    const testWidth = pdf.getTextWidth(testLine);

    if (testWidth <= maxWidth) {
      currentLine = testLine;
    } else {
      if (currentLine) {
        lines.push(currentLine);
        currentLine = word;
      } else {
        lines.push(word);
      }
    }
  }

  if (currentLine) {
    lines.push(currentLine);
  }

  return lines;
};

// Helper function to load image as base64
const loadImageAsBase64 = (imagePath: string): Promise<string> => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.crossOrigin = 'anonymous';
    img.onload = () => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      if (!ctx) {
        reject(new Error('Could not get canvas context'));
        return;
      }
      canvas.width = img.width;
      canvas.height = img.height;
      ctx.drawImage(img, 0, 0);
      const dataURL = canvas.toDataURL('image/png');
      resolve(dataURL);
    };
    img.onerror = () => reject(new Error('Failed to load image'));
    img.src = imagePath;
  });
};

export const generateInvoicePDF = async (data: InvoiceData): Promise<void> => {
  try {
    // Create a new PDF document with jsPDF
    const doc = new jsPDF({
      orientation: 'portrait',
      unit: 'mm',
      format: 'a4'
    });

    // Define colors exactly matching the preview (RGB values for jsPDF)
    const primaryColor: [number, number, number] = [25, 42, 86]; // #192a56 - text-primary
    const textColor: [number, number, number] = [15, 23, 42]; // #0f172a - text-foreground
    const mutedColor: [number, number, number] = [100, 116, 139]; // #64748b - text-muted-foreground
    const borderColor: [number, number, number] = [226, 232, 240]; // #e2e8f0 - border

    // Page dimensions (A4 in mm)
    const pageWidth = 210;
    const pageHeight = 297;
    const margin = 20;
    const contentWidth = pageWidth - (margin * 2);

    let yPosition = margin + 10;

    // Header Section - Left side with logo and company info
    try {
      // Load and add logo image
      const logoBase64 = await loadImageAsBase64('/lovable-uploads/7b54540a-e206-4ab4-b487-14e038e28808.png');
      doc.addImage(logoBase64, 'PNG', margin, yPosition, 20, 16); // 20mm width, 16mm height to match h-16 w-auto
    } catch (error) {
      console.warn('Could not load logo image:', error);
    }

    // Company info below logo
    yPosition += 20;
    doc.setFontSize(10);
    doc.setTextColor(...textColor);
    doc.setFont('helvetica', 'bold');
    doc.text('ZestX Media', margin, yPosition);

    yPosition += 5;
    doc.setFontSize(9);
    doc.setTextColor(...mutedColor);
    doc.setFont('helvetica', 'normal');
    doc.text('Professional Media Services', margin, yPosition);

    // Invoice title and details (right aligned) - matching preview position
    const invoiceStartY = margin + 10;
    doc.setFontSize(24);
    doc.setTextColor(...primaryColor);
    doc.setFont('helvetica', 'bold');
    doc.text('INVOICE', pageWidth - margin, invoiceStartY, { align: 'right' });

    // Invoice details
    let detailsY = invoiceStartY + 15;
    doc.setFontSize(9);
    doc.setTextColor(...textColor);
    doc.setFont('helvetica', 'normal');

    // Invoice # with bold label
    doc.setFont('helvetica', 'bold');
    const invoiceLabel = 'Invoice #: ';
    const invoiceLabelWidth = doc.getTextWidth(invoiceLabel);
    doc.text(invoiceLabel, pageWidth - margin - invoiceLabelWidth - doc.getTextWidth(data.invoiceNumber), detailsY);
    doc.setFont('helvetica', 'normal');
    doc.text(data.invoiceNumber, pageWidth - margin, detailsY, { align: 'right' });

    detailsY += 5;
    // Date with bold label
    doc.setFont('helvetica', 'bold');
    const dateLabel = 'Date: ';
    const formattedDate = formatDate(data.date);
    const dateLabelWidth = doc.getTextWidth(dateLabel);
    doc.text(dateLabel, pageWidth - margin - dateLabelWidth - doc.getTextWidth(formattedDate), detailsY);
    doc.setFont('helvetica', 'normal');
    doc.text(formattedDate, pageWidth - margin, detailsY, { align: 'right' });

    // Horizontal separator line (matching preview)
    yPosition = Math.max(yPosition + 15, detailsY + 15);
    doc.setDrawColor(...borderColor);
    doc.setLineWidth(0.3);
    doc.line(margin, yPosition, pageWidth - margin, yPosition);

    yPosition += 15;

    // Bill To Section - matching preview exactly
    doc.setFontSize(12);
    doc.setTextColor(...primaryColor);
    doc.setFont('helvetica', 'bold');
    doc.text('Bill To:', margin, yPosition);

    yPosition += 8;

    // Client information
    doc.setFontSize(10);
    doc.setTextColor(...textColor);
    doc.setFont('helvetica', 'bold');
    doc.text(data.clientName || 'Client Name', margin, yPosition);

    yPosition += 6;

    if (data.clientEmail) {
      doc.setFontSize(9);
      doc.setTextColor(...mutedColor);
      doc.setFont('helvetica', 'normal');
      doc.text(data.clientEmail, margin, yPosition);
      yPosition += 5;
    }

    if (data.clientAddress) {
      const addressLines = data.clientAddress.split('\n');
      doc.setFontSize(9);
      doc.setTextColor(...mutedColor);
      doc.setFont('helvetica', 'normal');

      addressLines.forEach(line => {
        if (line.trim()) {
          doc.text(line.trim(), margin, yPosition);
          yPosition += 5;
        }
      });
    }

    yPosition += 15;

    // Items Table - matching preview exactly with serial numbers
    const tableTop = yPosition;
    const serialX = margin;
    const descriptionX = margin + 15;
    const quantityX = pageWidth - margin - 60;
    const rateX = pageWidth - margin - 40;
    const amountX = pageWidth - margin - 20;

    // Table header with border (matching preview)
    doc.setDrawColor(...borderColor);
    doc.setLineWidth(0.5);
    doc.line(margin, tableTop + 8, pageWidth - margin, tableTop + 8);

    // Table headers
    doc.setFontSize(10);
    doc.setTextColor(...primaryColor);
    doc.setFont('helvetica', 'bold');
    doc.text('#', serialX, tableTop + 6);
    doc.text('Description', descriptionX, tableTop + 6);
    doc.text('Qty', quantityX, tableTop + 6, { align: 'center' });
    doc.text('Rate', rateX, tableTop + 6, { align: 'right' });
    doc.text('Amount', amountX, tableTop + 6, { align: 'right' });

    yPosition = tableTop + 15;

    // Table items - matching preview exactly with serial numbers
    let subtotal = 0;
    doc.setFontSize(9);
    doc.setTextColor(...textColor);
    doc.setFont('helvetica', 'normal');

    data.items.forEach((item, index) => {
      const itemAmount = item.quantity * item.rate;
      subtotal += itemAmount;

      // Row border (matching preview)
      doc.setDrawColor(...borderColor);
      doc.setLineWidth(0.2);
      doc.line(margin, yPosition + 6, pageWidth - margin, yPosition + 6);

      const description = item.description || `Service/Product ${index + 1}`;

      // Serial number
      doc.text((index + 1).toString(), serialX, yPosition + 3);

      // Description
      doc.text(description, descriptionX, yPosition + 3);

      // Quantity (centered)
      doc.text(item.quantity.toString(), quantityX, yPosition + 3, { align: 'center' });

      // Rate (right aligned with ₹ symbol matching preview)
      doc.text(`₹${item.rate.toFixed(2)}`, rateX, yPosition + 3, { align: 'right' });

      // Amount (right aligned with ₹ symbol and bold matching preview)
      doc.setFont('helvetica', 'bold');
      doc.text(`₹${itemAmount.toFixed(2)}`, amountX, yPosition + 3, { align: 'right' });
      doc.setFont('helvetica', 'normal');

      yPosition += 8;
    });

    yPosition += 10;

    // Total section - matching preview exactly with background box
    const totalBoxWidth = 50;
    const totalBoxX = pageWidth - margin - totalBoxWidth;

    // Total box background (matching preview bg-primary/5)
    doc.setFillColor(248, 250, 252); // Very light blue background
    doc.rect(totalBoxX, yPosition, totalBoxWidth, 20, 'F');

    // Subtotal
    yPosition += 5;
    doc.setFontSize(9);
    doc.setTextColor(...textColor);
    doc.setFont('helvetica', 'bold');
    doc.text('Subtotal:', totalBoxX + 2, yPosition);
    doc.setFont('helvetica', 'normal');
    doc.text(`₹${subtotal.toFixed(2)}`, pageWidth - margin - 2, yPosition, { align: 'right' });

    yPosition += 3;
    // Separator line inside box
    doc.setDrawColor(...borderColor);
    doc.setLineWidth(0.2);
    doc.line(totalBoxX + 2, yPosition, pageWidth - margin - 2, yPosition);

    yPosition += 5;
    // Total line (bold and larger matching preview)
    doc.setFontSize(12);
    doc.setTextColor(...primaryColor);
    doc.setFont('helvetica', 'bold');
    doc.text('Total:', totalBoxX + 2, yPosition);
    doc.text(`₹${subtotal.toFixed(2)}`, pageWidth - margin - 2, yPosition, { align: 'right' });

    yPosition += 20;

    // Payment terms and notes
    if (data.paymentTerms) {
      doc.setFontSize(10);
      doc.setTextColor(...primaryColor);
      doc.setFont('helvetica', 'bold');
      doc.text('Payment Terms:', margin, yPosition);

      yPosition += 8;

      doc.setFontSize(9);
      doc.setTextColor(...textColor);
      doc.setFont('helvetica', 'normal');
      const paymentTermsLines = wrapText(doc, data.paymentTerms, contentWidth);
      paymentTermsLines.forEach(line => {
        doc.text(line, margin, yPosition);
        yPosition += 5;
      });

      yPosition += 10;
    }

    if (data.notes) {
      doc.setFontSize(10);
      doc.setTextColor(...primaryColor);
      doc.setFont('helvetica', 'bold');
      doc.text('Notes:', margin, yPosition);

      yPosition += 8;

      doc.setFontSize(9);
      doc.setTextColor(...textColor);
      doc.setFont('helvetica', 'normal');
      const notesLines = wrapText(doc, data.notes, contentWidth);
      notesLines.forEach(line => {
        doc.text(line, margin, yPosition);
        yPosition += 5;
      });

      yPosition += 10;
    }

    // Footer - matching preview exactly with border
    const footerY = pageHeight - margin - 25;

    // Footer border line
    doc.setDrawColor(...borderColor);
    doc.setLineWidth(0.2);
    doc.line(margin, footerY - 5, pageWidth - margin, footerY - 5);

    doc.setFontSize(9);
    doc.setTextColor(...mutedColor);
    doc.setFont('helvetica', 'normal');
    doc.text('Thank you for your business!', pageWidth / 2, footerY, { align: 'center' });

    doc.setFontSize(8);
    doc.text('ZestX Media - Professional Media Services', pageWidth / 2, footerY + 5, { align: 'center' });

    // Save the PDF
    const filename = `ZestX-Invoice-${data.invoiceNumber}.pdf`;
    doc.save(filename);

  } catch (error) {
    console.error('Error generating PDF:', error);
    throw error;
  }
};