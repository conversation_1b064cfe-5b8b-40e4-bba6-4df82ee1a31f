import PDFDocument from 'pdfkit';

interface InvoiceItem {
  id: string;
  description: string;
  quantity: number;
  rate: number;
}

interface InvoiceData {
  invoiceNumber: string;
  date: string;
  clientName: string;
  clientEmail: string;
  clientAddress: string;
  items: InvoiceItem[];
  paymentTerms: string;
  notes: string;
}

// Helper function to format currency
const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: 'INR',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(amount);
};

// Helper function to format date
const formatDate = (dateString: string): string => {
  if (!dateString) return '';
  return new Date(dateString).toLocaleDateString('en-IN', {
    day: '2-digit',
    month: 'long',
    year: 'numeric'
  });
};

// Helper function to wrap text
const wrapText = (doc: PDFKit.PDFDocument, text: string, maxWidth: number): string[] => {
  const words = text.split(' ');
  const lines: string[] = [];
  let currentLine = '';

  for (const word of words) {
    const testLine = currentLine ? `${currentLine} ${word}` : word;
    const testWidth = doc.widthOfString(testLine);

    if (testWidth <= maxWidth) {
      currentLine = testLine;
    } else {
      if (currentLine) {
        lines.push(currentLine);
        currentLine = word;
      } else {
        lines.push(word);
      }
    }
  }

  if (currentLine) {
    lines.push(currentLine);
  }

  return lines;
};

export const generateInvoicePDF = (data: InvoiceData): void => {
  try {
    // Create a new PDF document
    const doc = new PDFDocument({
      size: 'A4',
      margin: 50,
      info: {
        Title: `Invoice ${data.invoiceNumber}`,
        Author: 'ZestX Media',
        Subject: 'Invoice',
        Creator: 'ZestX Invoice Generator'
      }
    });

    // Create a buffer to store the PDF
    const chunks: Buffer[] = [];
    doc.on('data', (chunk) => chunks.push(chunk));
    doc.on('end', () => {
      const pdfBuffer = Buffer.concat(chunks);
      const blob = new Blob([pdfBuffer], { type: 'application/pdf' });
      const url = URL.createObjectURL(blob);

      // Create download link
      const link = document.createElement('a');
      link.href = url;
      link.download = `ZestX-Invoice-${data.invoiceNumber}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    });

      // Define colors
      const primaryColor = '#192a56';
      const accentColor = '#f96d31';
      const textColor = '#333333';
      const grayColor = '#808080';
      const lightGrayColor = '#f5f5f5';

      // Page dimensions
      const pageWidth = doc.page.width;
      const pageHeight = doc.page.height;
      const margin = 50;
      const contentWidth = pageWidth - (margin * 2);

      let yPosition = margin;

      // Header Section
      doc.fontSize(28)
         .fillColor(primaryColor)
         .font('Helvetica-Bold')
         .text('ZestX', margin, yPosition);

      const zestxWidth = doc.widthOfString('ZestX');
      doc.fillColor(accentColor)
         .text('MEDIA', margin + zestxWidth + 5, yPosition);

      // Invoice title (right aligned)
      doc.fontSize(32)
         .fillColor(primaryColor)
         .text('INVOICE', pageWidth - margin - 120, yPosition, { width: 120, align: 'right' });

      yPosition += 40;

      // Company tagline
      doc.fontSize(10)
         .fillColor(grayColor)
         .font('Helvetica')
         .text('Professional Media Services', margin, yPosition);

      // Invoice details (right aligned)
      doc.fontSize(11)
         .fillColor(textColor)
         .font('Helvetica')
         .text(`Invoice #: ${data.invoiceNumber}`, pageWidth - margin - 150, yPosition, { width: 150, align: 'right' });

      yPosition += 15;
      const formattedDate = formatDate(data.date);
      doc.text(`Date: ${formattedDate}`, pageWidth - margin - 150, yPosition, { width: 150, align: 'right' });

      yPosition += 30;

      // Horizontal line
      doc.strokeColor(primaryColor)
         .lineWidth(2)
         .moveTo(margin, yPosition)
         .lineTo(pageWidth - margin, yPosition)
         .stroke();

      yPosition += 30;

      // Bill To Section
      doc.fontSize(14)
         .fillColor(primaryColor)
         .font('Helvetica-Bold')
         .text('Bill To:', margin, yPosition);

      yPosition += 20;

      // Client information
      doc.fontSize(12)
         .fillColor(textColor)
         .font('Helvetica-Bold')
         .text(data.clientName || 'Client Name', margin, yPosition);

      yPosition += 15;

      if (data.clientEmail) {
        doc.fontSize(10)
           .font('Helvetica')
           .text(data.clientEmail, margin, yPosition);
        yPosition += 12;
      }

      if (data.clientAddress) {
        const addressLines = data.clientAddress.split('\n');
        doc.fontSize(10)
           .font('Helvetica');

        addressLines.forEach(line => {
          if (line.trim()) {
            doc.text(line.trim(), margin, yPosition);
            yPosition += 12;
          }
        });
      }

      yPosition += 20;

      // Items Table
      const tableTop = yPosition;
      const descriptionX = margin;
      const quantityX = pageWidth - margin - 200;
      const rateX = pageWidth - margin - 130;
      const amountX = pageWidth - margin - 60;

      // Table header background
      doc.rect(margin, tableTop, contentWidth, 25)
         .fillColor(lightGrayColor)
         .fill();

      // Table headers
      doc.fontSize(11)
         .fillColor(primaryColor)
         .font('Helvetica-Bold')
         .text('Description', descriptionX, tableTop + 8)
         .text('Qty', quantityX, tableTop + 8, { width: 50, align: 'center' })
         .text('Rate', rateX, tableTop + 8, { width: 60, align: 'right' })
         .text('Amount', amountX, tableTop + 8, { width: 60, align: 'right' });

      yPosition = tableTop + 35;

      // Table items
      let subtotal = 0;
      doc.fontSize(10)
         .fillColor(textColor)
         .font('Helvetica');

      data.items.forEach((item, index) => {
        const itemAmount = item.quantity * item.rate;
        subtotal += itemAmount;

        // Alternate row background
        if (index % 2 === 1) {
          doc.rect(margin, yPosition - 5, contentWidth, 20)
             .fillColor('#fafafa')
             .fill();
        }

        const description = item.description || `Service/Product ${index + 1}`;
        const wrappedDescription = wrapText(doc, description, 200);

        // Calculate row height based on description lines
        const rowHeight = Math.max(20, wrappedDescription.length * 12);

        doc.fillColor(textColor);

        // Description (with text wrapping)
        wrappedDescription.forEach((line, lineIndex) => {
          doc.text(line, descriptionX, yPosition + (lineIndex * 12));
        });

        // Quantity (centered)
        doc.text(item.quantity.toString(), quantityX, yPosition, { width: 50, align: 'center' });

        // Rate (right aligned with proper currency formatting)
        doc.text(formatCurrency(item.rate), rateX, yPosition, { width: 60, align: 'right' });

        // Amount (right aligned with proper currency formatting)
        doc.text(formatCurrency(itemAmount), amountX, yPosition, { width: 60, align: 'right' });

        yPosition += rowHeight;
      });

      yPosition += 20;

      // Total section
      const totalSectionX = pageWidth - margin - 200;

      // Subtotal line
      doc.strokeColor(grayColor)
         .lineWidth(0.5)
         .moveTo(totalSectionX, yPosition)
         .lineTo(pageWidth - margin, yPosition)
         .stroke();

      yPosition += 15;

      doc.fontSize(11)
         .fillColor(textColor)
         .font('Helvetica')
         .text('Subtotal:', totalSectionX, yPosition)
         .text(formatCurrency(subtotal), totalSectionX + 80, yPosition, { width: 100, align: 'right' });

      yPosition += 20;

      // Total line (bold and larger)
      doc.fontSize(14)
         .fillColor(primaryColor)
         .font('Helvetica-Bold')
         .text('Total:', totalSectionX, yPosition)
         .text(formatCurrency(subtotal), totalSectionX + 80, yPosition, { width: 100, align: 'right' });

      yPosition += 40;

      // Payment terms and notes
      if (data.paymentTerms) {
        doc.fontSize(11)
           .fillColor(primaryColor)
           .font('Helvetica-Bold')
           .text('Payment Terms:', margin, yPosition);

        yPosition += 15;

        doc.fontSize(10)
           .fillColor(textColor)
           .font('Helvetica')
           .text(data.paymentTerms, margin, yPosition, { width: contentWidth });

        yPosition += 25;
      }

      if (data.notes) {
        doc.fontSize(11)
           .fillColor(primaryColor)
           .font('Helvetica-Bold')
           .text('Notes:', margin, yPosition);

        yPosition += 15;

        doc.fontSize(10)
           .fillColor(textColor)
           .font('Helvetica')
           .text(data.notes, margin, yPosition, { width: contentWidth });

        yPosition += 25;
      }

      // Footer
      const footerY = pageHeight - margin - 40;

      doc.fontSize(9)
         .fillColor(grayColor)
         .font('Helvetica')
         .text('Thank you for your business!', margin, footerY, { width: contentWidth, align: 'center' });

      doc.fontSize(8)
         .text('ZestX Media - Professional Media Services', margin, footerY + 15, { width: contentWidth, align: 'center' });

    // Finalize the PDF
    doc.end();

  } catch (error) {
    console.error('Error generating PDF:', error);
    throw error;
  }
};