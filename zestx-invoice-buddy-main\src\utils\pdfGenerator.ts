import jsPDF from 'jspdf';

interface InvoiceItem {
  id: string;
  description: string;
  quantity: number;
  rate: number;
}

interface InvoiceData {
  invoiceNumber: string;
  date: string;
  clientName: string;
  clientEmail: string;
  clientAddress: string;
  items: InvoiceItem[];
  paymentTerms: string;
  notes: string;
}

export const generateInvoicePDF = (data: InvoiceData) => {
  const pdf = new jsPDF();
  
  // Set up colors (converted to RGB)
  const primaryColor: [number, number, number] = [25, 42, 86]; // #192a56
  const accentColor: [number, number, number] = [249, 109, 49]; // #f96d31
  const textColor: [number, number, number] = [33, 33, 33];
  const grayColor: [number, number, number] = [128, 128, 128];

  // Header
  pdf.setFontSize(24);
  pdf.setTextColor(...primaryColor);
  pdf.setFont('helvetica', 'bold');
  pdf.text('ZestX', 20, 30);
  
  pdf.setTextColor(...accentColor);
  pdf.text('MEDIA', 55, 30);

  // Invoice title
  pdf.setFontSize(28);
  pdf.setTextColor(...primaryColor);
  pdf.setFont('helvetica', 'bold');
  pdf.text('INVOICE', 140, 30);

  // Invoice details
  pdf.setFontSize(10);
  pdf.setTextColor(...textColor);
  pdf.setFont('helvetica', 'normal');
  pdf.text(`Invoice #: ${data.invoiceNumber}`, 140, 45);
  
  const formattedDate = data.date ? new Date(data.date).toLocaleDateString('en-IN', {
    day: '2-digit',
    month: 'long',
    year: 'numeric'
  }) : '';
  pdf.text(`Date: ${formattedDate}`, 140, 52);

  // Company info
  pdf.setFontSize(9);
  pdf.setTextColor(...grayColor);
  pdf.text('Professional Media Services', 20, 40);

  // Line separator
  pdf.setDrawColor(...primaryColor);
  pdf.setLineWidth(0.5);
  pdf.line(20, 65, 190, 65);

  // Bill To section
  pdf.setFontSize(12);
  pdf.setTextColor(...primaryColor);
  pdf.setFont('helvetica', 'bold');
  pdf.text('Bill To:', 20, 80);

  pdf.setFontSize(10);
  pdf.setTextColor(...textColor);
  pdf.setFont('helvetica', 'bold');
  pdf.text(data.clientName || 'Client Name', 20, 90);

  pdf.setFont('helvetica', 'normal');
  let yPosition = 97;
  
  if (data.clientEmail) {
    pdf.text(data.clientEmail, 20, yPosition);
    yPosition += 7;
  }
  
  if (data.clientAddress) {
    const addressLines = data.clientAddress.split('\n');
    addressLines.forEach(line => {
      pdf.text(line, 20, yPosition);
      yPosition += 7;
    });
  }

  // Items table
  const tableStartY = Math.max(yPosition + 10, 120);
  
  // Table headers
  pdf.setFontSize(10);
  pdf.setTextColor(...primaryColor);
  pdf.setFont('helvetica', 'bold');
  
  pdf.text('Description', 20, tableStartY);
  pdf.text('Qty', 130, tableStartY);
  pdf.text('Rate', 150, tableStartY);
  pdf.text('Amount', 175, tableStartY);

  // Header line
  pdf.setDrawColor(...primaryColor);
  pdf.setLineWidth(0.3);
  pdf.line(20, tableStartY + 3, 190, tableStartY + 3);

  // Table items
  let itemY = tableStartY + 15;
  pdf.setFont('helvetica', 'normal');
  pdf.setTextColor(...textColor);

  data.items.forEach((item, index) => {
    const description = item.description || `Service/Product ${index + 1}`;
    const amount = (item.quantity * item.rate).toFixed(2);
    
    pdf.text(description, 20, itemY);
    pdf.text(item.quantity.toString(), 130, itemY);
    pdf.text(`₹${item.rate.toFixed(2)}`, 150, itemY);
    pdf.text(`₹${amount}`, 175, itemY);
    
    itemY += 12;
  });

  // Calculate total
  const subtotal = data.items.reduce((sum, item) => sum + (item.quantity * item.rate), 0);

  // Total section
  const totalY = itemY + 10;
  pdf.setDrawColor(...grayColor);
  pdf.setLineWidth(0.2);
  pdf.line(130, totalY, 190, totalY);

  pdf.setFontSize(10);
  pdf.setFont('helvetica', 'normal');
  pdf.text('Subtotal:', 130, totalY + 10);
  pdf.text(`₹${subtotal.toFixed(2)}`, 175, totalY + 10);

  pdf.setFont('helvetica', 'bold');
  pdf.setTextColor(...primaryColor);
  pdf.setFontSize(12);
  pdf.text('Total:', 130, totalY + 20);
  pdf.text(`₹${subtotal.toFixed(2)}`, 175, totalY + 20);

  // Payment terms and notes
  let footerY = totalY + 35;
  pdf.setFontSize(9);
  pdf.setTextColor(...textColor);
  pdf.setFont('helvetica', 'normal');

  if (data.paymentTerms) {
    pdf.setFont('helvetica', 'bold');
    pdf.text('Payment Terms:', 20, footerY);
    pdf.setFont('helvetica', 'normal');
    pdf.text(data.paymentTerms, 20, footerY + 7);
    footerY += 20;
  }

  if (data.notes) {
    pdf.setFont('helvetica', 'bold');
    pdf.text('Notes:', 20, footerY);
    pdf.setFont('helvetica', 'normal');
    
    const noteLines = data.notes.split('\n');
    noteLines.forEach((line, index) => {
      pdf.text(line, 20, footerY + 7 + (index * 7));
    });
    footerY += 7 + (noteLines.length * 7) + 10;
  }

  // Footer
  const pageHeight = pdf.internal.pageSize.height;
  pdf.setFontSize(8);
  pdf.setTextColor(...grayColor);
  pdf.text('Thank you for your business!', 105, pageHeight - 30, { align: 'center' });
  pdf.text('ZestX Media - Professional Media Services', 105, pageHeight - 20, { align: 'center' });

  // Save the PDF
  const filename = `ZestX-Invoice-${data.invoiceNumber}.pdf`;
  pdf.save(filename);
};