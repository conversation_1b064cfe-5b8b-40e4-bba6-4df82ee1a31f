{"version": 3, "file": "pdfkit.esnext.js", "sources": ["../lib/abstract_reference.js", "../lib/tree.js", "../lib/object.js", "../lib/reference.js", "../lib/page.js", "../lib/name_tree.js", "../lib/saslprep/lib/util.js", "../lib/saslprep/lib/code-points.js", "../lib/saslprep/index.js", "../lib/security.js", "../lib/gradient.js", "../lib/pattern.js", "../lib/mixins/color.js", "../lib/path.js", "../lib/mixins/vector.js", "../lib/font/afm.js", "../lib/font.js", "../lib/font/standard.js", "../lib/font/embedded.js", "../lib/font_factory.js", "../lib/mixins/fonts.js", "../lib/line_wrapper.js", "../lib/mixins/text.js", "../lib/image/jpeg.js", "../lib/image/png.js", "../lib/image.js", "../lib/mixins/images.js", "../lib/mixins/annotations.js", "../lib/outline.js", "../lib/mixins/outline.js", "../lib/structure_content.js", "../lib/structure_element.js", "../lib/number_tree.js", "../lib/mixins/markings.js", "../lib/mixins/acroform.js", "../lib/mixins/attachments.js", "../lib/mixins/pdfa.js", "../lib/mixins/pdfua.js", "../lib/mixins/subsets.js", "../lib/metadata.js", "../lib/mixins/metadata.js", "../lib/document.js"], "sourcesContent": ["/*\r\nPDFAbstractReference - abstract class for PDF reference\r\n*/\r\n\r\nclass PDFAbstractReference {\r\n  toString() {\r\n    throw new Error('Must be implemented by subclasses');\r\n  }\r\n}\r\n\r\nexport default PDFAbstractReference;\r\n", "/*\r\nPDFTree - abstract base class for name and number tree objects\r\n*/\r\n\r\nimport PDFObject from './object';\r\n\r\nclass PDFTree {\r\n  constructor(options = {}) {\r\n    this._items = {};\r\n    // disable /Limits output for this tree\r\n    this.limits =\r\n      typeof options.limits === 'boolean' ? options.limits : true;\r\n  }\r\n\r\n  add(key, val) {\r\n    return (this._items[key] = val);\r\n  }\r\n\r\n  get(key) {\r\n    return this._items[key];\r\n  }\r\n\r\n  toString() {\r\n    // Needs to be sorted by key\r\n    const sortedKeys = Object.keys(this._items).sort((a, b) =>\r\n      this._compareKeys(a, b)\r\n    );\r\n\r\n    const out = ['<<'];\r\n    if (this.limits && sortedKeys.length > 1) {\r\n      const first = sortedKeys[0],\r\n        last = sortedKeys[sortedKeys.length - 1];\r\n      out.push(\r\n        `  /Limits ${PDFObject.convert([this._dataForKey(first), this._dataForKey(last)])}`\r\n      );\r\n    }\r\n    out.push(`  /${this._keysName()} [`);\r\n    for (let key of sortedKeys) {\r\n      out.push(\r\n        `    ${PDFObject.convert(this._dataFor<PERSON>ey(key))} ${PDFObject.convert(\r\n          this._items[key]\r\n        )}`\r\n      );\r\n    }\r\n    out.push(']');\r\n    out.push('>>');\r\n    return out.join('\\n');\r\n  }\r\n\r\n  _compareKeys(/*a, b*/) {\r\n    throw new Error('Must be implemented by subclasses');\r\n  }\r\n\r\n  _keysName() {\r\n    throw new Error('Must be implemented by subclasses');\r\n  }\r\n\r\n  _dataForKey(/*k*/) {\r\n    throw new Error('Must be implemented by subclasses');\r\n  }\r\n}\r\n\r\nexport default PDFTree;\r\n", "/*\r\nPDFObject - converts JavaScript types into their corresponding PDF types.\r\nBy <PERSON>\r\n*/\r\n\r\nimport PDFAbstractReference from './abstract_reference';\r\nimport PDFTree from './tree';\r\n\r\nconst pad = (str, length) => (Array(length + 1).join('0') + str).slice(-length);\r\n\r\nconst escapableRe = /[\\n\\r\\t\\b\\f()\\\\]/g;\r\nconst escapable = {\r\n  '\\n': '\\\\n',\r\n  '\\r': '\\\\r',\r\n  '\\t': '\\\\t',\r\n  '\\b': '\\\\b',\r\n  '\\f': '\\\\f',\r\n  '\\\\': '\\\\\\\\',\r\n  '(': '\\\\(',\r\n  ')': '\\\\)'\r\n};\r\n\r\n// Convert little endian UTF-16 to big endian\r\nconst swapBytes = function(buff) {\r\n  const l = buff.length;\r\n  if (l & 0x01) {\r\n    throw new Error('Buffer length must be even');\r\n  } else {\r\n    for (let i = 0, end = l - 1; i < end; i += 2) {\r\n      const a = buff[i];\r\n      buff[i] = buff[i + 1];\r\n      buff[i + 1] = a;\r\n    }\r\n  }\r\n\r\n  return buff;\r\n};\r\n\r\nclass PDFObject {\r\n  static convert(object, encryptFn = null) {\r\n    // String literals are converted to the PDF name type\r\n    if (typeof object === 'string') {\r\n      return `/${object}`;\r\n\r\n      // String objects are converted to PDF strings (UTF-16)\r\n    } else if (object instanceof String) {\r\n      let string = object;\r\n      // Detect if this is a unicode string\r\n      let isUnicode = false;\r\n      for (let i = 0, end = string.length; i < end; i++) {\r\n        if (string.charCodeAt(i) > 0x7f) {\r\n          isUnicode = true;\r\n          break;\r\n        }\r\n      }\r\n\r\n      // If so, encode it as big endian UTF-16\r\n      let stringBuffer;\r\n      if (isUnicode) {\r\n        stringBuffer = swapBytes(Buffer.from(`\\ufeff${string}`, 'utf16le'));\r\n      } else {\r\n        stringBuffer = Buffer.from(string.valueOf(), 'ascii');\r\n      }\r\n\r\n      // Encrypt the string when necessary\r\n      if (encryptFn) {\r\n        string = encryptFn(stringBuffer).toString('binary');\r\n      } else {\r\n        string = stringBuffer.toString('binary');\r\n      }\r\n\r\n      // Escape characters as required by the spec\r\n      string = string.replace(escapableRe, c => escapable[c]);\r\n\r\n      return `(${string})`;\r\n\r\n      // Buffers are converted to PDF hex strings\r\n    } else if (Buffer.isBuffer(object)) {\r\n      return `<${object.toString('hex')}>`;\r\n    } else if (\r\n      object instanceof PDFAbstractReference ||\r\n      object instanceof PDFTree\r\n    ) {\r\n      return object.toString();\r\n    } else if (object instanceof Date) {\r\n      let string =\r\n        `D:${pad(object.getUTCFullYear(), 4)}` +\r\n        pad(object.getUTCMonth() + 1, 2) +\r\n        pad(object.getUTCDate(), 2) +\r\n        pad(object.getUTCHours(), 2) +\r\n        pad(object.getUTCMinutes(), 2) +\r\n        pad(object.getUTCSeconds(), 2) +\r\n        'Z';\r\n\r\n      // Encrypt the string when necessary\r\n      if (encryptFn) {\r\n        string = encryptFn(Buffer.from(string, 'ascii')).toString('binary');\r\n\r\n        // Escape characters as required by the spec\r\n        string = string.replace(escapableRe, c => escapable[c]);\r\n      }\r\n\r\n      return `(${string})`;\r\n    } else if (Array.isArray(object)) {\r\n      const items = object.map(e => PDFObject.convert(e, encryptFn)).join(' ');\r\n      return `[${items}]`;\r\n    } else if ({}.toString.call(object) === '[object Object]') {\r\n      const out = ['<<'];\r\n      for (let key in object) {\r\n        const val = object[key];\r\n        out.push(`/${key} ${PDFObject.convert(val, encryptFn)}`);\r\n      }\r\n\r\n      out.push('>>');\r\n      return out.join('\\n');\r\n    } else if (typeof object === 'number') {\r\n      return PDFObject.number(object);\r\n    } else {\r\n      return `${object}`;\r\n    }\r\n  }\r\n\r\n  static number(n) {\r\n    if (n > -1e21 && n < 1e21) {\r\n      return Math.round(n * 1e6) / 1e6;\r\n    }\r\n\r\n    throw new Error(`unsupported number: ${n}`);\r\n  }\r\n}\r\n\r\nexport default PDFObject;\r\n", "/*\r\nPDFReference - represents a reference to another object in the PDF object heirarchy\r\nBy <PERSON>\r\n*/\r\n\r\nimport zlib from 'zlib';\r\nimport PDFAbstractReference from './abstract_reference';\r\nimport PDFObject from './object';\r\n\r\nclass PDFReference extends PDFAbstractReference {\r\n  constructor(document, id, data = {}) {\r\n    super();\r\n    this.document = document;\r\n    this.id = id;\r\n    this.data = data;\r\n    this.gen = 0;\r\n    this.compress = this.document.compress && !this.data.Filter;\r\n    this.uncompressedLength = 0;\r\n    this.buffer = [];\r\n  }\r\n\r\n  write(chunk) {\r\n    if (!Buffer.isBuffer(chunk)) {\r\n      chunk = Buffer.from(chunk + '\\n', 'binary');\r\n    }\r\n\r\n    this.uncompressedLength += chunk.length;\r\n    if (this.data.Length == null) {\r\n      this.data.Length = 0;\r\n    }\r\n    this.buffer.push(chunk);\r\n    this.data.Length += chunk.length;\r\n    if (this.compress) {\r\n      return (this.data.Filter = 'FlateDecode');\r\n    }\r\n  }\r\n\r\n  end(chunk) {\r\n    if (chunk) {\r\n      this.write(chunk);\r\n    }\r\n    return this.finalize();\r\n  }\r\n\r\n  finalize() {\r\n    this.offset = this.document._offset;\r\n\r\n    const encryptFn = this.document._security\r\n      ? this.document._security.getEncryptFn(this.id, this.gen)\r\n      : null;\r\n\r\n    if (this.buffer.length) {\r\n      this.buffer = Buffer.concat(this.buffer);\r\n      if (this.compress) {\r\n        this.buffer = zlib.deflateSync(this.buffer);\r\n      }\r\n\r\n      if (encryptFn) {\r\n        this.buffer = encryptFn(this.buffer);\r\n      }\r\n\r\n      this.data.Length = this.buffer.length;\r\n    }\r\n\r\n    this.document._write(`${this.id} ${this.gen} obj`);\r\n    this.document._write(PDFObject.convert(this.data, encryptFn));\r\n\r\n    if (this.buffer.length) {\r\n      this.document._write('stream');\r\n      this.document._write(this.buffer);\r\n\r\n      this.buffer = []; // free up memory\r\n      this.document._write('\\nendstream');\r\n    }\r\n\r\n    this.document._write('endobj');\r\n    this.document._refEnd(this);\r\n  }\r\n  toString() {\r\n    return `${this.id} ${this.gen} R`;\r\n  }\r\n}\r\n\r\nexport default PDFReference;\r\n", "/*\r\nPDFPage - represents a single page in the PDF document\r\nBy <PERSON>\r\n*/\r\n\r\nconst DEFAULT_MARGINS = {\r\n  top: 72,\r\n  left: 72,\r\n  bottom: 72,\r\n  right: 72\r\n};\r\n\r\nconst SIZES = {\r\n  '4A0': [4767.87, 6740.79],\r\n  '2A0': [3370.39, 4767.87],\r\n  A0: [2383.94, 3370.39],\r\n  A1: [1683.78, 2383.94],\r\n  A2: [1190.55, 1683.78],\r\n  A3: [841.89, 1190.55],\r\n  A4: [595.28, 841.89],\r\n  A5: [419.53, 595.28],\r\n  A6: [297.64, 419.53],\r\n  A7: [209.76, 297.64],\r\n  A8: [147.4, 209.76],\r\n  A9: [104.88, 147.4],\r\n  A10: [73.7, 104.88],\r\n  B0: [2834.65, 4008.19],\r\n  B1: [2004.09, 2834.65],\r\n  B2: [1417.32, 2004.09],\r\n  B3: [1000.63, 1417.32],\r\n  B4: [708.66, 1000.63],\r\n  B5: [498.9, 708.66],\r\n  B6: [354.33, 498.9],\r\n  B7: [249.45, 354.33],\r\n  B8: [175.75, 249.45],\r\n  B9: [124.72, 175.75],\r\n  B10: [87.87, 124.72],\r\n  C0: [2599.37, 3676.54],\r\n  C1: [1836.85, 2599.37],\r\n  C2: [1298.27, 1836.85],\r\n  C3: [918.43, 1298.27],\r\n  C4: [649.13, 918.43],\r\n  C5: [459.21, 649.13],\r\n  C6: [323.15, 459.21],\r\n  C7: [229.61, 323.15],\r\n  C8: [161.57, 229.61],\r\n  C9: [113.39, 161.57],\r\n  C10: [79.37, 113.39],\r\n  RA0: [2437.8, 3458.27],\r\n  RA1: [1729.13, 2437.8],\r\n  RA2: [1218.9, 1729.13],\r\n  RA3: [864.57, 1218.9],\r\n  RA4: [609.45, 864.57],\r\n  SRA0: [2551.18, 3628.35],\r\n  SRA1: [1814.17, 2551.18],\r\n  SRA2: [1275.59, 1814.17],\r\n  SRA3: [907.09, 1275.59],\r\n  SRA4: [637.8, 907.09],\r\n  EXECUTIVE: [521.86, 756.0],\r\n  FOLIO: [612.0, 936.0],\r\n  LEGAL: [612.0, 1008.0],\r\n  LETTER: [612.0, 792.0],\r\n  TABLOID: [792.0, 1224.0]\r\n};\r\n\r\nclass PDFPage {\r\n  constructor(document, options = {}) {\r\n    this.document = document;\r\n    this.size = options.size || 'letter';\r\n    this.layout = options.layout || 'portrait';\r\n\r\n    // process margins\r\n    if (typeof options.margin === 'number') {\r\n      this.margins = {\r\n        top: options.margin,\r\n        left: options.margin,\r\n        bottom: options.margin,\r\n        right: options.margin\r\n      };\r\n\r\n      // default to 1 inch margins\r\n    } else {\r\n      this.margins = options.margins || DEFAULT_MARGINS;\r\n    }\r\n\r\n    // calculate page dimensions\r\n    const dimensions = Array.isArray(this.size)\r\n      ? this.size\r\n      : SIZES[this.size.toUpperCase()];\r\n    this.width = dimensions[this.layout === 'portrait' ? 0 : 1];\r\n    this.height = dimensions[this.layout === 'portrait' ? 1 : 0];\r\n\r\n    this.content = this.document.ref();\r\n\r\n    // Initialize the Font, XObject, and ExtGState dictionaries\r\n    this.resources = this.document.ref({\r\n      ProcSet: ['PDF', 'Text', 'ImageB', 'ImageC', 'ImageI']\r\n    });\r\n\r\n    // The page dictionary\r\n    this.dictionary = this.document.ref({\r\n      Type: 'Page',\r\n      Parent: this.document._root.data.Pages,\r\n      MediaBox: [0, 0, this.width, this.height],\r\n      Contents: this.content,\r\n      Resources: this.resources\r\n    });\r\n\r\n    this.markings = [];\r\n  }\r\n\r\n  // Lazily create these objects\r\n  get fonts() {\r\n    const data = this.resources.data;\r\n    return data.Font != null ? data.Font : (data.Font = {});\r\n  }\r\n\r\n  get xobjects() {\r\n    const data = this.resources.data;\r\n    return data.XObject != null ? data.XObject : (data.XObject = {});\r\n  }\r\n\r\n  get ext_gstates() {\r\n    const data = this.resources.data;\r\n    return data.ExtGState != null ? data.ExtGState : (data.ExtGState = {});\r\n  }\r\n\r\n  get patterns() {\r\n    const data = this.resources.data;\r\n    return data.Pattern != null ? data.Pattern : (data.Pattern = {});\r\n  }\r\n\r\n  get colorSpaces() {\r\n    const data = this.resources.data;\r\n    return data.ColorSpace || (data.ColorSpace = {});\r\n  }\r\n\r\n  get annotations() {\r\n    const data = this.dictionary.data;\r\n    return data.Annots != null ? data.Annots : (data.Annots = []);\r\n  }\r\n\r\n  get structParentTreeKey() {\r\n    const data = this.dictionary.data;\r\n    return data.StructParents != null\r\n      ? data.StructParents\r\n      : (data.StructParents = this.document.createStructParentTreeNextKey());\r\n  }\r\n\r\n  maxY() {\r\n    return this.height - this.margins.bottom;\r\n  }\r\n\r\n  write(chunk) {\r\n    return this.content.write(chunk);\r\n  }\r\n\r\n  end() {\r\n    this.dictionary.end();\r\n    this.resources.end();\r\n    return this.content.end();\r\n  }\r\n}\r\n\r\nexport default PDFPage;\r\n", "/*\r\nPDFNameTree - represents a name tree object\r\n*/\r\n\r\nimport PDFTree from \"./tree\";\r\n\r\nclass PDFNameTree extends PDFTree {\r\n  _compareKeys(a, b) {\r\n    return a.localeCompare(b);\r\n  }\r\n\r\n  _keysName() {\r\n    return \"Names\";\r\n  }\r\n\r\n  _dataForKey(k) {\r\n    return new String(k);\r\n  }\r\n}\r\n\r\nexport default PDFNameTree;\r\n", "/**\r\n * Check if value is in a range group.\r\n * @param {number} value\r\n * @param {number[]} rangeGroup\r\n * @returns {boolean}\r\n */\r\nfunction inRange(value, rangeGroup) {\r\n  if (value < rangeGroup[0]) return false;\r\n  let startRange = 0;\r\n  let endRange = rangeGroup.length / 2;\r\n  while (startRange <= endRange) {\r\n    const middleRange = Math.floor((startRange + endRange) / 2);\r\n\r\n    // actual array index\r\n    const arrayIndex = middleRange * 2;\r\n\r\n    // Check if value is in range pointed by actual index\r\n    if (\r\n      value >= rangeGroup[arrayIndex] &&\r\n      value <= rangeGroup[arrayIndex + 1]\r\n    ) {\r\n      return true;\r\n    }\r\n\r\n    if (value > rangeGroup[arrayIndex + 1]) {\r\n      // Search Right Side Of Array\r\n      startRange = middleRange + 1;\r\n    } else {\r\n      // Search Left Side Of Array\r\n      endRange = middleRange - 1;\r\n    }\r\n  }\r\n  return false;\r\n}\r\n\r\nexport { inRange };\r\n", "import { inRange } from './util';\r\n\r\n// prettier-ignore-start\r\n/**\r\n * A.1 Unassigned code points in Unicode 3.2\r\n * @link https://tools.ietf.org/html/rfc3454#appendix-A.1\r\n */\r\nconst unassigned_code_points = [\r\n  0x0221,\r\n  0x0221,\r\n  0x0234,\r\n  0x024f,\r\n  0x02ae,\r\n  0x02af,\r\n  0x02ef,\r\n  0x02ff,\r\n  0x0350,\r\n  0x035f,\r\n  0x0370,\r\n  0x0373,\r\n  0x0376,\r\n  0x0379,\r\n  0x037b,\r\n  0x037d,\r\n  0x037f,\r\n  0x0383,\r\n  0x038b,\r\n  0x038b,\r\n  0x038d,\r\n  0x038d,\r\n  0x03a2,\r\n  0x03a2,\r\n  0x03cf,\r\n  0x03cf,\r\n  0x03f7,\r\n  0x03ff,\r\n  0x0487,\r\n  0x0487,\r\n  0x04cf,\r\n  0x04cf,\r\n  0x04f6,\r\n  0x04f7,\r\n  0x04fa,\r\n  0x04ff,\r\n  0x0510,\r\n  0x0530,\r\n  0x0557,\r\n  0x0558,\r\n  0x0560,\r\n  0x0560,\r\n  0x0588,\r\n  0x0588,\r\n  0x058b,\r\n  0x0590,\r\n  0x05a2,\r\n  0x05a2,\r\n  0x05ba,\r\n  0x05ba,\r\n  0x05c5,\r\n  0x05cf,\r\n  0x05eb,\r\n  0x05ef,\r\n  0x05f5,\r\n  0x060b,\r\n  0x060d,\r\n  0x061a,\r\n  0x061c,\r\n  0x061e,\r\n  0x0620,\r\n  0x0620,\r\n  0x063b,\r\n  0x063f,\r\n  0x0656,\r\n  0x065f,\r\n  0x06ee,\r\n  0x06ef,\r\n  0x06ff,\r\n  0x06ff,\r\n  0x070e,\r\n  0x070e,\r\n  0x072d,\r\n  0x072f,\r\n  0x074b,\r\n  0x077f,\r\n  0x07b2,\r\n  0x0900,\r\n  0x0904,\r\n  0x0904,\r\n  0x093a,\r\n  0x093b,\r\n  0x094e,\r\n  0x094f,\r\n  0x0955,\r\n  0x0957,\r\n  0x0971,\r\n  0x0980,\r\n  0x0984,\r\n  0x0984,\r\n  0x098d,\r\n  0x098e,\r\n  0x0991,\r\n  0x0992,\r\n  0x09a9,\r\n  0x09a9,\r\n  0x09b1,\r\n  0x09b1,\r\n  0x09b3,\r\n  0x09b5,\r\n  0x09ba,\r\n  0x09bb,\r\n  0x09bd,\r\n  0x09bd,\r\n  0x09c5,\r\n  0x09c6,\r\n  0x09c9,\r\n  0x09ca,\r\n  0x09ce,\r\n  0x09d6,\r\n  0x09d8,\r\n  0x09db,\r\n  0x09de,\r\n  0x09de,\r\n  0x09e4,\r\n  0x09e5,\r\n  0x09fb,\r\n  0x0a01,\r\n  0x0a03,\r\n  0x0a04,\r\n  0x0a0b,\r\n  0x0a0e,\r\n  0x0a11,\r\n  0x0a12,\r\n  0x0a29,\r\n  0x0a29,\r\n  0x0a31,\r\n  0x0a31,\r\n  0x0a34,\r\n  0x0a34,\r\n  0x0a37,\r\n  0x0a37,\r\n  0x0a3a,\r\n  0x0a3b,\r\n  0x0a3d,\r\n  0x0a3d,\r\n  0x0a43,\r\n  0x0a46,\r\n  0x0a49,\r\n  0x0a4a,\r\n  0x0a4e,\r\n  0x0a58,\r\n  0x0a5d,\r\n  0x0a5d,\r\n  0x0a5f,\r\n  0x0a65,\r\n  0x0a75,\r\n  0x0a80,\r\n  0x0a84,\r\n  0x0a84,\r\n  0x0a8c,\r\n  0x0a8c,\r\n  0x0a8e,\r\n  0x0a8e,\r\n  0x0a92,\r\n  0x0a92,\r\n  0x0aa9,\r\n  0x0aa9,\r\n  0x0ab1,\r\n  0x0ab1,\r\n  0x0ab4,\r\n  0x0ab4,\r\n  0x0aba,\r\n  0x0abb,\r\n  0x0ac6,\r\n  0x0ac6,\r\n  0x0aca,\r\n  0x0aca,\r\n  0x0ace,\r\n  0x0acf,\r\n  0x0ad1,\r\n  0x0adf,\r\n  0x0ae1,\r\n  0x0ae5,\r\n  0x0af0,\r\n  0x0b00,\r\n  0x0b04,\r\n  0x0b04,\r\n  0x0b0d,\r\n  0x0b0e,\r\n  0x0b11,\r\n  0x0b12,\r\n  0x0b29,\r\n  0x0b29,\r\n  0x0b31,\r\n  0x0b31,\r\n  0x0b34,\r\n  0x0b35,\r\n  0x0b3a,\r\n  0x0b3b,\r\n  0x0b44,\r\n  0x0b46,\r\n  0x0b49,\r\n  0x0b4a,\r\n  0x0b4e,\r\n  0x0b55,\r\n  0x0b58,\r\n  0x0b5b,\r\n  0x0b5e,\r\n  0x0b5e,\r\n  0x0b62,\r\n  0x0b65,\r\n  0x0b71,\r\n  0x0b81,\r\n  0x0b84,\r\n  0x0b84,\r\n  0x0b8b,\r\n  0x0b8d,\r\n  0x0b91,\r\n  0x0b91,\r\n  0x0b96,\r\n  0x0b98,\r\n  0x0b9b,\r\n  0x0b9b,\r\n  0x0b9d,\r\n  0x0b9d,\r\n  0x0ba0,\r\n  0x0ba2,\r\n  0x0ba5,\r\n  0x0ba7,\r\n  0x0bab,\r\n  0x0bad,\r\n  0x0bb6,\r\n  0x0bb6,\r\n  0x0bba,\r\n  0x0bbd,\r\n  0x0bc3,\r\n  0x0bc5,\r\n  0x0bc9,\r\n  0x0bc9,\r\n  0x0bce,\r\n  0x0bd6,\r\n  0x0bd8,\r\n  0x0be6,\r\n  0x0bf3,\r\n  0x0c00,\r\n  0x0c04,\r\n  0x0c04,\r\n  0x0c0d,\r\n  0x0c0d,\r\n  0x0c11,\r\n  0x0c11,\r\n  0x0c29,\r\n  0x0c29,\r\n  0x0c34,\r\n  0x0c34,\r\n  0x0c3a,\r\n  0x0c3d,\r\n  0x0c45,\r\n  0x0c45,\r\n  0x0c49,\r\n  0x0c49,\r\n  0x0c4e,\r\n  0x0c54,\r\n  0x0c57,\r\n  0x0c5f,\r\n  0x0c62,\r\n  0x0c65,\r\n  0x0c70,\r\n  0x0c81,\r\n  0x0c84,\r\n  0x0c84,\r\n  0x0c8d,\r\n  0x0c8d,\r\n  0x0c91,\r\n  0x0c91,\r\n  0x0ca9,\r\n  0x0ca9,\r\n  0x0cb4,\r\n  0x0cb4,\r\n  0x0cba,\r\n  0x0cbd,\r\n  0x0cc5,\r\n  0x0cc5,\r\n  0x0cc9,\r\n  0x0cc9,\r\n  0x0cce,\r\n  0x0cd4,\r\n  0x0cd7,\r\n  0x0cdd,\r\n  0x0cdf,\r\n  0x0cdf,\r\n  0x0ce2,\r\n  0x0ce5,\r\n  0x0cf0,\r\n  0x0d01,\r\n  0x0d04,\r\n  0x0d04,\r\n  0x0d0d,\r\n  0x0d0d,\r\n  0x0d11,\r\n  0x0d11,\r\n  0x0d29,\r\n  0x0d29,\r\n  0x0d3a,\r\n  0x0d3d,\r\n  0x0d44,\r\n  0x0d45,\r\n  0x0d49,\r\n  0x0d49,\r\n  0x0d4e,\r\n  0x0d56,\r\n  0x0d58,\r\n  0x0d5f,\r\n  0x0d62,\r\n  0x0d65,\r\n  0x0d70,\r\n  0x0d81,\r\n  0x0d84,\r\n  0x0d84,\r\n  0x0d97,\r\n  0x0d99,\r\n  0x0db2,\r\n  0x0db2,\r\n  0x0dbc,\r\n  0x0dbc,\r\n  0x0dbe,\r\n  0x0dbf,\r\n  0x0dc7,\r\n  0x0dc9,\r\n  0x0dcb,\r\n  0x0dce,\r\n  0x0dd5,\r\n  0x0dd5,\r\n  0x0dd7,\r\n  0x0dd7,\r\n  0x0de0,\r\n  0x0df1,\r\n  0x0df5,\r\n  0x0e00,\r\n  0x0e3b,\r\n  0x0e3e,\r\n  0x0e5c,\r\n  0x0e80,\r\n  0x0e83,\r\n  0x0e83,\r\n  0x0e85,\r\n  0x0e86,\r\n  0x0e89,\r\n  0x0e89,\r\n  0x0e8b,\r\n  0x0e8c,\r\n  0x0e8e,\r\n  0x0e93,\r\n  0x0e98,\r\n  0x0e98,\r\n  0x0ea0,\r\n  0x0ea0,\r\n  0x0ea4,\r\n  0x0ea4,\r\n  0x0ea6,\r\n  0x0ea6,\r\n  0x0ea8,\r\n  0x0ea9,\r\n  0x0eac,\r\n  0x0eac,\r\n  0x0eba,\r\n  0x0eba,\r\n  0x0ebe,\r\n  0x0ebf,\r\n  0x0ec5,\r\n  0x0ec5,\r\n  0x0ec7,\r\n  0x0ec7,\r\n  0x0ece,\r\n  0x0ecf,\r\n  0x0eda,\r\n  0x0edb,\r\n  0x0ede,\r\n  0x0eff,\r\n  0x0f48,\r\n  0x0f48,\r\n  0x0f6b,\r\n  0x0f70,\r\n  0x0f8c,\r\n  0x0f8f,\r\n  0x0f98,\r\n  0x0f98,\r\n  0x0fbd,\r\n  0x0fbd,\r\n  0x0fcd,\r\n  0x0fce,\r\n  0x0fd0,\r\n  0x0fff,\r\n  0x1022,\r\n  0x1022,\r\n  0x1028,\r\n  0x1028,\r\n  0x102b,\r\n  0x102b,\r\n  0x1033,\r\n  0x1035,\r\n  0x103a,\r\n  0x103f,\r\n  0x105a,\r\n  0x109f,\r\n  0x10c6,\r\n  0x10cf,\r\n  0x10f9,\r\n  0x10fa,\r\n  0x10fc,\r\n  0x10ff,\r\n  0x115a,\r\n  0x115e,\r\n  0x11a3,\r\n  0x11a7,\r\n  0x11fa,\r\n  0x11ff,\r\n  0x1207,\r\n  0x1207,\r\n  0x1247,\r\n  0x1247,\r\n  0x1249,\r\n  0x1249,\r\n  0x124e,\r\n  0x124f,\r\n  0x1257,\r\n  0x1257,\r\n  0x1259,\r\n  0x1259,\r\n  0x125e,\r\n  0x125f,\r\n  0x1287,\r\n  0x1287,\r\n  0x1289,\r\n  0x1289,\r\n  0x128e,\r\n  0x128f,\r\n  0x12af,\r\n  0x12af,\r\n  0x12b1,\r\n  0x12b1,\r\n  0x12b6,\r\n  0x12b7,\r\n  0x12bf,\r\n  0x12bf,\r\n  0x12c1,\r\n  0x12c1,\r\n  0x12c6,\r\n  0x12c7,\r\n  0x12cf,\r\n  0x12cf,\r\n  0x12d7,\r\n  0x12d7,\r\n  0x12ef,\r\n  0x12ef,\r\n  0x130f,\r\n  0x130f,\r\n  0x1311,\r\n  0x1311,\r\n  0x1316,\r\n  0x1317,\r\n  0x131f,\r\n  0x131f,\r\n  0x1347,\r\n  0x1347,\r\n  0x135b,\r\n  0x1360,\r\n  0x137d,\r\n  0x139f,\r\n  0x13f5,\r\n  0x1400,\r\n  0x1677,\r\n  0x167f,\r\n  0x169d,\r\n  0x169f,\r\n  0x16f1,\r\n  0x16ff,\r\n  0x170d,\r\n  0x170d,\r\n  0x1715,\r\n  0x171f,\r\n  0x1737,\r\n  0x173f,\r\n  0x1754,\r\n  0x175f,\r\n  0x176d,\r\n  0x176d,\r\n  0x1771,\r\n  0x1771,\r\n  0x1774,\r\n  0x177f,\r\n  0x17dd,\r\n  0x17df,\r\n  0x17ea,\r\n  0x17ff,\r\n  0x180f,\r\n  0x180f,\r\n  0x181a,\r\n  0x181f,\r\n  0x1878,\r\n  0x187f,\r\n  0x18aa,\r\n  0x1dff,\r\n  0x1e9c,\r\n  0x1e9f,\r\n  0x1efa,\r\n  0x1eff,\r\n  0x1f16,\r\n  0x1f17,\r\n  0x1f1e,\r\n  0x1f1f,\r\n  0x1f46,\r\n  0x1f47,\r\n  0x1f4e,\r\n  0x1f4f,\r\n  0x1f58,\r\n  0x1f58,\r\n  0x1f5a,\r\n  0x1f5a,\r\n  0x1f5c,\r\n  0x1f5c,\r\n  0x1f5e,\r\n  0x1f5e,\r\n  0x1f7e,\r\n  0x1f7f,\r\n  0x1fb5,\r\n  0x1fb5,\r\n  0x1fc5,\r\n  0x1fc5,\r\n  0x1fd4,\r\n  0x1fd5,\r\n  0x1fdc,\r\n  0x1fdc,\r\n  0x1ff0,\r\n  0x1ff1,\r\n  0x1ff5,\r\n  0x1ff5,\r\n  0x1fff,\r\n  0x1fff,\r\n  0x2053,\r\n  0x2056,\r\n  0x2058,\r\n  0x205e,\r\n  0x2064,\r\n  0x2069,\r\n  0x2072,\r\n  0x2073,\r\n  0x208f,\r\n  0x209f,\r\n  0x20b2,\r\n  0x20cf,\r\n  0x20eb,\r\n  0x20ff,\r\n  0x213b,\r\n  0x213c,\r\n  0x214c,\r\n  0x2152,\r\n  0x2184,\r\n  0x218f,\r\n  0x23cf,\r\n  0x23ff,\r\n  0x2427,\r\n  0x243f,\r\n  0x244b,\r\n  0x245f,\r\n  0x24ff,\r\n  0x24ff,\r\n  0x2614,\r\n  0x2615,\r\n  0x2618,\r\n  0x2618,\r\n  0x267e,\r\n  0x267f,\r\n  0x268a,\r\n  0x2700,\r\n  0x2705,\r\n  0x2705,\r\n  0x270a,\r\n  0x270b,\r\n  0x2728,\r\n  0x2728,\r\n  0x274c,\r\n  0x274c,\r\n  0x274e,\r\n  0x274e,\r\n  0x2753,\r\n  0x2755,\r\n  0x2757,\r\n  0x2757,\r\n  0x275f,\r\n  0x2760,\r\n  0x2795,\r\n  0x2797,\r\n  0x27b0,\r\n  0x27b0,\r\n  0x27bf,\r\n  0x27cf,\r\n  0x27ec,\r\n  0x27ef,\r\n  0x2b00,\r\n  0x2e7f,\r\n  0x2e9a,\r\n  0x2e9a,\r\n  0x2ef4,\r\n  0x2eff,\r\n  0x2fd6,\r\n  0x2fef,\r\n  0x2ffc,\r\n  0x2fff,\r\n  0x3040,\r\n  0x3040,\r\n  0x3097,\r\n  0x3098,\r\n  0x3100,\r\n  0x3104,\r\n  0x312d,\r\n  0x3130,\r\n  0x318f,\r\n  0x318f,\r\n  0x31b8,\r\n  0x31ef,\r\n  0x321d,\r\n  0x321f,\r\n  0x3244,\r\n  0x3250,\r\n  0x327c,\r\n  0x327e,\r\n  0x32cc,\r\n  0x32cf,\r\n  0x32ff,\r\n  0x32ff,\r\n  0x3377,\r\n  0x337a,\r\n  0x33de,\r\n  0x33df,\r\n  0x33ff,\r\n  0x33ff,\r\n  0x4db6,\r\n  0x4dff,\r\n  0x9fa6,\r\n  0x9fff,\r\n  0xa48d,\r\n  0xa48f,\r\n  0xa4c7,\r\n  0xabff,\r\n  0xd7a4,\r\n  0xd7ff,\r\n  0xfa2e,\r\n  0xfa2f,\r\n  0xfa6b,\r\n  0xfaff,\r\n  0xfb07,\r\n  0xfb12,\r\n  0xfb18,\r\n  0xfb1c,\r\n  0xfb37,\r\n  0xfb37,\r\n  0xfb3d,\r\n  0xfb3d,\r\n  0xfb3f,\r\n  0xfb3f,\r\n  0xfb42,\r\n  0xfb42,\r\n  0xfb45,\r\n  0xfb45,\r\n  0xfbb2,\r\n  0xfbd2,\r\n  0xfd40,\r\n  0xfd4f,\r\n  0xfd90,\r\n  0xfd91,\r\n  0xfdc8,\r\n  0xfdcf,\r\n  0xfdfd,\r\n  0xfdff,\r\n  0xfe10,\r\n  0xfe1f,\r\n  0xfe24,\r\n  0xfe2f,\r\n  0xfe47,\r\n  0xfe48,\r\n  0xfe53,\r\n  0xfe53,\r\n  0xfe67,\r\n  0xfe67,\r\n  0xfe6c,\r\n  0xfe6f,\r\n  0xfe75,\r\n  0xfe75,\r\n  0xfefd,\r\n  0xfefe,\r\n  0xff00,\r\n  0xff00,\r\n  0xffbf,\r\n  0xffc1,\r\n  0xffc8,\r\n  0xffc9,\r\n  0xffd0,\r\n  0xffd1,\r\n  0xffd8,\r\n  0xffd9,\r\n  0xffdd,\r\n  0xffdf,\r\n  0xffe7,\r\n  0xffe7,\r\n  0xffef,\r\n  0xfff8,\r\n  0x10000,\r\n  0x102ff,\r\n  0x1031f,\r\n  0x1031f,\r\n  0x10324,\r\n  0x1032f,\r\n  0x1034b,\r\n  0x103ff,\r\n  0x10426,\r\n  0x10427,\r\n  0x1044e,\r\n  0x1cfff,\r\n  0x1d0f6,\r\n  0x1d0ff,\r\n  0x1d127,\r\n  0x1d129,\r\n  0x1d1de,\r\n  0x1d3ff,\r\n  0x1d455,\r\n  0x1d455,\r\n  0x1d49d,\r\n  0x1d49d,\r\n  0x1d4a0,\r\n  0x1d4a1,\r\n  0x1d4a3,\r\n  0x1d4a4,\r\n  0x1d4a7,\r\n  0x1d4a8,\r\n  0x1d4ad,\r\n  0x1d4ad,\r\n  0x1d4ba,\r\n  0x1d4ba,\r\n  0x1d4bc,\r\n  0x1d4bc,\r\n  0x1d4c1,\r\n  0x1d4c1,\r\n  0x1d4c4,\r\n  0x1d4c4,\r\n  0x1d506,\r\n  0x1d506,\r\n  0x1d50b,\r\n  0x1d50c,\r\n  0x1d515,\r\n  0x1d515,\r\n  0x1d51d,\r\n  0x1d51d,\r\n  0x1d53a,\r\n  0x1d53a,\r\n  0x1d53f,\r\n  0x1d53f,\r\n  0x1d545,\r\n  0x1d545,\r\n  0x1d547,\r\n  0x1d549,\r\n  0x1d551,\r\n  0x1d551,\r\n  0x1d6a4,\r\n  0x1d6a7,\r\n  0x1d7ca,\r\n  0x1d7cd,\r\n  0x1d800,\r\n  0x1fffd,\r\n  0x2a6d7,\r\n  0x2f7ff,\r\n  0x2fa1e,\r\n  0x2fffd,\r\n  0x30000,\r\n  0x3fffd,\r\n  0x40000,\r\n  0x4fffd,\r\n  0x50000,\r\n  0x5fffd,\r\n  0x60000,\r\n  0x6fffd,\r\n  0x70000,\r\n  0x7fffd,\r\n  0x80000,\r\n  0x8fffd,\r\n  0x90000,\r\n  0x9fffd,\r\n  0xa0000,\r\n  0xafffd,\r\n  0xb0000,\r\n  0xbfffd,\r\n  0xc0000,\r\n  0xcfffd,\r\n  0xd0000,\r\n  0xdfffd,\r\n  0xe0000,\r\n  0xe0000,\r\n  0xe0002,\r\n  0xe001f,\r\n  0xe0080,\r\n  0xefffd\r\n];\r\n// prettier-ignore-end\r\n\r\nconst isUnassignedCodePoint = character =>\r\n  inRange(character, unassigned_code_points);\r\n\r\n// prettier-ignore-start\r\n/**\r\n * B.1 Commonly mapped to nothing\r\n * @link https://tools.ietf.org/html/rfc3454#appendix-B.1\r\n */\r\nconst commonly_mapped_to_nothing = [\r\n  0x00ad,\r\n  0x00ad,\r\n  0x034f,\r\n  0x034f,\r\n  0x1806,\r\n  0x1806,\r\n  0x180b,\r\n  0x180b,\r\n  0x180c,\r\n  0x180c,\r\n  0x180d,\r\n  0x180d,\r\n  0x200b,\r\n  0x200b,\r\n  0x200c,\r\n  0x200c,\r\n  0x200d,\r\n  0x200d,\r\n  0x2060,\r\n  0x2060,\r\n  0xfe00,\r\n  0xfe00,\r\n  0xfe01,\r\n  0xfe01,\r\n  0xfe02,\r\n  0xfe02,\r\n  0xfe03,\r\n  0xfe03,\r\n  0xfe04,\r\n  0xfe04,\r\n  0xfe05,\r\n  0xfe05,\r\n  0xfe06,\r\n  0xfe06,\r\n  0xfe07,\r\n  0xfe07,\r\n  0xfe08,\r\n  0xfe08,\r\n  0xfe09,\r\n  0xfe09,\r\n  0xfe0a,\r\n  0xfe0a,\r\n  0xfe0b,\r\n  0xfe0b,\r\n  0xfe0c,\r\n  0xfe0c,\r\n  0xfe0d,\r\n  0xfe0d,\r\n  0xfe0e,\r\n  0xfe0e,\r\n  0xfe0f,\r\n  0xfe0f,\r\n  0xfeff,\r\n  0xfeff\r\n];\r\n// prettier-ignore-end\r\n\r\nconst isCommonlyMappedToNothing = character =>\r\n  inRange(character, commonly_mapped_to_nothing);\r\n\r\n// prettier-ignore-start\r\n/**\r\n * C.1.2 Non-ASCII space characters\r\n * @link https://tools.ietf.org/html/rfc3454#appendix-C.1.2\r\n */\r\nconst non_ASCII_space_characters = [\r\n  0x00a0,\r\n  0x00a0 /* NO-BREAK SPACE */,\r\n  0x1680,\r\n  0x1680 /* OGHAM SPACE MARK */,\r\n  0x2000,\r\n  0x2000 /* EN QUAD */,\r\n  0x2001,\r\n  0x2001 /* EM QUAD */,\r\n  0x2002,\r\n  0x2002 /* EN SPACE */,\r\n  0x2003,\r\n  0x2003 /* EM SPACE */,\r\n  0x2004,\r\n  0x2004 /* THREE-PER-EM SPACE */,\r\n  0x2005,\r\n  0x2005 /* FOUR-PER-EM SPACE */,\r\n  0x2006,\r\n  0x2006 /* SIX-PER-EM SPACE */,\r\n  0x2007,\r\n  0x2007 /* FIGURE SPACE */,\r\n  0x2008,\r\n  0x2008 /* PUNCTUATION SPACE */,\r\n  0x2009,\r\n  0x2009 /* THIN SPACE */,\r\n  0x200a,\r\n  0x200a /* HAIR SPACE */,\r\n  0x200b,\r\n  0x200b /* ZERO WIDTH SPACE */,\r\n  0x202f,\r\n  0x202f /* NARROW NO-BREAK SPACE */,\r\n  0x205f,\r\n  0x205f /* MEDIUM MATHEMATICAL SPACE */,\r\n  0x3000,\r\n  0x3000 /* IDEOGRAPHIC SPACE */\r\n];\r\n// prettier-ignore-end\r\n\r\nconst isNonASCIISpaceCharacter = character =>\r\n  inRange(character, non_ASCII_space_characters);\r\n\r\n// prettier-ignore-start\r\nconst non_ASCII_controls_characters = [\r\n  /**\r\n   * C.2.2 Non-ASCII control characters\r\n   * @link https://tools.ietf.org/html/rfc3454#appendix-C.2.2\r\n   */\r\n  0x0080,\r\n  0x009f /* [CONTROL CHARACTERS] */,\r\n  0x06dd,\r\n  0x06dd /* ARABIC END OF AYAH */,\r\n  0x070f,\r\n  0x070f /* SYRIAC ABBREVIATION MARK */,\r\n  0x180e,\r\n  0x180e /* MONGOLIAN VOWEL SEPARATOR */,\r\n  0x200c,\r\n  0x200c /* ZERO WIDTH NON-JOINER */,\r\n  0x200d,\r\n  0x200d /* ZERO WIDTH JOINER */,\r\n  0x2028,\r\n  0x2028 /* LINE SEPARATOR */,\r\n  0x2029,\r\n  0x2029 /* PARAGRAPH SEPARATOR */,\r\n  0x2060,\r\n  0x2060 /* WORD JOINER */,\r\n  0x2061,\r\n  0x2061 /* FUNCTION APPLICATION */,\r\n  0x2062,\r\n  0x2062 /* INVISIBLE TIMES */,\r\n  0x2063,\r\n  0x2063 /* INVISIBLE SEPARATOR */,\r\n  0x206a,\r\n  0x206f /* [CONTROL CHARACTERS] */,\r\n  0xfeff,\r\n  0xfeff /* ZERO WIDTH NO-BREAK SPACE */,\r\n  0xfff9,\r\n  0xfffc /* [CONTROL CHARACTERS] */,\r\n  0x1d173,\r\n  0x1d17a /* [MUSICAL CONTROL CHARACTERS] */\r\n];\r\n\r\nconst non_character_codepoints = [\r\n  /**\r\n   * C.4 Non-character code points\r\n   * @link https://tools.ietf.org/html/rfc3454#appendix-C.4\r\n   */\r\n  0xfdd0,\r\n  0xfdef /* [NONCHARACTER CODE POINTS] */,\r\n  0xfffe,\r\n  0xffff /* [NONCHARACTER CODE POINTS] */,\r\n  0x1fffe,\r\n  0x1ffff /* [NONCHARACTER CODE POINTS] */,\r\n  0x2fffe,\r\n  0x2ffff /* [NONCHARACTER CODE POINTS] */,\r\n  0x3fffe,\r\n  0x3ffff /* [NONCHARACTER CODE POINTS] */,\r\n  0x4fffe,\r\n  0x4ffff /* [NONCHARACTER CODE POINTS] */,\r\n  0x5fffe,\r\n  0x5ffff /* [NONCHARACTER CODE POINTS] */,\r\n  0x6fffe,\r\n  0x6ffff /* [NONCHARACTER CODE POINTS] */,\r\n  0x7fffe,\r\n  0x7ffff /* [NONCHARACTER CODE POINTS] */,\r\n  0x8fffe,\r\n  0x8ffff /* [NONCHARACTER CODE POINTS] */,\r\n  0x9fffe,\r\n  0x9ffff /* [NONCHARACTER CODE POINTS] */,\r\n  0xafffe,\r\n  0xaffff /* [NONCHARACTER CODE POINTS] */,\r\n  0xbfffe,\r\n  0xbffff /* [NONCHARACTER CODE POINTS] */,\r\n  0xcfffe,\r\n  0xcffff /* [NONCHARACTER CODE POINTS] */,\r\n  0xdfffe,\r\n  0xdffff /* [NONCHARACTER CODE POINTS] */,\r\n  0xefffe,\r\n  0xeffff /* [NONCHARACTER CODE POINTS] */,\r\n  0x10fffe,\r\n  0x10ffff /* [NONCHARACTER CODE POINTS] */\r\n];\r\n\r\n/**\r\n * 2.3.  Prohibited Output\r\n */\r\nconst prohibited_characters = [\r\n  /**\r\n   * C.2.1 ASCII control characters\r\n   * @link https://tools.ietf.org/html/rfc3454#appendix-C.2.1\r\n   */\r\n  0,\r\n  0x001f /* [CONTROL CHARACTERS] */,\r\n  0x007f,\r\n  0x007f /* DELETE */,\r\n\r\n  /**\r\n   * C.8 Change display properties or are deprecated\r\n   * @link https://tools.ietf.org/html/rfc3454#appendix-C.8\r\n   */\r\n  0x0340,\r\n  0x0340 /* COMBINING GRAVE TONE MARK */,\r\n  0x0341,\r\n  0x0341 /* COMBINING ACUTE TONE MARK */,\r\n  0x200e,\r\n  0x200e /* LEFT-TO-RIGHT MARK */,\r\n  0x200f,\r\n  0x200f /* RIGHT-TO-LEFT MARK */,\r\n  0x202a,\r\n  0x202a /* LEFT-TO-RIGHT EMBEDDING */,\r\n  0x202b,\r\n  0x202b /* RIGHT-TO-LEFT EMBEDDING */,\r\n  0x202c,\r\n  0x202c /* POP DIRECTIONAL FORMATTING */,\r\n  0x202d,\r\n  0x202d /* LEFT-TO-RIGHT OVERRIDE */,\r\n  0x202e,\r\n  0x202e /* RIGHT-TO-LEFT OVERRIDE */,\r\n  0x206a,\r\n  0x206a /* INHIBIT SYMMETRIC SWAPPING */,\r\n  0x206b,\r\n  0x206b /* ACTIVATE SYMMETRIC SWAPPING */,\r\n  0x206c,\r\n  0x206c /* INHIBIT ARABIC FORM SHAPING */,\r\n  0x206d,\r\n  0x206d /* ACTIVATE ARABIC FORM SHAPING */,\r\n  0x206e,\r\n  0x206e /* NATIONAL DIGIT SHAPES */,\r\n  0x206f,\r\n  0x206f /* NOMINAL DIGIT SHAPES */,\r\n\r\n  /**\r\n   * C.7 Inappropriate for canonical representation\r\n   * @link https://tools.ietf.org/html/rfc3454#appendix-C.7\r\n   */\r\n  0x2ff0,\r\n  0x2ffb /* [IDEOGRAPHIC DESCRIPTION CHARACTERS] */,\r\n\r\n  /**\r\n   * C.5 Surrogate codes\r\n   * @link https://tools.ietf.org/html/rfc3454#appendix-C.5\r\n   */\r\n  0xd800,\r\n  0xdfff,\r\n\r\n  /**\r\n   * C.3 Private use\r\n   * @link https://tools.ietf.org/html/rfc3454#appendix-C.3\r\n   */\r\n  0xe000,\r\n  0xf8ff /* [PRIVATE USE, PLANE 0] */,\r\n\r\n  /**\r\n   * C.6 Inappropriate for plain text\r\n   * @link https://tools.ietf.org/html/rfc3454#appendix-C.6\r\n   */\r\n  0xfff9,\r\n  0xfff9 /* INTERLINEAR ANNOTATION ANCHOR */,\r\n  0xfffa,\r\n  0xfffa /* INTERLINEAR ANNOTATION SEPARATOR */,\r\n  0xfffb,\r\n  0xfffb /* INTERLINEAR ANNOTATION TERMINATOR */,\r\n  0xfffc,\r\n  0xfffc /* OBJECT REPLACEMENT CHARACTER */,\r\n  0xfffd,\r\n  0xfffd /* REPLACEMENT CHARACTER */,\r\n\r\n  /**\r\n   * C.9 Tagging characters\r\n   * @link https://tools.ietf.org/html/rfc3454#appendix-C.9\r\n   */\r\n  0xe0001,\r\n  0xe0001 /* LANGUAGE TAG */,\r\n  0xe0020,\r\n  0xe007f /* [TAGGING CHARACTERS] */,\r\n\r\n  /**\r\n   * C.3 Private use\r\n   * @link https://tools.ietf.org/html/rfc3454#appendix-C.3\r\n   */\r\n\r\n  0xf0000,\r\n  0xffffd /* [PRIVATE USE, PLANE 15] */,\r\n  0x100000,\r\n  0x10fffd /* [PRIVATE USE, PLANE 16] */\r\n];\r\n// prettier-ignore-end\r\n\r\nconst isProhibitedCharacter = character =>\r\n  inRange(character, non_ASCII_space_characters) ||\r\n  inRange(character, prohibited_characters) ||\r\n  inRange(character, non_ASCII_controls_characters) ||\r\n  inRange(character, non_character_codepoints);\r\n\r\n// prettier-ignore-start\r\n/**\r\n * D.1 Characters with bidirectional property \"R\" or \"AL\"\r\n * @link https://tools.ietf.org/html/rfc3454#appendix-D.1\r\n */\r\nconst bidirectional_r_al = [\r\n  0x05be,\r\n  0x05be,\r\n  0x05c0,\r\n  0x05c0,\r\n  0x05c3,\r\n  0x05c3,\r\n  0x05d0,\r\n  0x05ea,\r\n  0x05f0,\r\n  0x05f4,\r\n  0x061b,\r\n  0x061b,\r\n  0x061f,\r\n  0x061f,\r\n  0x0621,\r\n  0x063a,\r\n  0x0640,\r\n  0x064a,\r\n  0x066d,\r\n  0x066f,\r\n  0x0671,\r\n  0x06d5,\r\n  0x06dd,\r\n  0x06dd,\r\n  0x06e5,\r\n  0x06e6,\r\n  0x06fa,\r\n  0x06fe,\r\n  0x0700,\r\n  0x070d,\r\n  0x0710,\r\n  0x0710,\r\n  0x0712,\r\n  0x072c,\r\n  0x0780,\r\n  0x07a5,\r\n  0x07b1,\r\n  0x07b1,\r\n  0x200f,\r\n  0x200f,\r\n  0xfb1d,\r\n  0xfb1d,\r\n  0xfb1f,\r\n  0xfb28,\r\n  0xfb2a,\r\n  0xfb36,\r\n  0xfb38,\r\n  0xfb3c,\r\n  0xfb3e,\r\n  0xfb3e,\r\n  0xfb40,\r\n  0xfb41,\r\n  0xfb43,\r\n  0xfb44,\r\n  0xfb46,\r\n  0xfbb1,\r\n  0xfbd3,\r\n  0xfd3d,\r\n  0xfd50,\r\n  0xfd8f,\r\n  0xfd92,\r\n  0xfdc7,\r\n  0xfdf0,\r\n  0xfdfc,\r\n  0xfe70,\r\n  0xfe74,\r\n  0xfe76,\r\n  0xfefc\r\n];\r\n// prettier-ignore-end\r\n\r\nconst isBidirectionalRAL = character => inRange(character, bidirectional_r_al);\r\n\r\n// prettier-ignore-start\r\n/**\r\n * D.2 Characters with bidirectional property \"L\"\r\n * @link https://tools.ietf.org/html/rfc3454#appendix-D.2\r\n */\r\nconst bidirectional_l = [\r\n  0x0041,\r\n  0x005a,\r\n  0x0061,\r\n  0x007a,\r\n  0x00aa,\r\n  0x00aa,\r\n  0x00b5,\r\n  0x00b5,\r\n  0x00ba,\r\n  0x00ba,\r\n  0x00c0,\r\n  0x00d6,\r\n  0x00d8,\r\n  0x00f6,\r\n  0x00f8,\r\n  0x0220,\r\n  0x0222,\r\n  0x0233,\r\n  0x0250,\r\n  0x02ad,\r\n  0x02b0,\r\n  0x02b8,\r\n  0x02bb,\r\n  0x02c1,\r\n  0x02d0,\r\n  0x02d1,\r\n  0x02e0,\r\n  0x02e4,\r\n  0x02ee,\r\n  0x02ee,\r\n  0x037a,\r\n  0x037a,\r\n  0x0386,\r\n  0x0386,\r\n  0x0388,\r\n  0x038a,\r\n  0x038c,\r\n  0x038c,\r\n  0x038e,\r\n  0x03a1,\r\n  0x03a3,\r\n  0x03ce,\r\n  0x03d0,\r\n  0x03f5,\r\n  0x0400,\r\n  0x0482,\r\n  0x048a,\r\n  0x04ce,\r\n  0x04d0,\r\n  0x04f5,\r\n  0x04f8,\r\n  0x04f9,\r\n  0x0500,\r\n  0x050f,\r\n  0x0531,\r\n  0x0556,\r\n  0x0559,\r\n  0x055f,\r\n  0x0561,\r\n  0x0587,\r\n  0x0589,\r\n  0x0589,\r\n  0x0903,\r\n  0x0903,\r\n  0x0905,\r\n  0x0939,\r\n  0x093d,\r\n  0x0940,\r\n  0x0949,\r\n  0x094c,\r\n  0x0950,\r\n  0x0950,\r\n  0x0958,\r\n  0x0961,\r\n  0x0964,\r\n  0x0970,\r\n  0x0982,\r\n  0x0983,\r\n  0x0985,\r\n  0x098c,\r\n  0x098f,\r\n  0x0990,\r\n  0x0993,\r\n  0x09a8,\r\n  0x09aa,\r\n  0x09b0,\r\n  0x09b2,\r\n  0x09b2,\r\n  0x09b6,\r\n  0x09b9,\r\n  0x09be,\r\n  0x09c0,\r\n  0x09c7,\r\n  0x09c8,\r\n  0x09cb,\r\n  0x09cc,\r\n  0x09d7,\r\n  0x09d7,\r\n  0x09dc,\r\n  0x09dd,\r\n  0x09df,\r\n  0x09e1,\r\n  0x09e6,\r\n  0x09f1,\r\n  0x09f4,\r\n  0x09fa,\r\n  0x0a05,\r\n  0x0a0a,\r\n  0x0a0f,\r\n  0x0a10,\r\n  0x0a13,\r\n  0x0a28,\r\n  0x0a2a,\r\n  0x0a30,\r\n  0x0a32,\r\n  0x0a33,\r\n  0x0a35,\r\n  0x0a36,\r\n  0x0a38,\r\n  0x0a39,\r\n  0x0a3e,\r\n  0x0a40,\r\n  0x0a59,\r\n  0x0a5c,\r\n  0x0a5e,\r\n  0x0a5e,\r\n  0x0a66,\r\n  0x0a6f,\r\n  0x0a72,\r\n  0x0a74,\r\n  0x0a83,\r\n  0x0a83,\r\n  0x0a85,\r\n  0x0a8b,\r\n  0x0a8d,\r\n  0x0a8d,\r\n  0x0a8f,\r\n  0x0a91,\r\n  0x0a93,\r\n  0x0aa8,\r\n  0x0aaa,\r\n  0x0ab0,\r\n  0x0ab2,\r\n  0x0ab3,\r\n  0x0ab5,\r\n  0x0ab9,\r\n  0x0abd,\r\n  0x0ac0,\r\n  0x0ac9,\r\n  0x0ac9,\r\n  0x0acb,\r\n  0x0acc,\r\n  0x0ad0,\r\n  0x0ad0,\r\n  0x0ae0,\r\n  0x0ae0,\r\n  0x0ae6,\r\n  0x0aef,\r\n  0x0b02,\r\n  0x0b03,\r\n  0x0b05,\r\n  0x0b0c,\r\n  0x0b0f,\r\n  0x0b10,\r\n  0x0b13,\r\n  0x0b28,\r\n  0x0b2a,\r\n  0x0b30,\r\n  0x0b32,\r\n  0x0b33,\r\n  0x0b36,\r\n  0x0b39,\r\n  0x0b3d,\r\n  0x0b3e,\r\n  0x0b40,\r\n  0x0b40,\r\n  0x0b47,\r\n  0x0b48,\r\n  0x0b4b,\r\n  0x0b4c,\r\n  0x0b57,\r\n  0x0b57,\r\n  0x0b5c,\r\n  0x0b5d,\r\n  0x0b5f,\r\n  0x0b61,\r\n  0x0b66,\r\n  0x0b70,\r\n  0x0b83,\r\n  0x0b83,\r\n  0x0b85,\r\n  0x0b8a,\r\n  0x0b8e,\r\n  0x0b90,\r\n  0x0b92,\r\n  0x0b95,\r\n  0x0b99,\r\n  0x0b9a,\r\n  0x0b9c,\r\n  0x0b9c,\r\n  0x0b9e,\r\n  0x0b9f,\r\n  0x0ba3,\r\n  0x0ba4,\r\n  0x0ba8,\r\n  0x0baa,\r\n  0x0bae,\r\n  0x0bb5,\r\n  0x0bb7,\r\n  0x0bb9,\r\n  0x0bbe,\r\n  0x0bbf,\r\n  0x0bc1,\r\n  0x0bc2,\r\n  0x0bc6,\r\n  0x0bc8,\r\n  0x0bca,\r\n  0x0bcc,\r\n  0x0bd7,\r\n  0x0bd7,\r\n  0x0be7,\r\n  0x0bf2,\r\n  0x0c01,\r\n  0x0c03,\r\n  0x0c05,\r\n  0x0c0c,\r\n  0x0c0e,\r\n  0x0c10,\r\n  0x0c12,\r\n  0x0c28,\r\n  0x0c2a,\r\n  0x0c33,\r\n  0x0c35,\r\n  0x0c39,\r\n  0x0c41,\r\n  0x0c44,\r\n  0x0c60,\r\n  0x0c61,\r\n  0x0c66,\r\n  0x0c6f,\r\n  0x0c82,\r\n  0x0c83,\r\n  0x0c85,\r\n  0x0c8c,\r\n  0x0c8e,\r\n  0x0c90,\r\n  0x0c92,\r\n  0x0ca8,\r\n  0x0caa,\r\n  0x0cb3,\r\n  0x0cb5,\r\n  0x0cb9,\r\n  0x0cbe,\r\n  0x0cbe,\r\n  0x0cc0,\r\n  0x0cc4,\r\n  0x0cc7,\r\n  0x0cc8,\r\n  0x0cca,\r\n  0x0ccb,\r\n  0x0cd5,\r\n  0x0cd6,\r\n  0x0cde,\r\n  0x0cde,\r\n  0x0ce0,\r\n  0x0ce1,\r\n  0x0ce6,\r\n  0x0cef,\r\n  0x0d02,\r\n  0x0d03,\r\n  0x0d05,\r\n  0x0d0c,\r\n  0x0d0e,\r\n  0x0d10,\r\n  0x0d12,\r\n  0x0d28,\r\n  0x0d2a,\r\n  0x0d39,\r\n  0x0d3e,\r\n  0x0d40,\r\n  0x0d46,\r\n  0x0d48,\r\n  0x0d4a,\r\n  0x0d4c,\r\n  0x0d57,\r\n  0x0d57,\r\n  0x0d60,\r\n  0x0d61,\r\n  0x0d66,\r\n  0x0d6f,\r\n  0x0d82,\r\n  0x0d83,\r\n  0x0d85,\r\n  0x0d96,\r\n  0x0d9a,\r\n  0x0db1,\r\n  0x0db3,\r\n  0x0dbb,\r\n  0x0dbd,\r\n  0x0dbd,\r\n  0x0dc0,\r\n  0x0dc6,\r\n  0x0dcf,\r\n  0x0dd1,\r\n  0x0dd8,\r\n  0x0ddf,\r\n  0x0df2,\r\n  0x0df4,\r\n  0x0e01,\r\n  0x0e30,\r\n  0x0e32,\r\n  0x0e33,\r\n  0x0e40,\r\n  0x0e46,\r\n  0x0e4f,\r\n  0x0e5b,\r\n  0x0e81,\r\n  0x0e82,\r\n  0x0e84,\r\n  0x0e84,\r\n  0x0e87,\r\n  0x0e88,\r\n  0x0e8a,\r\n  0x0e8a,\r\n  0x0e8d,\r\n  0x0e8d,\r\n  0x0e94,\r\n  0x0e97,\r\n  0x0e99,\r\n  0x0e9f,\r\n  0x0ea1,\r\n  0x0ea3,\r\n  0x0ea5,\r\n  0x0ea5,\r\n  0x0ea7,\r\n  0x0ea7,\r\n  0x0eaa,\r\n  0x0eab,\r\n  0x0ead,\r\n  0x0eb0,\r\n  0x0eb2,\r\n  0x0eb3,\r\n  0x0ebd,\r\n  0x0ebd,\r\n  0x0ec0,\r\n  0x0ec4,\r\n  0x0ec6,\r\n  0x0ec6,\r\n  0x0ed0,\r\n  0x0ed9,\r\n  0x0edc,\r\n  0x0edd,\r\n  0x0f00,\r\n  0x0f17,\r\n  0x0f1a,\r\n  0x0f34,\r\n  0x0f36,\r\n  0x0f36,\r\n  0x0f38,\r\n  0x0f38,\r\n  0x0f3e,\r\n  0x0f47,\r\n  0x0f49,\r\n  0x0f6a,\r\n  0x0f7f,\r\n  0x0f7f,\r\n  0x0f85,\r\n  0x0f85,\r\n  0x0f88,\r\n  0x0f8b,\r\n  0x0fbe,\r\n  0x0fc5,\r\n  0x0fc7,\r\n  0x0fcc,\r\n  0x0fcf,\r\n  0x0fcf,\r\n  0x1000,\r\n  0x1021,\r\n  0x1023,\r\n  0x1027,\r\n  0x1029,\r\n  0x102a,\r\n  0x102c,\r\n  0x102c,\r\n  0x1031,\r\n  0x1031,\r\n  0x1038,\r\n  0x1038,\r\n  0x1040,\r\n  0x1057,\r\n  0x10a0,\r\n  0x10c5,\r\n  0x10d0,\r\n  0x10f8,\r\n  0x10fb,\r\n  0x10fb,\r\n  0x1100,\r\n  0x1159,\r\n  0x115f,\r\n  0x11a2,\r\n  0x11a8,\r\n  0x11f9,\r\n  0x1200,\r\n  0x1206,\r\n  0x1208,\r\n  0x1246,\r\n  0x1248,\r\n  0x1248,\r\n  0x124a,\r\n  0x124d,\r\n  0x1250,\r\n  0x1256,\r\n  0x1258,\r\n  0x1258,\r\n  0x125a,\r\n  0x125d,\r\n  0x1260,\r\n  0x1286,\r\n  0x1288,\r\n  0x1288,\r\n  0x128a,\r\n  0x128d,\r\n  0x1290,\r\n  0x12ae,\r\n  0x12b0,\r\n  0x12b0,\r\n  0x12b2,\r\n  0x12b5,\r\n  0x12b8,\r\n  0x12be,\r\n  0x12c0,\r\n  0x12c0,\r\n  0x12c2,\r\n  0x12c5,\r\n  0x12c8,\r\n  0x12ce,\r\n  0x12d0,\r\n  0x12d6,\r\n  0x12d8,\r\n  0x12ee,\r\n  0x12f0,\r\n  0x130e,\r\n  0x1310,\r\n  0x1310,\r\n  0x1312,\r\n  0x1315,\r\n  0x1318,\r\n  0x131e,\r\n  0x1320,\r\n  0x1346,\r\n  0x1348,\r\n  0x135a,\r\n  0x1361,\r\n  0x137c,\r\n  0x13a0,\r\n  0x13f4,\r\n  0x1401,\r\n  0x1676,\r\n  0x1681,\r\n  0x169a,\r\n  0x16a0,\r\n  0x16f0,\r\n  0x1700,\r\n  0x170c,\r\n  0x170e,\r\n  0x1711,\r\n  0x1720,\r\n  0x1731,\r\n  0x1735,\r\n  0x1736,\r\n  0x1740,\r\n  0x1751,\r\n  0x1760,\r\n  0x176c,\r\n  0x176e,\r\n  0x1770,\r\n  0x1780,\r\n  0x17b6,\r\n  0x17be,\r\n  0x17c5,\r\n  0x17c7,\r\n  0x17c8,\r\n  0x17d4,\r\n  0x17da,\r\n  0x17dc,\r\n  0x17dc,\r\n  0x17e0,\r\n  0x17e9,\r\n  0x1810,\r\n  0x1819,\r\n  0x1820,\r\n  0x1877,\r\n  0x1880,\r\n  0x18a8,\r\n  0x1e00,\r\n  0x1e9b,\r\n  0x1ea0,\r\n  0x1ef9,\r\n  0x1f00,\r\n  0x1f15,\r\n  0x1f18,\r\n  0x1f1d,\r\n  0x1f20,\r\n  0x1f45,\r\n  0x1f48,\r\n  0x1f4d,\r\n  0x1f50,\r\n  0x1f57,\r\n  0x1f59,\r\n  0x1f59,\r\n  0x1f5b,\r\n  0x1f5b,\r\n  0x1f5d,\r\n  0x1f5d,\r\n  0x1f5f,\r\n  0x1f7d,\r\n  0x1f80,\r\n  0x1fb4,\r\n  0x1fb6,\r\n  0x1fbc,\r\n  0x1fbe,\r\n  0x1fbe,\r\n  0x1fc2,\r\n  0x1fc4,\r\n  0x1fc6,\r\n  0x1fcc,\r\n  0x1fd0,\r\n  0x1fd3,\r\n  0x1fd6,\r\n  0x1fdb,\r\n  0x1fe0,\r\n  0x1fec,\r\n  0x1ff2,\r\n  0x1ff4,\r\n  0x1ff6,\r\n  0x1ffc,\r\n  0x200e,\r\n  0x200e,\r\n  0x2071,\r\n  0x2071,\r\n  0x207f,\r\n  0x207f,\r\n  0x2102,\r\n  0x2102,\r\n  0x2107,\r\n  0x2107,\r\n  0x210a,\r\n  0x2113,\r\n  0x2115,\r\n  0x2115,\r\n  0x2119,\r\n  0x211d,\r\n  0x2124,\r\n  0x2124,\r\n  0x2126,\r\n  0x2126,\r\n  0x2128,\r\n  0x2128,\r\n  0x212a,\r\n  0x212d,\r\n  0x212f,\r\n  0x2131,\r\n  0x2133,\r\n  0x2139,\r\n  0x213d,\r\n  0x213f,\r\n  0x2145,\r\n  0x2149,\r\n  0x2160,\r\n  0x2183,\r\n  0x2336,\r\n  0x237a,\r\n  0x2395,\r\n  0x2395,\r\n  0x249c,\r\n  0x24e9,\r\n  0x3005,\r\n  0x3007,\r\n  0x3021,\r\n  0x3029,\r\n  0x3031,\r\n  0x3035,\r\n  0x3038,\r\n  0x303c,\r\n  0x3041,\r\n  0x3096,\r\n  0x309d,\r\n  0x309f,\r\n  0x30a1,\r\n  0x30fa,\r\n  0x30fc,\r\n  0x30ff,\r\n  0x3105,\r\n  0x312c,\r\n  0x3131,\r\n  0x318e,\r\n  0x3190,\r\n  0x31b7,\r\n  0x31f0,\r\n  0x321c,\r\n  0x3220,\r\n  0x3243,\r\n  0x3260,\r\n  0x327b,\r\n  0x327f,\r\n  0x32b0,\r\n  0x32c0,\r\n  0x32cb,\r\n  0x32d0,\r\n  0x32fe,\r\n  0x3300,\r\n  0x3376,\r\n  0x337b,\r\n  0x33dd,\r\n  0x33e0,\r\n  0x33fe,\r\n  0x3400,\r\n  0x4db5,\r\n  0x4e00,\r\n  0x9fa5,\r\n  0xa000,\r\n  0xa48c,\r\n  0xac00,\r\n  0xd7a3,\r\n  0xd800,\r\n  0xfa2d,\r\n  0xfa30,\r\n  0xfa6a,\r\n  0xfb00,\r\n  0xfb06,\r\n  0xfb13,\r\n  0xfb17,\r\n  0xff21,\r\n  0xff3a,\r\n  0xff41,\r\n  0xff5a,\r\n  0xff66,\r\n  0xffbe,\r\n  0xffc2,\r\n  0xffc7,\r\n  0xffca,\r\n  0xffcf,\r\n  0xffd2,\r\n  0xffd7,\r\n  0xffda,\r\n  0xffdc,\r\n  0x10300,\r\n  0x1031e,\r\n  0x10320,\r\n  0x10323,\r\n  0x10330,\r\n  0x1034a,\r\n  0x10400,\r\n  0x10425,\r\n  0x10428,\r\n  0x1044d,\r\n  0x1d000,\r\n  0x1d0f5,\r\n  0x1d100,\r\n  0x1d126,\r\n  0x1d12a,\r\n  0x1d166,\r\n  0x1d16a,\r\n  0x1d172,\r\n  0x1d183,\r\n  0x1d184,\r\n  0x1d18c,\r\n  0x1d1a9,\r\n  0x1d1ae,\r\n  0x1d1dd,\r\n  0x1d400,\r\n  0x1d454,\r\n  0x1d456,\r\n  0x1d49c,\r\n  0x1d49e,\r\n  0x1d49f,\r\n  0x1d4a2,\r\n  0x1d4a2,\r\n  0x1d4a5,\r\n  0x1d4a6,\r\n  0x1d4a9,\r\n  0x1d4ac,\r\n  0x1d4ae,\r\n  0x1d4b9,\r\n  0x1d4bb,\r\n  0x1d4bb,\r\n  0x1d4bd,\r\n  0x1d4c0,\r\n  0x1d4c2,\r\n  0x1d4c3,\r\n  0x1d4c5,\r\n  0x1d505,\r\n  0x1d507,\r\n  0x1d50a,\r\n  0x1d50d,\r\n  0x1d514,\r\n  0x1d516,\r\n  0x1d51c,\r\n  0x1d51e,\r\n  0x1d539,\r\n  0x1d53b,\r\n  0x1d53e,\r\n  0x1d540,\r\n  0x1d544,\r\n  0x1d546,\r\n  0x1d546,\r\n  0x1d54a,\r\n  0x1d550,\r\n  0x1d552,\r\n  0x1d6a3,\r\n  0x1d6a8,\r\n  0x1d7c9,\r\n  0x20000,\r\n  0x2a6d6,\r\n  0x2f800,\r\n  0x2fa1d,\r\n  0xf0000,\r\n  0xffffd,\r\n  0x100000,\r\n  0x10fffd\r\n];\r\n// prettier-ignore-end\r\n\r\nconst isBidirectionalL = character => inRange(character, bidirectional_l);\r\n\r\nexport {\r\n  isUnassignedCodePoint,\r\n  isCommonlyMappedToNothing,\r\n  isNonASCIISpaceCharacter,\r\n  isProhibitedCharacter,\r\n  isBidirectionalRAL,\r\n  isBidirectionalL\r\n};\r\n", "import {\r\n  isUnassignedCodePoint,\r\n  isC<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\r\n  isNonASCI<PERSON><PERSON><PERSON>haracter,\r\n  isProhibited<PERSON>haracter,\r\n  isBidirectionalRAL,\r\n  isBidirectionalL\r\n} from './lib/code-points';\r\n\r\n// 2.1.  Mapping\r\n\r\n/**\r\n * non-ASCII space characters [StringPrep, C.1.2] that can be\r\n * mapped to SPACE (U+0020)\r\n */\r\nconst mapping2space = isNonASCIISpaceCharacter;\r\n\r\n/**\r\n * the \"commonly mapped to nothing\" characters [StringPrep, B.1]\r\n * that can be mapped to nothing.\r\n */\r\nconst mapping2nothing = isCommonlyMappedToNothing;\r\n\r\n// utils\r\nconst getCodePoint = character => character.codePointAt(0);\r\nconst first = x => x[0];\r\nconst last = x => x[x.length - 1];\r\n\r\n/**\r\n * Convert provided string into an array of Unicode Code Points.\r\n * Based on https://stackoverflow.com/a/21409165/1556249\r\n * and https://www.npmjs.com/package/code-point-at.\r\n * @param {string} input\r\n * @returns {number[]}\r\n */\r\nfunction toCodePoints(input) {\r\n  const codepoints = [];\r\n  const size = input.length;\r\n\r\n  for (let i = 0; i < size; i += 1) {\r\n    const before = input.charCodeAt(i);\r\n\r\n    if (before >= 0xd800 && before <= 0xdbff && size > i + 1) {\r\n      const next = input.charCodeAt(i + 1);\r\n\r\n      if (next >= 0xdc00 && next <= 0xdfff) {\r\n        codepoints.push((before - 0xd800) * 0x400 + next - 0xdc00 + 0x10000);\r\n        i += 1;\r\n        continue;\r\n      }\r\n    }\r\n\r\n    codepoints.push(before);\r\n  }\r\n\r\n  return codepoints;\r\n}\r\n\r\n/**\r\n * SASLprep.\r\n * @param {string} input\r\n * @param {Object} opts\r\n * @param {boolean} opts.allowUnassigned\r\n * @returns {string}\r\n */\r\nfunction saslprep(input, opts = {}) {\r\n  if (typeof input !== 'string') {\r\n    throw new TypeError('Expected string.');\r\n  }\r\n\r\n  if (input.length === 0) {\r\n    return '';\r\n  }\r\n\r\n  // 1. Map\r\n  const mapped_input = toCodePoints(input)\r\n    // 1.1 mapping to space\r\n    .map(character => (mapping2space(character) ? 0x20 : character))\r\n    // 1.2 mapping to nothing\r\n    .filter(character => !mapping2nothing(character));\r\n\r\n  // 2. Normalize\r\n  const normalized_input = String.fromCodePoint\r\n    .apply(null, mapped_input)\r\n    .normalize('NFKC');\r\n\r\n  const normalized_map = toCodePoints(normalized_input);\r\n\r\n  // 3. Prohibit\r\n  const hasProhibited = normalized_map.some(isProhibitedCharacter);\r\n\r\n  if (hasProhibited) {\r\n    throw new Error(\r\n      'Prohibited character, see https://tools.ietf.org/html/rfc4013#section-2.3'\r\n    );\r\n  }\r\n\r\n  // Unassigned Code Points\r\n  if (opts.allowUnassigned !== true) {\r\n    const hasUnassigned = normalized_map.some(isUnassignedCodePoint);\r\n\r\n    if (hasUnassigned) {\r\n      throw new Error(\r\n        'Unassigned code point, see https://tools.ietf.org/html/rfc4013#section-2.5'\r\n      );\r\n    }\r\n  }\r\n\r\n  // 4. check bidi\r\n\r\n  const hasBidiRAL = normalized_map.some(isBidirectionalRAL);\r\n\r\n  const hasBidiL = normalized_map.some(isBidirectionalL);\r\n\r\n  // 4.1 If a string contains any RandALCat character, the string MUST NOT\r\n  // contain any LCat character.\r\n  if (hasBidiRAL && hasBidiL) {\r\n    throw new Error(\r\n      'String must not contain RandALCat and LCat at the same time,' +\r\n        ' see https://tools.ietf.org/html/rfc3454#section-6'\r\n    );\r\n  }\r\n\r\n  /**\r\n   * 4.2 If a string contains any RandALCat character, a RandALCat\r\n   * character MUST be the first character of the string, and a\r\n   * RandALCat character MUST be the last character of the string.\r\n   */\r\n\r\n  const isFirstBidiRAL = isBidirectionalRAL(\r\n    getCodePoint(first(normalized_input))\r\n  );\r\n  const isLastBidiRAL = isBidirectionalRAL(\r\n    getCodePoint(last(normalized_input))\r\n  );\r\n\r\n  if (hasBidiRAL && !(isFirstBidiRAL && isLastBidiRAL)) {\r\n    throw new Error(\r\n      'Bidirectional RandALCat character must be the first and the last' +\r\n        ' character of the string, see https://tools.ietf.org/html/rfc3454#section-6'\r\n    );\r\n  }\r\n\r\n  return normalized_input;\r\n}\r\n\r\nexport default saslprep;\r\n", "/*\r\n   PDFSecurity - represents PDF security settings\r\n   By <PERSON> <<EMAIL>>\r\n */\r\n\r\nimport CryptoJS from 'crypto-js';\r\nimport saslprep from './saslprep/index';\r\n\r\nclass PDFSecurity {\r\n  static generateFileID(info = {}) {\r\n    let infoStr = `${info.CreationDate.getTime()}\\n`;\r\n\r\n    for (let key in info) {\r\n      // eslint-disable-next-line no-prototype-builtins\r\n      if (!info.hasOwnProperty(key)) {\r\n        continue;\r\n      }\r\n      infoStr += `${key}: ${info[key].valueOf()}\\n`;\r\n    }\r\n\r\n    return wordArrayToBuffer(CryptoJS.MD5(infoStr));\r\n  }\r\n\r\n  static generateRandomWordArray(bytes) {\r\n    return CryptoJS.lib.WordArray.random(bytes);\r\n  }\r\n\r\n  static create(document, options = {}) {\r\n    if (!options.ownerPassword && !options.userPassword) {\r\n      return null;\r\n    }\r\n    return new PDFSecurity(document, options);\r\n  }\r\n\r\n  constructor(document, options = {}) {\r\n    if (!options.ownerPassword && !options.userPassword) {\r\n      throw new Error('None of owner password and user password is defined.');\r\n    }\r\n\r\n    this.document = document;\r\n    this._setupEncryption(options);\r\n  }\r\n\r\n  _setupEncryption(options) {\r\n    switch (options.pdfVersion) {\r\n      case '1.4':\r\n      case '1.5':\r\n        this.version = 2;\r\n        break;\r\n      case '1.6':\r\n      case '1.7':\r\n        this.version = 4;\r\n        break;\r\n      case '1.7ext3':\r\n        this.version = 5;\r\n        break;\r\n      default:\r\n        this.version = 1;\r\n        break;\r\n    }\r\n\r\n    const encDict = {\r\n      Filter: 'Standard'\r\n    };\r\n\r\n    switch (this.version) {\r\n      case 1:\r\n      case 2:\r\n      case 4:\r\n        this._setupEncryptionV1V2V4(this.version, encDict, options);\r\n        break;\r\n      case 5:\r\n        this._setupEncryptionV5(encDict, options);\r\n        break;\r\n    }\r\n\r\n    this.dictionary = this.document.ref(encDict);\r\n  }\r\n\r\n  _setupEncryptionV1V2V4(v, encDict, options) {\r\n    let r, permissions;\r\n    switch (v) {\r\n      case 1:\r\n        r = 2;\r\n        this.keyBits = 40;\r\n        permissions = getPermissionsR2(options.permissions);\r\n        break;\r\n      case 2:\r\n        r = 3;\r\n        this.keyBits = 128;\r\n        permissions = getPermissionsR3(options.permissions);\r\n        break;\r\n      case 4:\r\n        r = 4;\r\n        this.keyBits = 128;\r\n        permissions = getPermissionsR3(options.permissions);\r\n        break;\r\n    }\r\n\r\n    const paddedUserPassword = processPasswordR2R3R4(options.userPassword);\r\n    const paddedOwnerPassword = options.ownerPassword\r\n      ? processPasswordR2R3R4(options.ownerPassword)\r\n      : paddedUserPassword;\r\n\r\n    const ownerPasswordEntry = getOwnerPasswordR2R3R4(\r\n      r,\r\n      this.keyBits,\r\n      paddedUserPassword,\r\n      paddedOwnerPassword\r\n    );\r\n    this.encryptionKey = getEncryptionKeyR2R3R4(\r\n      r,\r\n      this.keyBits,\r\n      this.document._id,\r\n      paddedUserPassword,\r\n      ownerPasswordEntry,\r\n      permissions\r\n    );\r\n    let userPasswordEntry;\r\n    if (r === 2) {\r\n      userPasswordEntry = getUserPasswordR2(this.encryptionKey);\r\n    } else {\r\n      userPasswordEntry = getUserPasswordR3R4(\r\n        this.document._id,\r\n        this.encryptionKey\r\n      );\r\n    }\r\n\r\n    encDict.V = v;\r\n    if (v >= 2) {\r\n      encDict.Length = this.keyBits;\r\n    }\r\n    if (v === 4) {\r\n      encDict.CF = {\r\n        StdCF: {\r\n          AuthEvent: 'DocOpen',\r\n          CFM: 'AESV2',\r\n          Length: this.keyBits / 8\r\n        }\r\n      };\r\n      encDict.StmF = 'StdCF';\r\n      encDict.StrF = 'StdCF';\r\n    }\r\n    encDict.R = r;\r\n    encDict.O = wordArrayToBuffer(ownerPasswordEntry);\r\n    encDict.U = wordArrayToBuffer(userPasswordEntry);\r\n    encDict.P = permissions;\r\n  }\r\n\r\n  _setupEncryptionV5(encDict, options) {\r\n    this.keyBits = 256;\r\n    const permissions = getPermissionsR3(options.permissions);\r\n\r\n    const processedUserPassword = processPasswordR5(options.userPassword);\r\n    const processedOwnerPassword = options.ownerPassword\r\n      ? processPasswordR5(options.ownerPassword)\r\n      : processedUserPassword;\r\n\r\n    this.encryptionKey = getEncryptionKeyR5(\r\n      PDFSecurity.generateRandomWordArray\r\n    );\r\n    const userPasswordEntry = getUserPasswordR5(\r\n      processedUserPassword,\r\n      PDFSecurity.generateRandomWordArray\r\n    );\r\n    const userKeySalt = CryptoJS.lib.WordArray.create(\r\n      userPasswordEntry.words.slice(10, 12),\r\n      8\r\n    );\r\n    const userEncryptionKeyEntry = getUserEncryptionKeyR5(\r\n      processedUserPassword,\r\n      userKeySalt,\r\n      this.encryptionKey\r\n    );\r\n    const ownerPasswordEntry = getOwnerPasswordR5(\r\n      processedOwnerPassword,\r\n      userPasswordEntry,\r\n      PDFSecurity.generateRandomWordArray\r\n    );\r\n    const ownerKeySalt = CryptoJS.lib.WordArray.create(\r\n      ownerPasswordEntry.words.slice(10, 12),\r\n      8\r\n    );\r\n    const ownerEncryptionKeyEntry = getOwnerEncryptionKeyR5(\r\n      processedOwnerPassword,\r\n      ownerKeySalt,\r\n      userPasswordEntry,\r\n      this.encryptionKey\r\n    );\r\n    const permsEntry = getEncryptedPermissionsR5(\r\n      permissions,\r\n      this.encryptionKey,\r\n      PDFSecurity.generateRandomWordArray\r\n    );\r\n\r\n    encDict.V = 5;\r\n    encDict.Length = this.keyBits;\r\n    encDict.CF = {\r\n      StdCF: {\r\n        AuthEvent: 'DocOpen',\r\n        CFM: 'AESV3',\r\n        Length: this.keyBits / 8\r\n      }\r\n    };\r\n    encDict.StmF = 'StdCF';\r\n    encDict.StrF = 'StdCF';\r\n    encDict.R = 5;\r\n    encDict.O = wordArrayToBuffer(ownerPasswordEntry);\r\n    encDict.OE = wordArrayToBuffer(ownerEncryptionKeyEntry);\r\n    encDict.U = wordArrayToBuffer(userPasswordEntry);\r\n    encDict.UE = wordArrayToBuffer(userEncryptionKeyEntry);\r\n    encDict.P = permissions;\r\n    encDict.Perms = wordArrayToBuffer(permsEntry);\r\n  }\r\n\r\n  getEncryptFn(obj, gen) {\r\n    let digest;\r\n    if (this.version < 5) {\r\n      digest = this.encryptionKey\r\n        .clone()\r\n        .concat(\r\n          CryptoJS.lib.WordArray.create(\r\n            [\r\n              ((obj & 0xff) << 24) |\r\n                ((obj & 0xff00) << 8) |\r\n                ((obj >> 8) & 0xff00) |\r\n                (gen & 0xff),\r\n              (gen & 0xff00) << 16\r\n            ],\r\n            5\r\n          )\r\n        );\r\n    }\r\n\r\n    if (this.version === 1 || this.version === 2) {\r\n      let key = CryptoJS.MD5(digest);\r\n      key.sigBytes = Math.min(16, this.keyBits / 8 + 5);\r\n      return buffer =>\r\n        wordArrayToBuffer(\r\n          CryptoJS.RC4.encrypt(CryptoJS.lib.WordArray.create(buffer), key)\r\n            .ciphertext\r\n        );\r\n    }\r\n\r\n    let key;\r\n    if (this.version === 4) {\r\n      key = CryptoJS.MD5(\r\n        digest.concat(CryptoJS.lib.WordArray.create([0x73416c54], 4))\r\n      );\r\n    } else {\r\n      key = this.encryptionKey;\r\n    }\r\n\r\n    const iv = PDFSecurity.generateRandomWordArray(16);\r\n    const options = {\r\n      mode: CryptoJS.mode.CBC,\r\n      padding: CryptoJS.pad.Pkcs7,\r\n      iv\r\n    };\r\n\r\n    return buffer =>\r\n      wordArrayToBuffer(\r\n        iv\r\n          .clone()\r\n          .concat(\r\n            CryptoJS.AES.encrypt(\r\n              CryptoJS.lib.WordArray.create(buffer),\r\n              key,\r\n              options\r\n            ).ciphertext\r\n          )\r\n      );\r\n  }\r\n\r\n  end() {\r\n    this.dictionary.end();\r\n  }\r\n}\r\n\r\nfunction getPermissionsR2(permissionObject = {}) {\r\n  let permissions = 0xffffffc0 >> 0;\r\n  if (permissionObject.printing) {\r\n    permissions |= 0b000000000100;\r\n  }\r\n  if (permissionObject.modifying) {\r\n    permissions |= 0b000000001000;\r\n  }\r\n  if (permissionObject.copying) {\r\n    permissions |= 0b000000010000;\r\n  }\r\n  if (permissionObject.annotating) {\r\n    permissions |= 0b000000100000;\r\n  }\r\n  return permissions;\r\n}\r\n\r\nfunction getPermissionsR3(permissionObject = {}) {\r\n  let permissions = 0xfffff0c0 >> 0;\r\n  if (permissionObject.printing === 'lowResolution') {\r\n    permissions |= 0b000000000100;\r\n  }\r\n  if (permissionObject.printing === 'highResolution') {\r\n    permissions |= 0b100000000100;\r\n  }\r\n  if (permissionObject.modifying) {\r\n    permissions |= 0b000000001000;\r\n  }\r\n  if (permissionObject.copying) {\r\n    permissions |= 0b000000010000;\r\n  }\r\n  if (permissionObject.annotating) {\r\n    permissions |= 0b000000100000;\r\n  }\r\n  if (permissionObject.fillingForms) {\r\n    permissions |= 0b000100000000;\r\n  }\r\n  if (permissionObject.contentAccessibility) {\r\n    permissions |= 0b001000000000;\r\n  }\r\n  if (permissionObject.documentAssembly) {\r\n    permissions |= 0b010000000000;\r\n  }\r\n  return permissions;\r\n}\r\n\r\nfunction getUserPasswordR2(encryptionKey) {\r\n  return CryptoJS.RC4.encrypt(processPasswordR2R3R4(), encryptionKey)\r\n    .ciphertext;\r\n}\r\n\r\nfunction getUserPasswordR3R4(documentId, encryptionKey) {\r\n  const key = encryptionKey.clone();\r\n  let cipher = CryptoJS.MD5(\r\n    processPasswordR2R3R4().concat(CryptoJS.lib.WordArray.create(documentId))\r\n  );\r\n  for (let i = 0; i < 20; i++) {\r\n    const xorRound = Math.ceil(key.sigBytes / 4);\r\n    for (let j = 0; j < xorRound; j++) {\r\n      key.words[j] =\r\n        encryptionKey.words[j] ^ (i | (i << 8) | (i << 16) | (i << 24));\r\n    }\r\n    cipher = CryptoJS.RC4.encrypt(cipher, key).ciphertext;\r\n  }\r\n  return cipher.concat(CryptoJS.lib.WordArray.create(null, 16));\r\n}\r\n\r\nfunction getOwnerPasswordR2R3R4(\r\n  r,\r\n  keyBits,\r\n  paddedUserPassword,\r\n  paddedOwnerPassword\r\n) {\r\n  let digest = paddedOwnerPassword;\r\n  let round = r >= 3 ? 51 : 1;\r\n  for (let i = 0; i < round; i++) {\r\n    digest = CryptoJS.MD5(digest);\r\n  }\r\n\r\n  const key = digest.clone();\r\n  key.sigBytes = keyBits / 8;\r\n  let cipher = paddedUserPassword;\r\n  round = r >= 3 ? 20 : 1;\r\n  for (let i = 0; i < round; i++) {\r\n    const xorRound = Math.ceil(key.sigBytes / 4);\r\n    for (let j = 0; j < xorRound; j++) {\r\n      key.words[j] = digest.words[j] ^ (i | (i << 8) | (i << 16) | (i << 24));\r\n    }\r\n    cipher = CryptoJS.RC4.encrypt(cipher, key).ciphertext;\r\n  }\r\n  return cipher;\r\n}\r\n\r\nfunction getEncryptionKeyR2R3R4(\r\n  r,\r\n  keyBits,\r\n  documentId,\r\n  paddedUserPassword,\r\n  ownerPasswordEntry,\r\n  permissions\r\n) {\r\n  let key = paddedUserPassword\r\n    .clone()\r\n    .concat(ownerPasswordEntry)\r\n    .concat(CryptoJS.lib.WordArray.create([lsbFirstWord(permissions)], 4))\r\n    .concat(CryptoJS.lib.WordArray.create(documentId));\r\n  const round = r >= 3 ? 51 : 1;\r\n  for (let i = 0; i < round; i++) {\r\n    key = CryptoJS.MD5(key);\r\n    key.sigBytes = keyBits / 8;\r\n  }\r\n  return key;\r\n}\r\n\r\nfunction getUserPasswordR5(processedUserPassword, generateRandomWordArray) {\r\n  const validationSalt = generateRandomWordArray(8);\r\n  const keySalt = generateRandomWordArray(8);\r\n  return CryptoJS.SHA256(processedUserPassword.clone().concat(validationSalt))\r\n    .concat(validationSalt)\r\n    .concat(keySalt);\r\n}\r\n\r\nfunction getUserEncryptionKeyR5(\r\n  processedUserPassword,\r\n  userKeySalt,\r\n  encryptionKey\r\n) {\r\n  const key = CryptoJS.SHA256(\r\n    processedUserPassword.clone().concat(userKeySalt)\r\n  );\r\n  const options = {\r\n    mode: CryptoJS.mode.CBC,\r\n    padding: CryptoJS.pad.NoPadding,\r\n    iv: CryptoJS.lib.WordArray.create(null, 16)\r\n  };\r\n  return CryptoJS.AES.encrypt(encryptionKey, key, options).ciphertext;\r\n}\r\n\r\nfunction getOwnerPasswordR5(\r\n  processedOwnerPassword,\r\n  userPasswordEntry,\r\n  generateRandomWordArray\r\n) {\r\n  const validationSalt = generateRandomWordArray(8);\r\n  const keySalt = generateRandomWordArray(8);\r\n  return CryptoJS.SHA256(\r\n    processedOwnerPassword\r\n      .clone()\r\n      .concat(validationSalt)\r\n      .concat(userPasswordEntry)\r\n  )\r\n    .concat(validationSalt)\r\n    .concat(keySalt);\r\n}\r\n\r\nfunction getOwnerEncryptionKeyR5(\r\n  processedOwnerPassword,\r\n  ownerKeySalt,\r\n  userPasswordEntry,\r\n  encryptionKey\r\n) {\r\n  const key = CryptoJS.SHA256(\r\n    processedOwnerPassword\r\n      .clone()\r\n      .concat(ownerKeySalt)\r\n      .concat(userPasswordEntry)\r\n  );\r\n  const options = {\r\n    mode: CryptoJS.mode.CBC,\r\n    padding: CryptoJS.pad.NoPadding,\r\n    iv: CryptoJS.lib.WordArray.create(null, 16)\r\n  };\r\n  return CryptoJS.AES.encrypt(encryptionKey, key, options).ciphertext;\r\n}\r\n\r\nfunction getEncryptionKeyR5(generateRandomWordArray) {\r\n  return generateRandomWordArray(32);\r\n}\r\n\r\nfunction getEncryptedPermissionsR5(\r\n  permissions,\r\n  encryptionKey,\r\n  generateRandomWordArray\r\n) {\r\n  const cipher = CryptoJS.lib.WordArray.create(\r\n    [lsbFirstWord(permissions), 0xffffffff, 0x54616462],\r\n    12\r\n  ).concat(generateRandomWordArray(4));\r\n  const options = {\r\n    mode: CryptoJS.mode.ECB,\r\n    padding: CryptoJS.pad.NoPadding\r\n  };\r\n  return CryptoJS.AES.encrypt(cipher, encryptionKey, options).ciphertext;\r\n}\r\n\r\nfunction processPasswordR2R3R4(password = '') {\r\n  const out = Buffer.alloc(32);\r\n  const length = password.length;\r\n  let index = 0;\r\n  while (index < length && index < 32) {\r\n    const code = password.charCodeAt(index);\r\n    if (code > 0xff) {\r\n      throw new Error('Password contains one or more invalid characters.');\r\n    }\r\n    out[index] = code;\r\n    index++;\r\n  }\r\n  while (index < 32) {\r\n    out[index] = PASSWORD_PADDING[index - length];\r\n    index++;\r\n  }\r\n  return CryptoJS.lib.WordArray.create(out);\r\n}\r\n\r\nfunction processPasswordR5(password = '') {\r\n  password = unescape(encodeURIComponent(saslprep(password)));\r\n  const length = Math.min(127, password.length);\r\n  const out = Buffer.alloc(length);\r\n\r\n  for (let i = 0; i < length; i++) {\r\n    out[i] = password.charCodeAt(i);\r\n  }\r\n\r\n  return CryptoJS.lib.WordArray.create(out);\r\n}\r\n\r\nfunction lsbFirstWord(data) {\r\n  return (\r\n    ((data & 0xff) << 24) |\r\n    ((data & 0xff00) << 8) |\r\n    ((data >> 8) & 0xff00) |\r\n    ((data >> 24) & 0xff)\r\n  );\r\n}\r\n\r\nfunction wordArrayToBuffer(wordArray) {\r\n  const byteArray = [];\r\n  for (let i = 0; i < wordArray.sigBytes; i++) {\r\n    byteArray.push(\r\n      (wordArray.words[Math.floor(i / 4)] >> (8 * (3 - (i % 4)))) & 0xff\r\n    );\r\n  }\r\n  return Buffer.from(byteArray);\r\n}\r\n\r\nconst PASSWORD_PADDING = [\r\n  0x28,\r\n  0xbf,\r\n  0x4e,\r\n  0x5e,\r\n  0x4e,\r\n  0x75,\r\n  0x8a,\r\n  0x41,\r\n  0x64,\r\n  0x00,\r\n  0x4e,\r\n  0x56,\r\n  0xff,\r\n  0xfa,\r\n  0x01,\r\n  0x08,\r\n  0x2e,\r\n  0x2e,\r\n  0x00,\r\n  0xb6,\r\n  0xd0,\r\n  0x68,\r\n  0x3e,\r\n  0x80,\r\n  0x2f,\r\n  0x0c,\r\n  0xa9,\r\n  0xfe,\r\n  0x64,\r\n  0x53,\r\n  0x69,\r\n  0x7a\r\n];\r\n\r\nexport default PDFSecurity;\r\n", "import PDFObject from './object';\r\n\r\nconst { number } = PDFObject;\r\n\r\nclass PDFGradient {\r\n  constructor(doc) {\r\n    this.doc = doc;\r\n    this.stops = [];\r\n    this.embedded = false;\r\n    this.transform = [1, 0, 0, 1, 0, 0];\r\n  }\r\n\r\n  stop(pos, color, opacity) {\r\n    if (opacity == null) {\r\n      opacity = 1;\r\n    }\r\n    color = this.doc._normalizeColor(color);\r\n\r\n    if (this.stops.length === 0) {\r\n      if (color.length === 3) {\r\n        this._colorSpace = 'DeviceRGB';\r\n      } else if (color.length === 4) {\r\n        this._colorSpace = 'DeviceCMYK';\r\n      } else if (color.length === 1) {\r\n        this._colorSpace = 'DeviceGray';\r\n      } else {\r\n        throw new Error('Unknown color space');\r\n      }\r\n    } else if (\r\n      (this._colorSpace === 'DeviceRGB' && color.length !== 3) ||\r\n      (this._colorSpace === 'DeviceCMYK' && color.length !== 4) ||\r\n      (this._colorSpace === 'DeviceGray' && color.length !== 1)\r\n    ) {\r\n      throw new Error('All gradient stops must use the same color space');\r\n    }\r\n\r\n    opacity = Math.max(0, Math.min(1, opacity));\r\n    this.stops.push([pos, color, opacity]);\r\n    return this;\r\n  }\r\n\r\n  setTransform(m11, m12, m21, m22, dx, dy) {\r\n    this.transform = [m11, m12, m21, m22, dx, dy];\r\n    return this;\r\n  }\r\n\r\n  embed(m) {\r\n    let fn;\r\n    const stopsLength = this.stops.length;\r\n    if (stopsLength === 0) {\r\n      return;\r\n    }\r\n    this.embedded = true;\r\n    this.matrix = m;\r\n\r\n    // if the last stop comes before 100%, add a copy at 100%\r\n    const last = this.stops[stopsLength - 1];\r\n    if (last[0] < 1) {\r\n      this.stops.push([1, last[1], last[2]]);\r\n    }\r\n\r\n    const bounds = [];\r\n    const encode = [];\r\n    const stops = [];\r\n\r\n    for (let i = 0; i < stopsLength - 1; i++) {\r\n      encode.push(0, 1);\r\n      if (i + 2 !== stopsLength) {\r\n        bounds.push(this.stops[i + 1][0]);\r\n      }\r\n\r\n      fn = this.doc.ref({\r\n        FunctionType: 2,\r\n        Domain: [0, 1],\r\n        C0: this.stops[i + 0][1],\r\n        C1: this.stops[i + 1][1],\r\n        N: 1\r\n      });\r\n\r\n      stops.push(fn);\r\n      fn.end();\r\n    }\r\n\r\n    // if there are only two stops, we don't need a stitching function\r\n    if (stopsLength === 1) {\r\n      fn = stops[0];\r\n    } else {\r\n      fn = this.doc.ref({\r\n        FunctionType: 3, // stitching function\r\n        Domain: [0, 1],\r\n        Functions: stops,\r\n        Bounds: bounds,\r\n        Encode: encode\r\n      });\r\n\r\n      fn.end();\r\n    }\r\n\r\n    this.id = `Sh${++this.doc._gradCount}`;\r\n\r\n    const shader = this.shader(fn);\r\n    shader.end();\r\n\r\n    const pattern = this.doc.ref({\r\n      Type: 'Pattern',\r\n      PatternType: 2,\r\n      Shading: shader,\r\n      Matrix: this.matrix.map(number)\r\n    });\r\n\r\n    pattern.end();\r\n\r\n    if (this.stops.some(stop => stop[2] < 1)) {\r\n      let grad = this.opacityGradient();\r\n      grad._colorSpace = 'DeviceGray';\r\n\r\n      for (let stop of this.stops) {\r\n        grad.stop(stop[0], [stop[2]]);\r\n      }\r\n\r\n      grad = grad.embed(this.matrix);\r\n\r\n      const pageBBox = [0, 0, this.doc.page.width, this.doc.page.height];\r\n\r\n      const form = this.doc.ref({\r\n        Type: 'XObject',\r\n        Subtype: 'Form',\r\n        FormType: 1,\r\n        BBox: pageBBox,\r\n        Group: {\r\n          Type: 'Group',\r\n          S: 'Transparency',\r\n          CS: 'DeviceGray'\r\n        },\r\n        Resources: {\r\n          ProcSet: ['PDF', 'Text', 'ImageB', 'ImageC', 'ImageI'],\r\n          Pattern: {\r\n            Sh1: grad\r\n          }\r\n        }\r\n      });\r\n\r\n      form.write('/Pattern cs /Sh1 scn');\r\n      form.end(`${pageBBox.join(' ')} re f`);\r\n\r\n      const gstate = this.doc.ref({\r\n        Type: 'ExtGState',\r\n        SMask: {\r\n          Type: 'Mask',\r\n          S: 'Luminosity',\r\n          G: form\r\n        }\r\n      });\r\n\r\n      gstate.end();\r\n\r\n      const opacityPattern = this.doc.ref({\r\n        Type: 'Pattern',\r\n        PatternType: 1,\r\n        PaintType: 1,\r\n        TilingType: 2,\r\n        BBox: pageBBox,\r\n        XStep: pageBBox[2],\r\n        YStep: pageBBox[3],\r\n        Resources: {\r\n          ProcSet: ['PDF', 'Text', 'ImageB', 'ImageC', 'ImageI'],\r\n          Pattern: {\r\n            Sh1: pattern\r\n          },\r\n          ExtGState: {\r\n            Gs1: gstate\r\n          }\r\n        }\r\n      });\r\n\r\n      opacityPattern.write('/Gs1 gs /Pattern cs /Sh1 scn');\r\n      opacityPattern.end(`${pageBBox.join(' ')} re f`);\r\n\r\n      this.doc.page.patterns[this.id] = opacityPattern;\r\n    } else {\r\n      this.doc.page.patterns[this.id] = pattern;\r\n    }\r\n\r\n    return pattern;\r\n  }\r\n\r\n  apply(stroke) {\r\n    // apply gradient transform to existing document ctm\r\n    const [m0, m1, m2, m3, m4, m5] = this.doc._ctm;\r\n    const [m11, m12, m21, m22, dx, dy] = this.transform;\r\n    const m = [\r\n      m0 * m11 + m2 * m12,\r\n      m1 * m11 + m3 * m12,\r\n      m0 * m21 + m2 * m22,\r\n      m1 * m21 + m3 * m22,\r\n      m0 * dx + m2 * dy + m4,\r\n      m1 * dx + m3 * dy + m5\r\n    ];\r\n\r\n    if (!this.embedded || m.join(' ') !== this.matrix.join(' ')) {\r\n      this.embed(m);\r\n    }\r\n    this.doc._setColorSpace('Pattern', stroke);\r\n    const op = stroke ? 'SCN' : 'scn';\r\n    return this.doc.addContent(`/${this.id} ${op}`);\r\n  }\r\n}\r\n\r\nclass PDFLinearGradient extends PDFGradient {\r\n  constructor(doc, x1, y1, x2, y2) {\r\n    super(doc);\r\n    this.x1 = x1;\r\n    this.y1 = y1;\r\n    this.x2 = x2;\r\n    this.y2 = y2;\r\n  }\r\n\r\n  shader(fn) {\r\n    return this.doc.ref({\r\n      ShadingType: 2,\r\n      ColorSpace: this._colorSpace,\r\n      Coords: [this.x1, this.y1, this.x2, this.y2],\r\n      Function: fn,\r\n      Extend: [true, true]\r\n    });\r\n  }\r\n\r\n  opacityGradient() {\r\n    return new PDFLinearGradient(this.doc, this.x1, this.y1, this.x2, this.y2);\r\n  }\r\n}\r\n\r\nclass PDFRadialGradient extends PDFGradient {\r\n  constructor(doc, x1, y1, r1, x2, y2, r2) {\r\n    super(doc);\r\n    this.doc = doc;\r\n    this.x1 = x1;\r\n    this.y1 = y1;\r\n    this.r1 = r1;\r\n    this.x2 = x2;\r\n    this.y2 = y2;\r\n    this.r2 = r2;\r\n  }\r\n\r\n  shader(fn) {\r\n    return this.doc.ref({\r\n      ShadingType: 3,\r\n      ColorSpace: this._colorSpace,\r\n      Coords: [this.x1, this.y1, this.r1, this.x2, this.y2, this.r2],\r\n      Function: fn,\r\n      Extend: [true, true]\r\n    });\r\n  }\r\n\r\n  opacityGradient() {\r\n    return new PDFRadialGradient(\r\n      this.doc,\r\n      this.x1,\r\n      this.y1,\r\n      this.r1,\r\n      this.x2,\r\n      this.y2,\r\n      this.r2\r\n    );\r\n  }\r\n}\r\n\r\nexport default { PDFGradient, PDFLinearGradient, PDFRadialGradient };\r\n", "/*\r\nPDF tiling pattern support. Uncolored only.\r\n */\r\n\r\nconst underlyingColorSpaces = ['DeviceCMYK', 'DeviceRGB'];\r\n\r\nclass PDFTilingPattern {\r\n  constructor(doc, bBox, xStep, yStep, stream) {\r\n    this.doc = doc;\r\n    this.bBox = bBox;\r\n    this.xStep = xStep;\r\n    this.yStep = yStep;\r\n    this.stream = stream;\r\n  }\r\n\r\n  createPattern() {\r\n    // no resources needed for our current usage\r\n    // required entry\r\n    const resources = this.doc.ref();\r\n    resources.end();\r\n    // apply default transform matrix (flipped in the default doc._ctm)\r\n    // see document.js & gradient.js\r\n    const [m0, m1, m2, m3, m4, m5] = this.doc._ctm;\r\n    const [m11, m12, m21, m22, dx, dy] = [1, 0, 0, 1, 0, 0];\r\n    const m = [\r\n      m0 * m11 + m2 * m12,\r\n      m1 * m11 + m3 * m12,\r\n      m0 * m21 + m2 * m22,\r\n      m1 * m21 + m3 * m22,\r\n      m0 * dx + m2 * dy + m4,\r\n      m1 * dx + m3 * dy + m5\r\n    ];\r\n    const pattern = this.doc.ref({\r\n      Type: 'Pattern',\r\n      PatternType: 1, // tiling\r\n      PaintType: 2, // 1-colored, 2-uncolored\r\n      TilingType: 2, // 2-no distortion\r\n      BBox: this.bBox,\r\n      XStep: this.xStep,\r\n      YStep: this.yStep,\r\n      Matrix: m.map(v => +v.toFixed(5)),\r\n      Resources: resources\r\n    });\r\n    pattern.end(this.stream);\r\n    return pattern;\r\n  }\r\n\r\n  embedPatternColorSpaces() {\r\n    // map each pattern to an underlying color space\r\n    // and embed on each page\r\n    underlyingColorSpaces.forEach(csName => {\r\n      const csId = this.getPatternColorSpaceId(csName);\r\n\r\n      if (this.doc.page.colorSpaces[csId]) return;\r\n      const cs = this.doc.ref(['Pattern', csName]);\r\n      cs.end();\r\n      this.doc.page.colorSpaces[csId] = cs;\r\n    });\r\n  }\r\n\r\n  getPatternColorSpaceId(underlyingColorspace) {\r\n    return `CsP${underlyingColorspace}`;\r\n  }\r\n\r\n  embed() {\r\n    if (!this.id) {\r\n      this.doc._patternCount = this.doc._patternCount + 1;\r\n      this.id = 'P' + this.doc._patternCount;\r\n      this.pattern = this.createPattern();\r\n    }\r\n\r\n    // patterns are embedded in each page\r\n    if (!this.doc.page.patterns[this.id]) {\r\n      this.doc.page.patterns[this.id] = this.pattern;\r\n    }\r\n  }\r\n\r\n  apply(stroke, patternColor) {\r\n    // do any embedding/creating that might be needed\r\n    this.embedPatternColorSpaces();\r\n    this.embed();\r\n\r\n    const normalizedColor = this.doc._normalizeColor(patternColor);\r\n    if (!normalizedColor)\r\n      throw Error(`invalid pattern color. (value: ${patternColor})`);\r\n\r\n    // select one of the pattern color spaces\r\n    const csId = this.getPatternColorSpaceId(\r\n      this.doc._getColorSpace(normalizedColor)\r\n    );\r\n    this.doc._setColorSpace(csId, stroke);\r\n\r\n    // stroke/fill using the pattern and color (in the above underlying color space)\r\n    const op = stroke ? 'SCN' : 'scn';\r\n    return this.doc.addContent(\r\n      `${normalizedColor.join(' ')} /${this.id} ${op}`\r\n    );\r\n  }\r\n}\r\n\r\nexport default { PDFTilingPattern };\r\n", "import Gradient from '../gradient';\r\nimport pattern from '../pattern';\r\n\r\nconst { PDFGradient, PDFLinearGradient, PDFRadialGradient } = Gradient;\r\nconst { PDFTilingPattern } = pattern;\r\n\r\nexport default {\r\n  initColor() {\r\n    // The opacity dictionaries\r\n    this._opacityRegistry = {};\r\n    this._opacityCount = 0;\r\n    this._patternCount = 0;\r\n    return (this._gradCount = 0);\r\n  },\r\n\r\n  _normalizeColor(color) {\r\n    if (typeof color === 'string') {\r\n      if (color.charAt(0) === '#') {\r\n        if (color.length === 4) {\r\n          color = color.replace(\r\n            /#([0-9A-F])([0-9A-F])([0-9A-F])/i,\r\n            '#$1$1$2$2$3$3'\r\n          );\r\n        }\r\n        const hex = parseInt(color.slice(1), 16);\r\n        color = [hex >> 16, (hex >> 8) & 0xff, hex & 0xff];\r\n      } else if (namedColors[color]) {\r\n        color = namedColors[color];\r\n      }\r\n    }\r\n\r\n    if (Array.isArray(color)) {\r\n      // RGB\r\n      if (color.length === 3) {\r\n        color = color.map(part => part / 255);\r\n        // CMYK\r\n      } else if (color.length === 4) {\r\n        color = color.map(part => part / 100);\r\n      }\r\n      return color;\r\n    }\r\n\r\n    return null;\r\n  },\r\n\r\n  _setColor(color, stroke) {\r\n    if (color instanceof PDFGradient) {\r\n      color.apply(stroke);\r\n      return true;\r\n      // see if tiling pattern, decode & apply it it\r\n    } else if (Array.isArray(color) && color[0] instanceof PDFTilingPattern) {\r\n      color[0].apply(stroke, color[1]);\r\n      return true;\r\n    }\r\n    // any other case should be a normal color and not a pattern\r\n    return this._setColorCore(color, stroke);\r\n  },\r\n\r\n  _setColorCore(color, stroke) {\r\n    color = this._normalizeColor(color);\r\n    if (!color) {\r\n      return false;\r\n    }\r\n\r\n    const op = stroke ? 'SCN' : 'scn';\r\n    const space = this._getColorSpace(color);\r\n    this._setColorSpace(space, stroke);\r\n\r\n    color = color.join(' ');\r\n    this.addContent(`${color} ${op}`);\r\n\r\n    return true;\r\n  },\r\n\r\n  _setColorSpace(space, stroke) {\r\n    const op = stroke ? 'CS' : 'cs';\r\n    return this.addContent(`/${space} ${op}`);\r\n  },\r\n\r\n  _getColorSpace(color) {\r\n    return color.length === 4 ? 'DeviceCMYK' : 'DeviceRGB';\r\n  },\r\n\r\n  fillColor(color, opacity) {\r\n    const set = this._setColor(color, false);\r\n    if (set) {\r\n      this.fillOpacity(opacity);\r\n    }\r\n\r\n    // save this for text wrapper, which needs to reset\r\n    // the fill color on new pages\r\n    this._fillColor = [color, opacity];\r\n    return this;\r\n  },\r\n\r\n  strokeColor(color, opacity) {\r\n    const set = this._setColor(color, true);\r\n    if (set) {\r\n      this.strokeOpacity(opacity);\r\n    }\r\n    return this;\r\n  },\r\n\r\n  opacity(opacity) {\r\n    this._doOpacity(opacity, opacity);\r\n    return this;\r\n  },\r\n\r\n  fillOpacity(opacity) {\r\n    this._doOpacity(opacity, null);\r\n    return this;\r\n  },\r\n\r\n  strokeOpacity(opacity) {\r\n    this._doOpacity(null, opacity);\r\n    return this;\r\n  },\r\n\r\n  _doOpacity(fillOpacity, strokeOpacity) {\r\n    let dictionary, name;\r\n    if (fillOpacity == null && strokeOpacity == null) {\r\n      return;\r\n    }\r\n\r\n    if (fillOpacity != null) {\r\n      fillOpacity = Math.max(0, Math.min(1, fillOpacity));\r\n    }\r\n    if (strokeOpacity != null) {\r\n      strokeOpacity = Math.max(0, Math.min(1, strokeOpacity));\r\n    }\r\n    const key = `${fillOpacity}_${strokeOpacity}`;\r\n\r\n    if (this._opacityRegistry[key]) {\r\n      [dictionary, name] = this._opacityRegistry[key];\r\n    } else {\r\n      dictionary = { Type: 'ExtGState' };\r\n\r\n      if (fillOpacity != null) {\r\n        dictionary.ca = fillOpacity;\r\n      }\r\n      if (strokeOpacity != null) {\r\n        dictionary.CA = strokeOpacity;\r\n      }\r\n\r\n      dictionary = this.ref(dictionary);\r\n      dictionary.end();\r\n      const id = ++this._opacityCount;\r\n      name = `Gs${id}`;\r\n      this._opacityRegistry[key] = [dictionary, name];\r\n    }\r\n\r\n    this.page.ext_gstates[name] = dictionary;\r\n    return this.addContent(`/${name} gs`);\r\n  },\r\n\r\n  linearGradient(x1, y1, x2, y2) {\r\n    return new PDFLinearGradient(this, x1, y1, x2, y2);\r\n  },\r\n\r\n  radialGradient(x1, y1, r1, x2, y2, r2) {\r\n    return new PDFRadialGradient(this, x1, y1, r1, x2, y2, r2);\r\n  },\r\n\r\n  pattern(bbox, xStep, yStep, stream) {\r\n    return new PDFTilingPattern(this, bbox, xStep, yStep, stream);\r\n  }\r\n};\r\n\r\nvar namedColors = {\r\n  aliceblue: [240, 248, 255],\r\n  antiquewhite: [250, 235, 215],\r\n  aqua: [0, 255, 255],\r\n  aquamarine: [127, 255, 212],\r\n  azure: [240, 255, 255],\r\n  beige: [245, 245, 220],\r\n  bisque: [255, 228, 196],\r\n  black: [0, 0, 0],\r\n  blanchedalmond: [255, 235, 205],\r\n  blue: [0, 0, 255],\r\n  blueviolet: [138, 43, 226],\r\n  brown: [165, 42, 42],\r\n  burlywood: [222, 184, 135],\r\n  cadetblue: [95, 158, 160],\r\n  chartreuse: [127, 255, 0],\r\n  chocolate: [210, 105, 30],\r\n  coral: [255, 127, 80],\r\n  cornflowerblue: [100, 149, 237],\r\n  cornsilk: [255, 248, 220],\r\n  crimson: [220, 20, 60],\r\n  cyan: [0, 255, 255],\r\n  darkblue: [0, 0, 139],\r\n  darkcyan: [0, 139, 139],\r\n  darkgoldenrod: [184, 134, 11],\r\n  darkgray: [169, 169, 169],\r\n  darkgreen: [0, 100, 0],\r\n  darkgrey: [169, 169, 169],\r\n  darkkhaki: [189, 183, 107],\r\n  darkmagenta: [139, 0, 139],\r\n  darkolivegreen: [85, 107, 47],\r\n  darkorange: [255, 140, 0],\r\n  darkorchid: [153, 50, 204],\r\n  darkred: [139, 0, 0],\r\n  darksalmon: [233, 150, 122],\r\n  darkseagreen: [143, 188, 143],\r\n  darkslateblue: [72, 61, 139],\r\n  darkslategray: [47, 79, 79],\r\n  darkslategrey: [47, 79, 79],\r\n  darkturquoise: [0, 206, 209],\r\n  darkviolet: [148, 0, 211],\r\n  deeppink: [255, 20, 147],\r\n  deepskyblue: [0, 191, 255],\r\n  dimgray: [105, 105, 105],\r\n  dimgrey: [105, 105, 105],\r\n  dodgerblue: [30, 144, 255],\r\n  firebrick: [178, 34, 34],\r\n  floralwhite: [255, 250, 240],\r\n  forestgreen: [34, 139, 34],\r\n  fuchsia: [255, 0, 255],\r\n  gainsboro: [220, 220, 220],\r\n  ghostwhite: [248, 248, 255],\r\n  gold: [255, 215, 0],\r\n  goldenrod: [218, 165, 32],\r\n  gray: [128, 128, 128],\r\n  grey: [128, 128, 128],\r\n  green: [0, 128, 0],\r\n  greenyellow: [173, 255, 47],\r\n  honeydew: [240, 255, 240],\r\n  hotpink: [255, 105, 180],\r\n  indianred: [205, 92, 92],\r\n  indigo: [75, 0, 130],\r\n  ivory: [255, 255, 240],\r\n  khaki: [240, 230, 140],\r\n  lavender: [230, 230, 250],\r\n  lavenderblush: [255, 240, 245],\r\n  lawngreen: [124, 252, 0],\r\n  lemonchiffon: [255, 250, 205],\r\n  lightblue: [173, 216, 230],\r\n  lightcoral: [240, 128, 128],\r\n  lightcyan: [224, 255, 255],\r\n  lightgoldenrodyellow: [250, 250, 210],\r\n  lightgray: [211, 211, 211],\r\n  lightgreen: [144, 238, 144],\r\n  lightgrey: [211, 211, 211],\r\n  lightpink: [255, 182, 193],\r\n  lightsalmon: [255, 160, 122],\r\n  lightseagreen: [32, 178, 170],\r\n  lightskyblue: [135, 206, 250],\r\n  lightslategray: [119, 136, 153],\r\n  lightslategrey: [119, 136, 153],\r\n  lightsteelblue: [176, 196, 222],\r\n  lightyellow: [255, 255, 224],\r\n  lime: [0, 255, 0],\r\n  limegreen: [50, 205, 50],\r\n  linen: [250, 240, 230],\r\n  magenta: [255, 0, 255],\r\n  maroon: [128, 0, 0],\r\n  mediumaquamarine: [102, 205, 170],\r\n  mediumblue: [0, 0, 205],\r\n  mediumorchid: [186, 85, 211],\r\n  mediumpurple: [147, 112, 219],\r\n  mediumseagreen: [60, 179, 113],\r\n  mediumslateblue: [123, 104, 238],\r\n  mediumspringgreen: [0, 250, 154],\r\n  mediumturquoise: [72, 209, 204],\r\n  mediumvioletred: [199, 21, 133],\r\n  midnightblue: [25, 25, 112],\r\n  mintcream: [245, 255, 250],\r\n  mistyrose: [255, 228, 225],\r\n  moccasin: [255, 228, 181],\r\n  navajowhite: [255, 222, 173],\r\n  navy: [0, 0, 128],\r\n  oldlace: [253, 245, 230],\r\n  olive: [128, 128, 0],\r\n  olivedrab: [107, 142, 35],\r\n  orange: [255, 165, 0],\r\n  orangered: [255, 69, 0],\r\n  orchid: [218, 112, 214],\r\n  palegoldenrod: [238, 232, 170],\r\n  palegreen: [152, 251, 152],\r\n  paleturquoise: [175, 238, 238],\r\n  palevioletred: [219, 112, 147],\r\n  papayawhip: [255, 239, 213],\r\n  peachpuff: [255, 218, 185],\r\n  peru: [205, 133, 63],\r\n  pink: [255, 192, 203],\r\n  plum: [221, 160, 221],\r\n  powderblue: [176, 224, 230],\r\n  purple: [128, 0, 128],\r\n  red: [255, 0, 0],\r\n  rosybrown: [188, 143, 143],\r\n  royalblue: [65, 105, 225],\r\n  saddlebrown: [139, 69, 19],\r\n  salmon: [250, 128, 114],\r\n  sandybrown: [244, 164, 96],\r\n  seagreen: [46, 139, 87],\r\n  seashell: [255, 245, 238],\r\n  sienna: [160, 82, 45],\r\n  silver: [192, 192, 192],\r\n  skyblue: [135, 206, 235],\r\n  slateblue: [106, 90, 205],\r\n  slategray: [112, 128, 144],\r\n  slategrey: [112, 128, 144],\r\n  snow: [255, 250, 250],\r\n  springgreen: [0, 255, 127],\r\n  steelblue: [70, 130, 180],\r\n  tan: [210, 180, 140],\r\n  teal: [0, 128, 128],\r\n  thistle: [216, 191, 216],\r\n  tomato: [255, 99, 71],\r\n  turquoise: [64, 224, 208],\r\n  violet: [238, 130, 238],\r\n  wheat: [245, 222, 179],\r\n  white: [255, 255, 255],\r\n  whitesmoke: [245, 245, 245],\r\n  yellow: [255, 255, 0],\r\n  yellowgreen: [154, 205, 50]\r\n};\r\n", "let cx, cy, px, py, sx, sy;\r\n\r\ncx = cy = px = py = sx = sy = 0;\r\n\r\nconst parameters = {\r\n  A: 7,\r\n  a: 7,\r\n  C: 6,\r\n  c: 6,\r\n  H: 1,\r\n  h: 1,\r\n  L: 2,\r\n  l: 2,\r\n  M: 2,\r\n  m: 2,\r\n  Q: 4,\r\n  q: 4,\r\n  S: 4,\r\n  s: 4,\r\n  T: 2,\r\n  t: 2,\r\n  V: 1,\r\n  v: 1,\r\n  Z: 0,\r\n  z: 0\r\n};\r\n\r\nconst parse = function(path) {\r\n  let cmd;\r\n  const ret = [];\r\n  let args = [];\r\n  let curArg = '';\r\n  let foundDecimal = false;\r\n  let params = 0;\r\n\r\n  for (let c of path) {\r\n    if (parameters[c] != null) {\r\n      params = parameters[c];\r\n      if (cmd) {\r\n        // save existing command\r\n        if (curArg.length > 0) {\r\n          args[args.length] = +curArg;\r\n        }\r\n        ret[ret.length] = { cmd, args };\r\n\r\n        args = [];\r\n        curArg = '';\r\n        foundDecimal = false;\r\n      }\r\n\r\n      cmd = c;\r\n    } else if (\r\n      [' ', ','].includes(c) ||\r\n      (c === '-' && curArg.length > 0 && curArg[curArg.length - 1] !== 'e') ||\r\n      (c === '.' && foundDecimal)\r\n    ) {\r\n      if (curArg.length === 0) {\r\n        continue;\r\n      }\r\n\r\n      if (args.length === params) {\r\n        // handle reused commands\r\n        ret[ret.length] = { cmd, args };\r\n        args = [+curArg];\r\n\r\n        // handle assumed commands\r\n        if (cmd === 'M') {\r\n          cmd = 'L';\r\n        }\r\n        if (cmd === 'm') {\r\n          cmd = 'l';\r\n        }\r\n      } else {\r\n        args[args.length] = +curArg;\r\n      }\r\n\r\n      foundDecimal = c === '.';\r\n\r\n      // fix for negative numbers or repeated decimals with no delimeter between commands\r\n      curArg = ['-', '.'].includes(c) ? c : '';\r\n    } else {\r\n      curArg += c;\r\n      if (c === '.') {\r\n        foundDecimal = true;\r\n      }\r\n    }\r\n  }\r\n\r\n  // add the last command\r\n  if (curArg.length > 0) {\r\n    if (args.length === params) {\r\n      // handle reused commands\r\n      ret[ret.length] = { cmd, args };\r\n      args = [+curArg];\r\n\r\n      // handle assumed commands\r\n      if (cmd === 'M') {\r\n        cmd = 'L';\r\n      }\r\n      if (cmd === 'm') {\r\n        cmd = 'l';\r\n      }\r\n    } else {\r\n      args[args.length] = +curArg;\r\n    }\r\n  }\r\n\r\n  ret[ret.length] = { cmd, args };\r\n\r\n  return ret;\r\n};\r\n\r\nconst apply = function(commands, doc) {\r\n  // current point, control point, and subpath starting point\r\n  cx = cy = px = py = sx = sy = 0;\r\n\r\n  // run the commands\r\n  for (let i = 0; i < commands.length; i++) {\r\n    const c = commands[i];\r\n    if (typeof runners[c.cmd] === 'function') {\r\n      runners[c.cmd](doc, c.args);\r\n    }\r\n  }\r\n};\r\n\r\nconst runners = {\r\n  M(doc, a) {\r\n    cx = a[0];\r\n    cy = a[1];\r\n    px = py = null;\r\n    sx = cx;\r\n    sy = cy;\r\n    return doc.moveTo(cx, cy);\r\n  },\r\n\r\n  m(doc, a) {\r\n    cx += a[0];\r\n    cy += a[1];\r\n    px = py = null;\r\n    sx = cx;\r\n    sy = cy;\r\n    return doc.moveTo(cx, cy);\r\n  },\r\n\r\n  C(doc, a) {\r\n    cx = a[4];\r\n    cy = a[5];\r\n    px = a[2];\r\n    py = a[3];\r\n    return doc.bezierCurveTo(...a);\r\n  },\r\n\r\n  c(doc, a) {\r\n    doc.bezierCurveTo(\r\n      a[0] + cx,\r\n      a[1] + cy,\r\n      a[2] + cx,\r\n      a[3] + cy,\r\n      a[4] + cx,\r\n      a[5] + cy\r\n    );\r\n    px = cx + a[2];\r\n    py = cy + a[3];\r\n    cx += a[4];\r\n    return (cy += a[5]);\r\n  },\r\n\r\n  S(doc, a) {\r\n    if (px === null) {\r\n      px = cx;\r\n      py = cy;\r\n    }\r\n\r\n    doc.bezierCurveTo(cx - (px - cx), cy - (py - cy), a[0], a[1], a[2], a[3]);\r\n    px = a[0];\r\n    py = a[1];\r\n    cx = a[2];\r\n    return (cy = a[3]);\r\n  },\r\n\r\n  s(doc, a) {\r\n    if (px === null) {\r\n      px = cx;\r\n      py = cy;\r\n    }\r\n\r\n    doc.bezierCurveTo(\r\n      cx - (px - cx),\r\n      cy - (py - cy),\r\n      cx + a[0],\r\n      cy + a[1],\r\n      cx + a[2],\r\n      cy + a[3]\r\n    );\r\n    px = cx + a[0];\r\n    py = cy + a[1];\r\n    cx += a[2];\r\n    return (cy += a[3]);\r\n  },\r\n\r\n  Q(doc, a) {\r\n    px = a[0];\r\n    py = a[1];\r\n    cx = a[2];\r\n    cy = a[3];\r\n    return doc.quadraticCurveTo(a[0], a[1], cx, cy);\r\n  },\r\n\r\n  q(doc, a) {\r\n    doc.quadraticCurveTo(a[0] + cx, a[1] + cy, a[2] + cx, a[3] + cy);\r\n    px = cx + a[0];\r\n    py = cy + a[1];\r\n    cx += a[2];\r\n    return (cy += a[3]);\r\n  },\r\n\r\n  T(doc, a) {\r\n    if (px === null) {\r\n      px = cx;\r\n      py = cy;\r\n    } else {\r\n      px = cx - (px - cx);\r\n      py = cy - (py - cy);\r\n    }\r\n\r\n    doc.quadraticCurveTo(px, py, a[0], a[1]);\r\n    px = cx - (px - cx);\r\n    py = cy - (py - cy);\r\n    cx = a[0];\r\n    return (cy = a[1]);\r\n  },\r\n\r\n  t(doc, a) {\r\n    if (px === null) {\r\n      px = cx;\r\n      py = cy;\r\n    } else {\r\n      px = cx - (px - cx);\r\n      py = cy - (py - cy);\r\n    }\r\n\r\n    doc.quadraticCurveTo(px, py, cx + a[0], cy + a[1]);\r\n    cx += a[0];\r\n    return (cy += a[1]);\r\n  },\r\n\r\n  A(doc, a) {\r\n    solveArc(doc, cx, cy, a);\r\n    cx = a[5];\r\n    return (cy = a[6]);\r\n  },\r\n\r\n  a(doc, a) {\r\n    a[5] += cx;\r\n    a[6] += cy;\r\n    solveArc(doc, cx, cy, a);\r\n    cx = a[5];\r\n    return (cy = a[6]);\r\n  },\r\n\r\n  L(doc, a) {\r\n    cx = a[0];\r\n    cy = a[1];\r\n    px = py = null;\r\n    return doc.lineTo(cx, cy);\r\n  },\r\n\r\n  l(doc, a) {\r\n    cx += a[0];\r\n    cy += a[1];\r\n    px = py = null;\r\n    return doc.lineTo(cx, cy);\r\n  },\r\n\r\n  H(doc, a) {\r\n    cx = a[0];\r\n    px = py = null;\r\n    return doc.lineTo(cx, cy);\r\n  },\r\n\r\n  h(doc, a) {\r\n    cx += a[0];\r\n    px = py = null;\r\n    return doc.lineTo(cx, cy);\r\n  },\r\n\r\n  V(doc, a) {\r\n    cy = a[0];\r\n    px = py = null;\r\n    return doc.lineTo(cx, cy);\r\n  },\r\n\r\n  v(doc, a) {\r\n    cy += a[0];\r\n    px = py = null;\r\n    return doc.lineTo(cx, cy);\r\n  },\r\n\r\n  Z(doc) {\r\n    doc.closePath();\r\n    cx = sx;\r\n    return (cy = sy);\r\n  },\r\n\r\n  z(doc) {\r\n    doc.closePath();\r\n    cx = sx;\r\n    return (cy = sy);\r\n  }\r\n};\r\n\r\nconst solveArc = function(doc, x, y, coords) {\r\n  const [rx, ry, rot, large, sweep, ex, ey] = coords;\r\n  const segs = arcToSegments(ex, ey, rx, ry, large, sweep, rot, x, y);\r\n\r\n  for (let seg of segs) {\r\n    const bez = segmentToBezier(...seg);\r\n    doc.bezierCurveTo(...bez);\r\n  }\r\n};\r\n\r\n// from Inkscape svgtopdf, thanks!\r\nconst arcToSegments = function(x, y, rx, ry, large, sweep, rotateX, ox, oy) {\r\n  const th = rotateX * (Math.PI / 180);\r\n  const sin_th = Math.sin(th);\r\n  const cos_th = Math.cos(th);\r\n  rx = Math.abs(rx);\r\n  ry = Math.abs(ry);\r\n  px = cos_th * (ox - x) * 0.5 + sin_th * (oy - y) * 0.5;\r\n  py = cos_th * (oy - y) * 0.5 - sin_th * (ox - x) * 0.5;\r\n  let pl = (px * px) / (rx * rx) + (py * py) / (ry * ry);\r\n  if (pl > 1) {\r\n    pl = Math.sqrt(pl);\r\n    rx *= pl;\r\n    ry *= pl;\r\n  }\r\n\r\n  const a00 = cos_th / rx;\r\n  const a01 = sin_th / rx;\r\n  const a10 = -sin_th / ry;\r\n  const a11 = cos_th / ry;\r\n  const x0 = a00 * ox + a01 * oy;\r\n  const y0 = a10 * ox + a11 * oy;\r\n  const x1 = a00 * x + a01 * y;\r\n  const y1 = a10 * x + a11 * y;\r\n\r\n  const d = (x1 - x0) * (x1 - x0) + (y1 - y0) * (y1 - y0);\r\n  let sfactor_sq = 1 / d - 0.25;\r\n  if (sfactor_sq < 0) {\r\n    sfactor_sq = 0;\r\n  }\r\n  let sfactor = Math.sqrt(sfactor_sq);\r\n  if (sweep === large) {\r\n    sfactor = -sfactor;\r\n  }\r\n\r\n  const xc = 0.5 * (x0 + x1) - sfactor * (y1 - y0);\r\n  const yc = 0.5 * (y0 + y1) + sfactor * (x1 - x0);\r\n\r\n  const th0 = Math.atan2(y0 - yc, x0 - xc);\r\n  const th1 = Math.atan2(y1 - yc, x1 - xc);\r\n\r\n  let th_arc = th1 - th0;\r\n  if (th_arc < 0 && sweep === 1) {\r\n    th_arc += 2 * Math.PI;\r\n  } else if (th_arc > 0 && sweep === 0) {\r\n    th_arc -= 2 * Math.PI;\r\n  }\r\n\r\n  const segments = Math.ceil(Math.abs(th_arc / (Math.PI * 0.5 + 0.001)));\r\n  const result = [];\r\n\r\n  for (let i = 0; i < segments; i++) {\r\n    const th2 = th0 + (i * th_arc) / segments;\r\n    const th3 = th0 + ((i + 1) * th_arc) / segments;\r\n    result[i] = [xc, yc, th2, th3, rx, ry, sin_th, cos_th];\r\n  }\r\n\r\n  return result;\r\n};\r\n\r\nconst segmentToBezier = function(cx, cy, th0, th1, rx, ry, sin_th, cos_th) {\r\n  const a00 = cos_th * rx;\r\n  const a01 = -sin_th * ry;\r\n  const a10 = sin_th * rx;\r\n  const a11 = cos_th * ry;\r\n\r\n  const th_half = 0.5 * (th1 - th0);\r\n  const t =\r\n    ((8 / 3) * Math.sin(th_half * 0.5) * Math.sin(th_half * 0.5)) /\r\n    Math.sin(th_half);\r\n  const x1 = cx + Math.cos(th0) - t * Math.sin(th0);\r\n  const y1 = cy + Math.sin(th0) + t * Math.cos(th0);\r\n  const x3 = cx + Math.cos(th1);\r\n  const y3 = cy + Math.sin(th1);\r\n  const x2 = x3 + t * Math.sin(th1);\r\n  const y2 = y3 - t * Math.cos(th1);\r\n\r\n  return [\r\n    a00 * x1 + a01 * y1,\r\n    a10 * x1 + a11 * y1,\r\n    a00 * x2 + a01 * y2,\r\n    a10 * x2 + a11 * y2,\r\n    a00 * x3 + a01 * y3,\r\n    a10 * x3 + a11 * y3\r\n  ];\r\n};\r\n\r\nclass SVGPath {\r\n  static apply(doc, path) {\r\n    const commands = parse(path);\r\n    apply(commands, doc);\r\n  }\r\n}\r\n\r\nexport default SVGPath;\r\n", "import SVGPath from '../path';\r\nimport PDFObject from '../object';\r\n\r\nconst { number } = PDFObject;\r\n\r\n// This constant is used to approximate a symmetrical arc using a cubic\r\n// Bezier curve.\r\nconst KAPPA = 4.0 * ((Math.sqrt(2) - 1.0) / 3.0);\r\nexport default {\r\n  initVector() {\r\n    this._ctm = [1, 0, 0, 1, 0, 0]; // current transformation matrix\r\n    return (this._ctmStack = []);\r\n  },\r\n\r\n  save() {\r\n    this._ctmStack.push(this._ctm.slice());\r\n    // TODO: save/restore colorspace and styles so not setting it unnessesarily all the time?\r\n    return this.addContent('q');\r\n  },\r\n\r\n  restore() {\r\n    this._ctm = this._ctmStack.pop() || [1, 0, 0, 1, 0, 0];\r\n    return this.addContent('Q');\r\n  },\r\n\r\n  closePath() {\r\n    return this.addContent('h');\r\n  },\r\n\r\n  lineWidth(w) {\r\n    return this.addContent(`${number(w)} w`);\r\n  },\r\n\r\n  _CAP_STYLES: {\r\n    BUTT: 0,\r\n    ROUND: 1,\r\n    SQUARE: 2\r\n  },\r\n\r\n  lineCap(c) {\r\n    if (typeof c === 'string') {\r\n      c = this._CAP_STYLES[c.toUpperCase()];\r\n    }\r\n    return this.addContent(`${c} J`);\r\n  },\r\n\r\n  _JOIN_STYLES: {\r\n    MITER: 0,\r\n    ROUND: 1,\r\n    BEVEL: 2\r\n  },\r\n\r\n  lineJoin(j) {\r\n    if (typeof j === 'string') {\r\n      j = this._JOIN_STYLES[j.toUpperCase()];\r\n    }\r\n    return this.addContent(`${j} j`);\r\n  },\r\n\r\n  miterLimit(m) {\r\n    return this.addContent(`${number(m)} M`);\r\n  },\r\n\r\n  dash(length, options = {}) {\r\n    const originalLength = length;\r\n    if (!Array.isArray(length)) {\r\n      length = [length, options.space || length];\r\n    }\r\n\r\n    const valid = length.every(x => Number.isFinite(x) && x > 0);\r\n    if (!valid) {\r\n      throw new Error(\r\n        `dash(${JSON.stringify(originalLength)}, ${JSON.stringify(\r\n          options\r\n        )}) invalid, lengths must be numeric and greater than zero`\r\n      );\r\n    }\r\n\r\n    length = length.map(number).join(' ');\r\n    return this.addContent(`[${length}] ${number(options.phase || 0)} d`);\r\n  },\r\n\r\n  undash() {\r\n    return this.addContent('[] 0 d');\r\n  },\r\n\r\n  moveTo(x, y) {\r\n    return this.addContent(`${number(x)} ${number(y)} m`);\r\n  },\r\n\r\n  lineTo(x, y) {\r\n    return this.addContent(`${number(x)} ${number(y)} l`);\r\n  },\r\n\r\n  bezierCurveTo(cp1x, cp1y, cp2x, cp2y, x, y) {\r\n    return this.addContent(\r\n      `${number(cp1x)} ${number(cp1y)} ${number(cp2x)} ${number(cp2y)} ${number(\r\n        x\r\n      )} ${number(y)} c`\r\n    );\r\n  },\r\n\r\n  quadraticCurveTo(cpx, cpy, x, y) {\r\n    return this.addContent(\r\n      `${number(cpx)} ${number(cpy)} ${number(x)} ${number(y)} v`\r\n    );\r\n  },\r\n\r\n  rect(x, y, w, h) {\r\n    return this.addContent(\r\n      `${number(x)} ${number(y)} ${number(w)} ${number(h)} re`\r\n    );\r\n  },\r\n\r\n  roundedRect(x, y, w, h, r) {\r\n    if (r == null) {\r\n      r = 0;\r\n    }\r\n    r = Math.min(r, 0.5 * w, 0.5 * h);\r\n\r\n    // amount to inset control points from corners (see `ellipse`)\r\n    const c = r * (1.0 - KAPPA);\r\n\r\n    this.moveTo(x + r, y);\r\n    this.lineTo(x + w - r, y);\r\n    this.bezierCurveTo(x + w - c, y, x + w, y + c, x + w, y + r);\r\n    this.lineTo(x + w, y + h - r);\r\n    this.bezierCurveTo(x + w, y + h - c, x + w - c, y + h, x + w - r, y + h);\r\n    this.lineTo(x + r, y + h);\r\n    this.bezierCurveTo(x + c, y + h, x, y + h - c, x, y + h - r);\r\n    this.lineTo(x, y + r);\r\n    this.bezierCurveTo(x, y + c, x + c, y, x + r, y);\r\n    return this.closePath();\r\n  },\r\n\r\n  ellipse(x, y, r1, r2) {\r\n    // based on http://stackoverflow.com/questions/2172798/how-to-draw-an-oval-in-html5-canvas/2173084#2173084\r\n    if (r2 == null) {\r\n      r2 = r1;\r\n    }\r\n    x -= r1;\r\n    y -= r2;\r\n    const ox = r1 * KAPPA;\r\n    const oy = r2 * KAPPA;\r\n    const xe = x + r1 * 2;\r\n    const ye = y + r2 * 2;\r\n    const xm = x + r1;\r\n    const ym = y + r2;\r\n\r\n    this.moveTo(x, ym);\r\n    this.bezierCurveTo(x, ym - oy, xm - ox, y, xm, y);\r\n    this.bezierCurveTo(xm + ox, y, xe, ym - oy, xe, ym);\r\n    this.bezierCurveTo(xe, ym + oy, xm + ox, ye, xm, ye);\r\n    this.bezierCurveTo(xm - ox, ye, x, ym + oy, x, ym);\r\n    return this.closePath();\r\n  },\r\n\r\n  circle(x, y, radius) {\r\n    return this.ellipse(x, y, radius);\r\n  },\r\n\r\n  arc(x, y, radius, startAngle, endAngle, anticlockwise) {\r\n    if (anticlockwise == null) {\r\n      anticlockwise = false;\r\n    }\r\n    const TWO_PI = 2.0 * Math.PI;\r\n    const HALF_PI = 0.5 * Math.PI;\r\n\r\n    let deltaAng = endAngle - startAngle;\r\n\r\n    if (Math.abs(deltaAng) > TWO_PI) {\r\n      // draw only full circle if more than that is specified\r\n      deltaAng = TWO_PI;\r\n    } else if (deltaAng !== 0 && anticlockwise !== deltaAng < 0) {\r\n      // necessary to flip direction of rendering\r\n      const dir = anticlockwise ? -1 : 1;\r\n      deltaAng = dir * TWO_PI + deltaAng;\r\n    }\r\n\r\n    const numSegs = Math.ceil(Math.abs(deltaAng) / HALF_PI);\r\n    const segAng = deltaAng / numSegs;\r\n    const handleLen = (segAng / HALF_PI) * KAPPA * radius;\r\n    let curAng = startAngle;\r\n\r\n    // component distances between anchor point and control point\r\n    let deltaCx = -Math.sin(curAng) * handleLen;\r\n    let deltaCy = Math.cos(curAng) * handleLen;\r\n\r\n    // anchor point\r\n    let ax = x + Math.cos(curAng) * radius;\r\n    let ay = y + Math.sin(curAng) * radius;\r\n\r\n    // calculate and render segments\r\n    this.moveTo(ax, ay);\r\n\r\n    for (let segIdx = 0; segIdx < numSegs; segIdx++) {\r\n      // starting control point\r\n      const cp1x = ax + deltaCx;\r\n      const cp1y = ay + deltaCy;\r\n\r\n      // step angle\r\n      curAng += segAng;\r\n\r\n      // next anchor point\r\n      ax = x + Math.cos(curAng) * radius;\r\n      ay = y + Math.sin(curAng) * radius;\r\n\r\n      // next control point delta\r\n      deltaCx = -Math.sin(curAng) * handleLen;\r\n      deltaCy = Math.cos(curAng) * handleLen;\r\n\r\n      // ending control point\r\n      const cp2x = ax - deltaCx;\r\n      const cp2y = ay - deltaCy;\r\n\r\n      // render segment\r\n      this.bezierCurveTo(cp1x, cp1y, cp2x, cp2y, ax, ay);\r\n    }\r\n\r\n    return this;\r\n  },\r\n\r\n  polygon(...points) {\r\n    this.moveTo(...(points.shift() || []));\r\n    for (let point of points) {\r\n      this.lineTo(...(point || []));\r\n    }\r\n    return this.closePath();\r\n  },\r\n\r\n  path(path) {\r\n    SVGPath.apply(this, path);\r\n    return this;\r\n  },\r\n\r\n  _windingRule(rule) {\r\n    if (/even-?odd/.test(rule)) {\r\n      return '*';\r\n    }\r\n\r\n    return '';\r\n  },\r\n\r\n  fill(color, rule) {\r\n    if (/(even-?odd)|(non-?zero)/.test(color)) {\r\n      rule = color;\r\n      color = null;\r\n    }\r\n\r\n    if (color) {\r\n      this.fillColor(color);\r\n    }\r\n    return this.addContent(`f${this._windingRule(rule)}`);\r\n  },\r\n\r\n  stroke(color) {\r\n    if (color) {\r\n      this.strokeColor(color);\r\n    }\r\n    return this.addContent('S');\r\n  },\r\n\r\n  fillAndStroke(fillColor, strokeColor, rule) {\r\n    if (strokeColor == null) {\r\n      strokeColor = fillColor;\r\n    }\r\n    const isFillRule = /(even-?odd)|(non-?zero)/;\r\n    if (isFillRule.test(fillColor)) {\r\n      rule = fillColor;\r\n      fillColor = null;\r\n    }\r\n\r\n    if (isFillRule.test(strokeColor)) {\r\n      rule = strokeColor;\r\n      strokeColor = fillColor;\r\n    }\r\n\r\n    if (fillColor) {\r\n      this.fillColor(fillColor);\r\n      this.strokeColor(strokeColor);\r\n    }\r\n\r\n    return this.addContent(`B${this._windingRule(rule)}`);\r\n  },\r\n\r\n  clip(rule) {\r\n    return this.addContent(`W${this._windingRule(rule)} n`);\r\n  },\r\n\r\n  transform(m11, m12, m21, m22, dx, dy) {\r\n    // keep track of the current transformation matrix\r\n    if (m11 === 1 && m12 === 0 && m21 === 0 && m22 === 1 && dx === 0 && dy === 0) {\r\n      // Ignore identity transforms\r\n      return this;\r\n    }\r\n    const m = this._ctm;\r\n    const [m0, m1, m2, m3, m4, m5] = m;\r\n    m[0] = m0 * m11 + m2 * m12;\r\n    m[1] = m1 * m11 + m3 * m12;\r\n    m[2] = m0 * m21 + m2 * m22;\r\n    m[3] = m1 * m21 + m3 * m22;\r\n    m[4] = m0 * dx + m2 * dy + m4;\r\n    m[5] = m1 * dx + m3 * dy + m5;\r\n\r\n    const values = [m11, m12, m21, m22, dx, dy].map(v => number(v)).join(' ');\r\n    return this.addContent(`${values} cm`);\r\n  },\r\n\r\n  translate(x, y) {\r\n    return this.transform(1, 0, 0, 1, x, y);\r\n  },\r\n\r\n  rotate(angle, options = {}) {\r\n    let y;\r\n    const rad = (angle * Math.PI) / 180;\r\n    const cos = Math.cos(rad);\r\n    const sin = Math.sin(rad);\r\n    let x = (y = 0);\r\n\r\n    if (options.origin != null) {\r\n      [x, y] = options.origin;\r\n      const x1 = x * cos - y * sin;\r\n      const y1 = x * sin + y * cos;\r\n      x -= x1;\r\n      y -= y1;\r\n    }\r\n\r\n    return this.transform(cos, sin, -sin, cos, x, y);\r\n  },\r\n\r\n  scale(xFactor, yFactor, options = {}) {\r\n    let y;\r\n    if (yFactor == null) {\r\n      yFactor = xFactor;\r\n    }\r\n    if (typeof yFactor === 'object') {\r\n      options = yFactor;\r\n      yFactor = xFactor;\r\n    }\r\n\r\n    let x = (y = 0);\r\n    if (options.origin != null) {\r\n      [x, y] = options.origin;\r\n      x -= xFactor * x;\r\n      y -= yFactor * y;\r\n    }\r\n\r\n    return this.transform(xFactor, 0, 0, yFactor, x, y);\r\n  }\r\n};\r\n", "import fs from 'fs';\r\n\r\nconst WIN_ANSI_MAP = {\r\n  402: 131,\r\n  8211: 150,\r\n  8212: 151,\r\n  8216: 145,\r\n  8217: 146,\r\n  8218: 130,\r\n  8220: 147,\r\n  8221: 148,\r\n  8222: 132,\r\n  8224: 134,\r\n  8225: 135,\r\n  8226: 149,\r\n  8230: 133,\r\n  8364: 128,\r\n  8240: 137,\r\n  8249: 139,\r\n  8250: 155,\r\n  710: 136,\r\n  8482: 153,\r\n  338: 140,\r\n  339: 156,\r\n  732: 152,\r\n  352: 138,\r\n  353: 154,\r\n  376: 159,\r\n  381: 142,\r\n  382: 158\r\n};\r\n\r\nconst characters = `\\\r\n.notdef       .notdef        .notdef        .notdef\r\n.notdef       .notdef        .notdef        .notdef\r\n.notdef       .notdef        .notdef        .notdef\r\n.notdef       .notdef        .notdef        .notdef\r\n.notdef       .notdef        .notdef        .notdef\r\n.notdef       .notdef        .notdef        .notdef\r\n.notdef       .notdef        .notdef        .notdef\r\n.notdef       .notdef        .notdef        .notdef\r\n  \r\nspace         exclam         quotedbl       numbersign\r\ndollar        percent        ampersand      quotesingle\r\nparenleft     parenright     asterisk       plus\r\ncomma         hyphen         period         slash\r\nzero          one            two            three\r\nfour          five           six            seven\r\neight         nine           colon          semicolon\r\nless          equal          greater        question\r\n  \r\nat            A              B              C\r\nD             E              F              G\r\nH             I              J              K\r\nL             M              N              O\r\nP             Q              R              S\r\nT             U              V              W\r\nX             Y              Z              bracketleft\r\nbackslash     bracketright   asciicircum    underscore\r\n  \r\ngrave         a              b              c\r\nd             e              f              g\r\nh             i              j              k\r\nl             m              n              o\r\np             q              r              s\r\nt             u              v              w\r\nx             y              z              braceleft\r\nbar           braceright     asciitilde     .notdef\r\n  \r\nEuro          .notdef        quotesinglbase florin\r\nquotedblbase  ellipsis       dagger         daggerdbl\r\ncircumflex    perthousand    Scaron         guilsinglleft\r\nOE            .notdef        Zcaron         .notdef\r\n.notdef       quoteleft      quoteright     quotedblleft\r\nquotedblright bullet         endash         emdash\r\ntilde         trademark      scaron         guilsinglright\r\noe            .notdef        zcaron         ydieresis\r\n  \r\nspace         exclamdown     cent           sterling\r\ncurrency      yen            brokenbar      section\r\ndieresis      copyright      ordfeminine    guillemotleft\r\nlogicalnot    hyphen         registered     macron\r\ndegree        plusminus      twosuperior    threesuperior\r\nacute         mu             paragraph      periodcentered\r\ncedilla       onesuperior    ordmasculine   guillemotright\r\nonequarter    onehalf        threequarters  questiondown\r\n  \r\nAgrave        Aacute         Acircumflex    Atilde\r\nAdieresis     Aring          AE             Ccedilla\r\nEgrave        Eacute         Ecircumflex    Edieresis\r\nIgrave        Iacute         Icircumflex    Idieresis\r\nEth           Ntilde         Ograve         Oacute\r\nOcircumflex   Otilde         Odieresis      multiply\r\nOslash        Ugrave         Uacute         Ucircumflex\r\nUdieresis     Yacute         Thorn          germandbls\r\n  \r\nagrave        aacute         acircumflex    atilde\r\nadieresis     aring          ae             ccedilla\r\negrave        eacute         ecircumflex    edieresis\r\nigrave        iacute         icircumflex    idieresis\r\neth           ntilde         ograve         oacute\r\nocircumflex   otilde         odieresis      divide\r\noslash        ugrave         uacute         ucircumflex\r\nudieresis     yacute         thorn          ydieresis\\\r\n`.split(/\\s+/);\r\n\r\nclass AFMFont {\r\n  static open(filename) {\r\n    return new AFMFont(fs.readFileSync(filename, 'utf8'));\r\n  }\r\n\r\n  constructor(contents) {\r\n    this.contents = contents;\r\n    this.attributes = {};\r\n    this.glyphWidths = {};\r\n    this.boundingBoxes = {};\r\n    this.kernPairs = {};\r\n\r\n    this.parse();\r\n    // todo: remove charWidths since appears to not be used\r\n    this.charWidths = new Array(256);\r\n    for (let char = 0; char <= 255; char++) {\r\n      this.charWidths[char] = this.glyphWidths[characters[char]];\r\n    }\r\n\r\n    this.bbox = this.attributes['FontBBox'].split(/\\s+/).map(e => +e);\r\n    this.ascender = +(this.attributes['Ascender'] || 0);\r\n    this.descender = +(this.attributes['Descender'] || 0);\r\n    this.xHeight = +(this.attributes['XHeight'] || 0);\r\n    this.capHeight = +(this.attributes['CapHeight'] || 0);\r\n    this.lineGap =\r\n      this.bbox[3] - this.bbox[1] - (this.ascender - this.descender);\r\n  }\r\n\r\n  parse() {\r\n    let section = '';\r\n    for (let line of this.contents.split('\\n')) {\r\n      var match;\r\n      var a;\r\n      if ((match = line.match(/^Start(\\w+)/))) {\r\n        section = match[1];\r\n        continue;\r\n      } else if ((match = line.match(/^End(\\w+)/))) {\r\n        section = '';\r\n        continue;\r\n      }\r\n\r\n      switch (section) {\r\n        case 'FontMetrics':\r\n          match = line.match(/(^\\w+)\\s+(.*)/);\r\n          var key = match[1];\r\n          var value = match[2];\r\n\r\n          if ((a = this.attributes[key])) {\r\n            if (!Array.isArray(a)) {\r\n              a = this.attributes[key] = [a];\r\n            }\r\n            a.push(value);\r\n          } else {\r\n            this.attributes[key] = value;\r\n          }\r\n          break;\r\n\r\n        case 'CharMetrics':\r\n          if (!/^CH?\\s/.test(line)) {\r\n            continue;\r\n          }\r\n          var name = line.match(/\\bN\\s+(\\.?\\w+)\\s*;/)[1];\r\n          this.glyphWidths[name] = +line.match(/\\bWX\\s+(\\d+)\\s*;/)[1];\r\n          break;\r\n\r\n        case 'KernPairs':\r\n          match = line.match(/^KPX\\s+(\\.?\\w+)\\s+(\\.?\\w+)\\s+(-?\\d+)/);\r\n          if (match) {\r\n            this.kernPairs[match[1] + '\\0' + match[2]] = parseInt(match[3]);\r\n          }\r\n          break;\r\n      }\r\n    }\r\n  }\r\n\r\n  encodeText(text) {\r\n    const res = [];\r\n    for (let i = 0, len = text.length; i < len; i++) {\r\n      let char = text.charCodeAt(i);\r\n      char = WIN_ANSI_MAP[char] || char;\r\n      res.push(char.toString(16));\r\n    }\r\n\r\n    return res;\r\n  }\r\n\r\n  glyphsForString(string) {\r\n    const glyphs = [];\r\n\r\n    for (let i = 0, len = string.length; i < len; i++) {\r\n      const charCode = string.charCodeAt(i);\r\n      glyphs.push(this.characterToGlyph(charCode));\r\n    }\r\n\r\n    return glyphs;\r\n  }\r\n\r\n  characterToGlyph(character) {\r\n    return characters[WIN_ANSI_MAP[character] || character] || '.notdef';\r\n  }\r\n\r\n  widthOfGlyph(glyph) {\r\n    return this.glyphWidths[glyph] || 0;\r\n  }\r\n\r\n  getKernPair(left, right) {\r\n    return this.kernPairs[left + '\\0' + right] || 0;\r\n  }\r\n\r\n  advancesForGlyphs(glyphs) {\r\n    const advances = [];\r\n\r\n    for (let index = 0; index < glyphs.length; index++) {\r\n      const left = glyphs[index];\r\n      const right = glyphs[index + 1];\r\n      advances.push(this.widthOfGlyph(left) + this.getKernPair(left, right));\r\n    }\r\n\r\n    return advances;\r\n  }\r\n}\r\n\r\nexport default AFMFont;\r\n", "class PDFFont {\r\n  constructor() {}\r\n\r\n  encode() {\r\n    throw new Error('Must be implemented by subclasses');\r\n  }\r\n\r\n  widthOfString() {\r\n    throw new Error('Must be implemented by subclasses');\r\n  }\r\n\r\n  ref() {\r\n    return this.dictionary != null\r\n      ? this.dictionary\r\n      : (this.dictionary = this.document.ref());\r\n  }\r\n\r\n  finalize() {\r\n    if (this.embedded || this.dictionary == null) {\r\n      return;\r\n    }\r\n\r\n    this.embed();\r\n    return (this.embedded = true);\r\n  }\r\n\r\n  embed() {\r\n    throw new Error('Must be implemented by subclasses');\r\n  }\r\n\r\n  lineHeight(size, includeGap) {\r\n    if (includeGap == null) {\r\n      includeGap = false;\r\n    }\r\n    const gap = includeGap ? this.lineGap : 0;\r\n    return ((this.ascender + gap - this.descender) / 1000) * size;\r\n  }\r\n}\r\n\r\nexport default PDFFont;\r\n", "import AFMFont from './afm';\r\nimport PDFFont from '../font';\r\nimport fs from 'fs';\r\n\r\n// This insanity is so bundlers can inline the font files\r\nconst STANDARD_FONTS = {\r\n  Courier() {\r\n    return fs.readFileSync(__dirname + '/data/Courier.afm', 'utf8');\r\n  },\r\n  'Courier-Bold'() {\r\n    return fs.readFileSync(__dirname + '/data/Courier-Bold.afm', 'utf8');\r\n  },\r\n  'Courier-Oblique'() {\r\n    return fs.readFileSync(__dirname + '/data/Courier-Oblique.afm', 'utf8');\r\n  },\r\n  'Courier-BoldOblique'() {\r\n    return fs.readFileSync(__dirname + '/data/Courier-BoldOblique.afm', 'utf8');\r\n  },\r\n  Helvetica() {\r\n    return fs.readFileSync(__dirname + '/data/Helvetica.afm', 'utf8');\r\n  },\r\n  'Helvetica-Bold'() {\r\n    return fs.readFileSync(__dirname + '/data/Helvetica-Bold.afm', 'utf8');\r\n  },\r\n  'Helvetica-Oblique'() {\r\n    return fs.readFileSync(__dirname + '/data/Helvetica-Oblique.afm', 'utf8');\r\n  },\r\n  'Helvetica-BoldOblique'() {\r\n    return fs.readFileSync(\r\n      __dirname + '/data/Helvetica-BoldOblique.afm',\r\n      'utf8'\r\n    );\r\n  },\r\n  'Times-Roman'() {\r\n    return fs.readFileSync(__dirname + '/data/Times-Roman.afm', 'utf8');\r\n  },\r\n  'Times-Bold'() {\r\n    return fs.readFileSync(__dirname + '/data/Times-Bold.afm', 'utf8');\r\n  },\r\n  'Times-Italic'() {\r\n    return fs.readFileSync(__dirname + '/data/Times-Italic.afm', 'utf8');\r\n  },\r\n  'Times-BoldItalic'() {\r\n    return fs.readFileSync(__dirname + '/data/Times-BoldItalic.afm', 'utf8');\r\n  },\r\n  Symbol() {\r\n    return fs.readFileSync(__dirname + '/data/Symbol.afm', 'utf8');\r\n  },\r\n  ZapfDingbats() {\r\n    return fs.readFileSync(__dirname + '/data/ZapfDingbats.afm', 'utf8');\r\n  }\r\n};\r\n\r\nclass StandardFont extends PDFFont {\r\n  constructor(document, name, id) {\r\n    super();\r\n    this.document = document;\r\n    this.name = name;\r\n    this.id = id;\r\n    this.font = new AFMFont(STANDARD_FONTS[this.name]());\r\n    ({\r\n      ascender: this.ascender,\r\n      descender: this.descender,\r\n      bbox: this.bbox,\r\n      lineGap: this.lineGap,\r\n      xHeight: this.xHeight,\r\n      capHeight: this.capHeight\r\n    } = this.font);\r\n  }\r\n\r\n  embed() {\r\n    this.dictionary.data = {\r\n      Type: 'Font',\r\n      BaseFont: this.name,\r\n      Subtype: 'Type1',\r\n      Encoding: 'WinAnsiEncoding'\r\n    };\r\n\r\n    return this.dictionary.end();\r\n  }\r\n\r\n  encode(text) {\r\n    const encoded = this.font.encodeText(text);\r\n    const glyphs = this.font.glyphsForString(`${text}`);\r\n    const advances = this.font.advancesForGlyphs(glyphs);\r\n    const positions = [];\r\n    for (let i = 0; i < glyphs.length; i++) {\r\n      const glyph = glyphs[i];\r\n      positions.push({\r\n        xAdvance: advances[i],\r\n        yAdvance: 0,\r\n        xOffset: 0,\r\n        yOffset: 0,\r\n        advanceWidth: this.font.widthOfGlyph(glyph)\r\n      });\r\n    }\r\n\r\n    return [encoded, positions];\r\n  }\r\n\r\n  widthOfString(string, size) {\r\n    const glyphs = this.font.glyphsForString(`${string}`);\r\n    const advances = this.font.advancesForGlyphs(glyphs);\r\n\r\n    let width = 0;\r\n    for (let advance of advances) {\r\n      width += advance;\r\n    }\r\n\r\n    const scale = size / 1000;\r\n    return width * scale;\r\n  }\r\n\r\n  static isStandardFont(name) {\r\n    return name in STANDARD_FONTS;\r\n  }\r\n}\r\n\r\nexport default StandardFont;\r\n", "import PDFFont from '../font';\r\n\r\nconst toHex = function(num) {\r\n  return `0000${num.toString(16)}`.slice(-4);\r\n};\r\n\r\nclass EmbeddedFont extends PDFFont {\r\n  constructor(document, font, id) {\r\n    super();\r\n    this.document = document;\r\n    this.font = font;\r\n    this.id = id;\r\n    this.subset = this.font.createSubset();\r\n    this.unicode = [[0]];\r\n    this.widths = [this.font.getGlyph(0).advanceWidth];\r\n\r\n    this.name = this.font.postscriptName;\r\n    this.scale = 1000 / this.font.unitsPerEm;\r\n    this.ascender = this.font.ascent * this.scale;\r\n    this.descender = this.font.descent * this.scale;\r\n    this.xHeight = this.font.xHeight * this.scale;\r\n    this.capHeight = this.font.capHeight * this.scale;\r\n    this.lineGap = this.font.lineGap * this.scale;\r\n    this.bbox = this.font.bbox;\r\n\r\n    if (document.options.fontLayoutCache !== false) {\r\n      this.layoutCache = Object.create(null);\r\n    }\r\n  }\r\n\r\n  layoutRun(text, features) {\r\n    const run = this.font.layout(text, features);\r\n\r\n    // Normalize position values\r\n    for (let i = 0; i < run.positions.length; i++) {\r\n      const position = run.positions[i];\r\n      for (let key in position) {\r\n        position[key] *= this.scale;\r\n      }\r\n\r\n      position.advanceWidth = run.glyphs[i].advanceWidth * this.scale;\r\n    }\r\n\r\n    return run;\r\n  }\r\n\r\n  layoutCached(text) {\r\n    if (!this.layoutCache) {\r\n      return this.layoutRun(text);\r\n    }\r\n    let cached;\r\n    if ((cached = this.layoutCache[text])) {\r\n      return cached;\r\n    }\r\n\r\n    const run = this.layoutRun(text);\r\n    this.layoutCache[text] = run;\r\n    return run;\r\n  }\r\n\r\n  layout(text, features, onlyWidth) {\r\n    // Skip the cache if any user defined features are applied\r\n    if (features) {\r\n      return this.layoutRun(text, features);\r\n    }\r\n\r\n    let glyphs = onlyWidth ? null : [];\r\n    let positions = onlyWidth ? null : [];\r\n    let advanceWidth = 0;\r\n\r\n    // Split the string by words to increase cache efficiency.\r\n    // For this purpose, spaces and tabs are a good enough delimeter.\r\n    let last = 0;\r\n    let index = 0;\r\n    while (index <= text.length) {\r\n      var needle;\r\n      if (\r\n        (index === text.length && last < index) ||\r\n        ((needle = text.charAt(index)), [' ', '\\t'].includes(needle))\r\n      ) {\r\n        const run = this.layoutCached(text.slice(last, ++index));\r\n        if (!onlyWidth) {\r\n          glyphs = glyphs.concat(run.glyphs);\r\n          positions = positions.concat(run.positions);\r\n        }\r\n\r\n        advanceWidth += run.advanceWidth;\r\n        last = index;\r\n      } else {\r\n        index++;\r\n      }\r\n    }\r\n\r\n    return { glyphs, positions, advanceWidth };\r\n  }\r\n\r\n  encode(text, features) {\r\n    const { glyphs, positions } = this.layout(text, features);\r\n\r\n    const res = [];\r\n    for (let i = 0; i < glyphs.length; i++) {\r\n      const glyph = glyphs[i];\r\n      const gid = this.subset.includeGlyph(glyph.id);\r\n      res.push(`0000${gid.toString(16)}`.slice(-4));\r\n\r\n      if (this.widths[gid] == null) {\r\n        this.widths[gid] = glyph.advanceWidth * this.scale;\r\n      }\r\n      if (this.unicode[gid] == null) {\r\n        this.unicode[gid] = glyph.codePoints;\r\n      }\r\n    }\r\n\r\n    return [res, positions];\r\n  }\r\n\r\n  widthOfString(string, size, features) {\r\n    const width = this.layout(string, features, true).advanceWidth;\r\n    const scale = size / 1000;\r\n    return width * scale;\r\n  }\r\n\r\n  embed() {\r\n    const isCFF = this.subset.cff != null;\r\n    const fontFile = this.document.ref();\r\n\r\n    if (isCFF) {\r\n      fontFile.data.Subtype = 'CIDFontType0C';\r\n    }\r\n\r\n    this.subset\r\n      .encodeStream()\r\n      .on('data', data => fontFile.write(data))\r\n      .on('end', () => fontFile.end());\r\n\r\n    const familyClass =\r\n      ((this.font['OS/2'] != null\r\n        ? this.font['OS/2'].sFamilyClass\r\n        : undefined) || 0) >> 8;\r\n    let flags = 0;\r\n    if (this.font.post.isFixedPitch) {\r\n      flags |= 1 << 0;\r\n    }\r\n    if (1 <= familyClass && familyClass <= 7) {\r\n      flags |= 1 << 1;\r\n    }\r\n    flags |= 1 << 2; // assume the font uses non-latin characters\r\n    if (familyClass === 10) {\r\n      flags |= 1 << 3;\r\n    }\r\n    if (this.font.head.macStyle.italic) {\r\n      flags |= 1 << 6;\r\n    }\r\n\r\n    // generate a tag (6 uppercase letters. 17 is the char code offset from '0' to 'A'. 73 will map to 'Z')\r\n    const tag = [1, 2, 3, 4, 5, 6]\r\n      .map(i => String.fromCharCode((this.id.charCodeAt(i) || 73) + 17))\r\n      .join('');\r\n    const name = tag + '+' + this.font.postscriptName;\r\n\r\n    const { bbox } = this.font;\r\n    const descriptor = this.document.ref({\r\n      Type: 'FontDescriptor',\r\n      FontName: name,\r\n      Flags: flags,\r\n      FontBBox: [\r\n        bbox.minX * this.scale,\r\n        bbox.minY * this.scale,\r\n        bbox.maxX * this.scale,\r\n        bbox.maxY * this.scale\r\n      ],\r\n      ItalicAngle: this.font.italicAngle,\r\n      Ascent: this.ascender,\r\n      Descent: this.descender,\r\n      CapHeight: (this.font.capHeight || this.font.ascent) * this.scale,\r\n      XHeight: (this.font.xHeight || 0) * this.scale,\r\n      StemV: 0\r\n    }); // not sure how to calculate this\r\n\r\n    if (isCFF) {\r\n      descriptor.data.FontFile3 = fontFile;\r\n    } else {\r\n      descriptor.data.FontFile2 = fontFile;\r\n    }\r\n\r\n    if (this.document.subset) {\r\n      const CIDSet = Buffer.from('FFFFFFFFC0', 'hex');\r\n      const CIDSetRef = this.document.ref();\r\n      CIDSetRef.write(CIDSet);\r\n      CIDSetRef.end();\r\n\r\n      descriptor.data.CIDSet = CIDSetRef;\r\n    }\r\n\r\n    descriptor.end();\r\n\r\n    const descendantFontData = {\r\n      Type: 'Font',\r\n      Subtype: 'CIDFontType0',\r\n      BaseFont: name,\r\n      CIDSystemInfo: {\r\n        Registry: new String('Adobe'),\r\n        Ordering: new String('Identity'),\r\n        Supplement: 0\r\n      },\r\n      FontDescriptor: descriptor,\r\n      W: [0, this.widths]\r\n    };\r\n\r\n    if (!isCFF) {\r\n      descendantFontData.Subtype = 'CIDFontType2';\r\n      descendantFontData.CIDToGIDMap = 'Identity';\r\n    }\r\n\r\n    const descendantFont = this.document.ref(descendantFontData);\r\n\r\n    descendantFont.end();\r\n\r\n    this.dictionary.data = {\r\n      Type: 'Font',\r\n      Subtype: 'Type0',\r\n      BaseFont: name,\r\n      Encoding: 'Identity-H',\r\n      DescendantFonts: [descendantFont],\r\n      ToUnicode: this.toUnicodeCmap()\r\n    };\r\n\r\n    return this.dictionary.end();\r\n  }\r\n\r\n  // Maps the glyph ids encoded in the PDF back to unicode strings\r\n  // Because of ligature substitutions and the like, there may be one or more\r\n  // unicode characters represented by each glyph.\r\n  toUnicodeCmap() {\r\n    const cmap = this.document.ref();\r\n\r\n    const entries = [];\r\n    for (let codePoints of this.unicode) {\r\n      const encoded = [];\r\n\r\n      // encode codePoints to utf16\r\n      for (let value of codePoints) {\r\n        if (value > 0xffff) {\r\n          value -= 0x10000;\r\n          encoded.push(toHex(((value >>> 10) & 0x3ff) | 0xd800));\r\n          value = 0xdc00 | (value & 0x3ff);\r\n        }\r\n\r\n        encoded.push(toHex(value));\r\n      }\r\n\r\n      entries.push(`<${encoded.join(' ')}>`);\r\n    }\r\n\r\n    const chunkSize = 256;\r\n    const chunks = Math.ceil(entries.length / chunkSize);\r\n    const ranges = [];\r\n    for (let i = 0; i < chunks; i++) {\r\n      const start = i * chunkSize;\r\n      const end = Math.min((i + 1) * chunkSize, entries.length);\r\n      ranges.push(`<${toHex(start)}> <${toHex(end - 1)}> [${entries.slice(start, end).join(' ')}]`);\r\n    }\r\n\r\n    cmap.end(`\\\r\n/CIDInit /ProcSet findresource begin\r\n12 dict begin\r\nbegincmap\r\n/CIDSystemInfo <<\r\n  /Registry (Adobe)\r\n  /Ordering (UCS)\r\n  /Supplement 0\r\n>> def\r\n/CMapName /Adobe-Identity-UCS def\r\n/CMapType 2 def\r\n1 begincodespacerange\r\n<0000><ffff>\r\nendcodespacerange\r\n1 beginbfrange\r\n${ranges.join('\\n')}\r\nendbfrange\r\nendcmap\r\nCMapName currentdict /CMap defineresource pop\r\nend\r\nend\\\r\n`);\r\n\r\n    return cmap;\r\n  }\r\n}\r\n\r\nexport default EmbeddedFont;\r\n", "import fs from 'fs';\r\nimport fontkit from 'fontkit';\r\nimport StandardFont from './font/standard';\r\nimport EmbeddedFont from './font/embedded';\r\n\r\nclass PDFFontFactory {\r\n  static open(document, src, family, id) {\r\n    let font;\r\n    if (typeof src === 'string') {\r\n      if (StandardFont.isStandardFont(src)) {\r\n        return new StandardFont(document, src, id);\r\n      }\r\n\r\n      src = fs.readFileSync(src);\r\n    }\r\n    if (Buffer.isBuffer(src)) {\r\n      font = fontkit.create(src, family);\r\n    } else if (src instanceof Uint8Array) {\r\n      font = fontkit.create(Buffer.from(src), family);\r\n    } else if (src instanceof ArrayBuffer) {\r\n      font = fontkit.create(Buffer.from(new Uint8Array(src)), family);\r\n    }\r\n\r\n    if (font == null) {\r\n      throw new Error('Not a supported font format or standard PDF font.');\r\n    }\r\n\r\n    return new EmbeddedFont(document, font, id);\r\n  }\r\n}\r\n\r\nexport default PDFFontFactory;\r\n", "import PDFFontFactory from '../font_factory';\r\n\r\nexport default {\r\n  initFonts(defaultFont = 'Helvetica') {\r\n    // Lookup table for embedded fonts\r\n    this._fontFamilies = {};\r\n    this._fontCount = 0;\r\n\r\n    // Font state\r\n    this._fontSize = 12;\r\n    this._font = null;\r\n\r\n    this._registeredFonts = {};\r\n\r\n    // Set the default font\r\n    if (defaultFont) {\r\n      this.font(defaultFont);\r\n    }\r\n  },\r\n\r\n  font(src, family, size) {\r\n    let cacheKey, font;\r\n    if (typeof family === 'number') {\r\n      size = family;\r\n      family = null;\r\n    }\r\n\r\n    // check registered fonts if src is a string\r\n    if (typeof src === 'string' && this._registeredFonts[src]) {\r\n      cacheKey = src;\r\n      ({ src, family } = this._registeredFonts[src]);\r\n    } else {\r\n      cacheKey = family || src;\r\n      if (typeof cacheKey !== 'string') {\r\n        cacheKey = null;\r\n      }\r\n    }\r\n\r\n    if (size != null) {\r\n      this.fontSize(size);\r\n    }\r\n\r\n    // fast path: check if the font is already in the PDF\r\n    if ((font = this._fontFamilies[cacheKey])) {\r\n      this._font = font;\r\n      return this;\r\n    }\r\n\r\n    // load the font\r\n    const id = `F${++this._fontCount}`;\r\n    this._font = PDFFontFactory.open(this, src, family, id);\r\n\r\n    // check for existing font familes with the same name already in the PDF\r\n    // useful if the font was passed as a buffer\r\n    if ((font = this._fontFamilies[this._font.name])) {\r\n      this._font = font;\r\n      return this;\r\n    }\r\n\r\n    // save the font for reuse later\r\n    if (cacheKey) {\r\n      this._fontFamilies[cacheKey] = this._font;\r\n    }\r\n\r\n    if (this._font.name) {\r\n      this._fontFamilies[this._font.name] = this._font;\r\n    }\r\n\r\n    return this;\r\n  },\r\n\r\n  fontSize(_fontSize) {\r\n    this._fontSize = _fontSize;\r\n    return this;\r\n  },\r\n\r\n  currentLineHeight(includeGap) {\r\n    if (includeGap == null) {\r\n      includeGap = false;\r\n    }\r\n    return this._font.lineHeight(this._fontSize, includeGap);\r\n  },\r\n\r\n  registerFont(name, src, family) {\r\n    this._registeredFonts[name] = {\r\n      src,\r\n      family\r\n    };\r\n\r\n    return this;\r\n  }\r\n};\r\n", "import { EventEmitter } from 'events';\r\nimport LineBreaker from 'linebreak';\r\n\r\nconst SOFT_HYPHEN = '\\u00AD';\r\nconst HYPHEN = '-';\r\n\r\nclass LineWrapper extends EventEmitter {\r\n  constructor(document, options) {\r\n    super();\r\n    this.document = document;\r\n    this.indent = options.indent || 0;\r\n    this.characterSpacing = options.characterSpacing || 0;\r\n    this.wordSpacing = options.wordSpacing === 0;\r\n    this.columns = options.columns || 1;\r\n    this.columnGap = options.columnGap != null ? options.columnGap : 18; // 1/4 inch\r\n    this.lineWidth =\r\n      (options.width - this.columnGap * (this.columns - 1)) / this.columns;\r\n    this.spaceLeft = this.lineWidth;\r\n    this.startX = this.document.x;\r\n    this.startY = this.document.y;\r\n    this.column = 1;\r\n    this.ellipsis = options.ellipsis;\r\n    this.continuedX = 0;\r\n    this.features = options.features;\r\n\r\n    // calculate the maximum Y position the text can appear at\r\n    if (options.height != null) {\r\n      this.height = options.height;\r\n      this.maxY = this.startY + options.height;\r\n    } else {\r\n      this.maxY = this.document.page.maxY();\r\n    }\r\n\r\n    // handle paragraph indents\r\n    this.on('firstLine', options => {\r\n      // if this is the first line of the text segment, and\r\n      // we're continuing where we left off, indent that much\r\n      // otherwise use the user specified indent option\r\n      const indent = this.continuedX || this.indent;\r\n      this.document.x += indent;\r\n      this.lineWidth -= indent;\r\n\r\n      return this.once('line', () => {\r\n        this.document.x -= indent;\r\n        this.lineWidth += indent;\r\n        if (options.continued && !this.continuedX) {\r\n          this.continuedX = this.indent;\r\n        }\r\n        if (!options.continued) {\r\n          return (this.continuedX = 0);\r\n        }\r\n      });\r\n    });\r\n\r\n    // handle left aligning last lines of paragraphs\r\n    this.on('lastLine', options => {\r\n      const { align } = options;\r\n      if (align === 'justify') {\r\n        options.align = 'left';\r\n      }\r\n      this.lastLine = true;\r\n\r\n      return this.once('line', () => {\r\n        this.document.y += options.paragraphGap || 0;\r\n        options.align = align;\r\n        return (this.lastLine = false);\r\n      });\r\n    });\r\n  }\r\n\r\n  wordWidth(word) {\r\n    return (\r\n      this.document.widthOfString(word, this) +\r\n      this.characterSpacing +\r\n      this.wordSpacing\r\n    );\r\n  }\r\n\r\n  canFit(word, w) {\r\n    if (word[word.length - 1] != SOFT_HYPHEN) {\r\n      return w <= this.spaceLeft;\r\n    }\r\n    return w + this.wordWidth(HYPHEN) <= this.spaceLeft;\r\n  }\r\n\r\n  eachWord(text, fn) {\r\n    // setup a unicode line breaker\r\n    let bk;\r\n    const breaker = new LineBreaker(text);\r\n    let last = null;\r\n    const wordWidths = Object.create(null);\r\n\r\n    while ((bk = breaker.nextBreak())) {\r\n      var shouldContinue;\r\n      let word = text.slice(\r\n        (last != null ? last.position : undefined) || 0,\r\n        bk.position\r\n      );\r\n      let w =\r\n        wordWidths[word] != null\r\n          ? wordWidths[word]\r\n          : (wordWidths[word] = this.wordWidth(word));\r\n\r\n      // if the word is longer than the whole line, chop it up\r\n      // TODO: break by grapheme clusters, not JS string characters\r\n      if (w > this.lineWidth + this.continuedX) {\r\n        // make some fake break objects\r\n        let lbk = last;\r\n        const fbk = {};\r\n\r\n        while (word.length) {\r\n          // fit as much of the word as possible into the space we have\r\n          var l, mightGrow;\r\n          if (w > this.spaceLeft) {\r\n            // start our check at the end of our available space - this method is faster than a loop of each character and it resolves\r\n            // an issue with long loops when processing massive words, such as a huge number of spaces\r\n            l = Math.ceil(this.spaceLeft / (w / word.length));\r\n            w = this.wordWidth(word.slice(0, l));\r\n            mightGrow = w <= this.spaceLeft && l < word.length;\r\n          } else {\r\n            l = word.length;\r\n          }\r\n          let mustShrink = w > this.spaceLeft && l > 0;\r\n          // shrink or grow word as necessary after our near-guess above\r\n          while (mustShrink || mightGrow) {\r\n            if (mustShrink) {\r\n              w = this.wordWidth(word.slice(0, --l));\r\n              mustShrink = w > this.spaceLeft && l > 0;\r\n            } else {\r\n              w = this.wordWidth(word.slice(0, ++l));\r\n              mustShrink = w > this.spaceLeft && l > 0;\r\n              mightGrow = w <= this.spaceLeft && l < word.length;\r\n            }\r\n          }\r\n\r\n          // check for the edge case where a single character cannot fit into a line.\r\n          if (l === 0 && this.spaceLeft === this.lineWidth) {\r\n            l = 1;\r\n          }\r\n\r\n          // send a required break unless this is the last piece and a linebreak is not specified\r\n          fbk.required = bk.required || l < word.length;\r\n          shouldContinue = fn(word.slice(0, l), w, fbk, lbk);\r\n          lbk = { required: false };\r\n\r\n          // get the remaining piece of the word\r\n          word = word.slice(l);\r\n          w = this.wordWidth(word);\r\n\r\n          if (shouldContinue === false) {\r\n            break;\r\n          }\r\n        }\r\n      } else {\r\n        // otherwise just emit the break as it was given to us\r\n        shouldContinue = fn(word, w, bk, last);\r\n      }\r\n\r\n      if (shouldContinue === false) {\r\n        break;\r\n      }\r\n      last = bk;\r\n    }\r\n  }\r\n\r\n  wrap(text, options) {\r\n    // override options from previous continued fragments\r\n    if (options.indent != null) {\r\n      this.indent = options.indent;\r\n    }\r\n    if (options.characterSpacing != null) {\r\n      this.characterSpacing = options.characterSpacing;\r\n    }\r\n    if (options.wordSpacing != null) {\r\n      this.wordSpacing = options.wordSpacing;\r\n    }\r\n    if (options.ellipsis != null) {\r\n      this.ellipsis = options.ellipsis;\r\n    }\r\n\r\n    // make sure we're actually on the page\r\n    // and that the first line of is never by\r\n    // itself at the bottom of a page (orphans)\r\n    const nextY = this.document.y + this.document.currentLineHeight(true);\r\n    if (this.document.y > this.maxY || nextY > this.maxY) {\r\n      this.nextSection();\r\n    }\r\n\r\n    let buffer = '';\r\n    let textWidth = 0;\r\n    let wc = 0;\r\n    let lc = 0;\r\n\r\n    let { y } = this.document; // used to reset Y pos if options.continued (below)\r\n    const emitLine = () => {\r\n      options.textWidth = textWidth + this.wordSpacing * (wc - 1);\r\n      options.wordCount = wc;\r\n      options.lineWidth = this.lineWidth;\r\n      ({ y } = this.document);\r\n      this.emit('line', buffer, options, this);\r\n      return lc++;\r\n    };\r\n\r\n    this.emit('sectionStart', options, this);\r\n\r\n    this.eachWord(text, (word, w, bk, last) => {\r\n      if (last == null || last.required) {\r\n        this.emit('firstLine', options, this);\r\n        this.spaceLeft = this.lineWidth;\r\n      }\r\n\r\n      if (this.canFit(word, w)) {\r\n        buffer += word;\r\n        textWidth += w;\r\n        wc++;\r\n      }\r\n\r\n      if (bk.required || !this.canFit(word, w)) {\r\n        // if the user specified a max height and an ellipsis, and is about to pass the\r\n        // max height and max columns after the next line, append the ellipsis\r\n        const lh = this.document.currentLineHeight(true);\r\n        if (\r\n          this.height != null &&\r\n          this.ellipsis &&\r\n          this.document.y + lh * 2 > this.maxY &&\r\n          this.column >= this.columns\r\n        ) {\r\n          if (this.ellipsis === true) {\r\n            this.ellipsis = '…';\r\n          } // map default ellipsis character\r\n          buffer = buffer.replace(/\\s+$/, '');\r\n          textWidth = this.wordWidth(buffer + this.ellipsis);\r\n\r\n          // remove characters from the buffer until the ellipsis fits\r\n          // to avoid infinite loop need to stop while-loop if buffer is empty string\r\n          while (buffer && textWidth > this.lineWidth) {\r\n            buffer = buffer.slice(0, -1).replace(/\\s+$/, '');\r\n            textWidth = this.wordWidth(buffer + this.ellipsis);\r\n          }\r\n          // need to add ellipsis only if there is enough space for it\r\n          if (textWidth <= this.lineWidth) {\r\n            buffer = buffer + this.ellipsis;\r\n          }\r\n\r\n          textWidth = this.wordWidth(buffer);\r\n        }\r\n\r\n        if (bk.required) {\r\n          if (w > this.spaceLeft) {\r\n            emitLine();\r\n            buffer = word;\r\n            textWidth = w;\r\n            wc = 1;\r\n          }\r\n\r\n          this.emit('lastLine', options, this);\r\n        }\r\n\r\n        // Previous entry is a soft hyphen - add visible hyphen.\r\n        if (buffer[buffer.length - 1] == SOFT_HYPHEN) {\r\n          buffer = buffer.slice(0, -1) + HYPHEN;\r\n          this.spaceLeft -= this.wordWidth(HYPHEN);\r\n        }\r\n\r\n        emitLine();\r\n\r\n        // if we've reached the edge of the page,\r\n        // continue on a new page or column\r\n        if (this.document.y + lh > this.maxY) {\r\n          const shouldContinue = this.nextSection();\r\n\r\n          // stop if we reached the maximum height\r\n          if (!shouldContinue) {\r\n            wc = 0;\r\n            buffer = '';\r\n            return false;\r\n          }\r\n        }\r\n\r\n        // reset the space left and buffer\r\n        if (bk.required) {\r\n          this.spaceLeft = this.lineWidth;\r\n          buffer = '';\r\n          textWidth = 0;\r\n          return (wc = 0);\r\n        } else {\r\n          // reset the space left and buffer\r\n          this.spaceLeft = this.lineWidth - w;\r\n          buffer = word;\r\n          textWidth = w;\r\n          return (wc = 1);\r\n        }\r\n      } else {\r\n        return (this.spaceLeft -= w);\r\n      }\r\n    });\r\n\r\n    if (wc > 0) {\r\n      this.emit('lastLine', options, this);\r\n      emitLine();\r\n    }\r\n\r\n    this.emit('sectionEnd', options, this);\r\n\r\n    // if the wrap is set to be continued, save the X position\r\n    // to start the first line of the next segment at, and reset\r\n    // the y position\r\n    if (options.continued === true) {\r\n      if (lc > 1) {\r\n        this.continuedX = 0;\r\n      }\r\n      this.continuedX += options.textWidth || 0;\r\n      return (this.document.y = y);\r\n    } else {\r\n      return (this.document.x = this.startX);\r\n    }\r\n  }\r\n\r\n  nextSection(options) {\r\n    this.emit('sectionEnd', options, this);\r\n\r\n    if (++this.column > this.columns) {\r\n      // if a max height was specified by the user, we're done.\r\n      // otherwise, the default is to make a new page at the bottom.\r\n      if (this.height != null) {\r\n        return false;\r\n      }\r\n\r\n      this.document.continueOnNewPage();\r\n      this.column = 1;\r\n      this.startY = this.document.page.margins.top;\r\n      this.maxY = this.document.page.maxY();\r\n      this.document.x = this.startX;\r\n      if (this.document._fillColor) {\r\n        this.document.fillColor(...this.document._fillColor);\r\n      }\r\n      this.emit('pageBreak', options, this);\r\n    } else {\r\n      this.document.x += this.lineWidth + this.columnGap;\r\n      this.document.y = this.startY;\r\n      this.emit('columnBreak', options, this);\r\n    }\r\n\r\n    this.emit('sectionStart', options, this);\r\n    return true;\r\n  }\r\n}\r\n\r\nexport default LineWrapper;\r\n", "import LineWrapper from '../line_wrapper';\r\nimport PDFObject from '../object';\r\n\r\nconst { number } = PDFObject;\r\n\r\nexport default {\r\n  initText() {\r\n    this._line = this._line.bind(this);\r\n    // Current coordinates\r\n    this.x = 0;\r\n    this.y = 0;\r\n    return (this._lineGap = 0);\r\n  },\r\n\r\n  lineGap(_lineGap) {\r\n    this._lineGap = _lineGap;\r\n    return this;\r\n  },\r\n\r\n  moveDown(lines) {\r\n    if (lines == null) {\r\n      lines = 1;\r\n    }\r\n    this.y += this.currentLineHeight(true) * lines + this._lineGap;\r\n    return this;\r\n  },\r\n\r\n  moveUp(lines) {\r\n    if (lines == null) {\r\n      lines = 1;\r\n    }\r\n    this.y -= this.currentLineHeight(true) * lines + this._lineGap;\r\n    return this;\r\n  },\r\n\r\n  _text(text, x, y, options, lineCallback) {\r\n    options = this._initOptions(x, y, options);\r\n\r\n    // Convert text to a string\r\n    text = text == null ? '' : `${text}`;\r\n\r\n    // if the wordSpacing option is specified, remove multiple consecutive spaces\r\n    if (options.wordSpacing) {\r\n      text = text.replace(/\\s{2,}/g, ' ');\r\n    }\r\n\r\n    const addStructure = () => {\r\n      if (options.structParent) {\r\n        options.structParent.add(this.struct(options.structType || 'P',\r\n          [ this.markStructureContent(options.structType || 'P') ]));\r\n      }\r\n    };\r\n\r\n    // word wrapping\r\n    if (options.width) {\r\n      let wrapper = this._wrapper;\r\n      if (!wrapper) {\r\n        wrapper = new LineWrapper(this, options);\r\n        wrapper.on('line', lineCallback);\r\n        wrapper.on('firstLine', addStructure);\r\n      }\r\n\r\n      this._wrapper = options.continued ? wrapper : null;\r\n      this._textOptions = options.continued ? options : null;\r\n      wrapper.wrap(text, options);\r\n\r\n      // render paragraphs as single lines\r\n    } else {\r\n      for (let line of text.split('\\n')) {\r\n        addStructure();\r\n        lineCallback(line, options);\r\n      }\r\n    }\r\n\r\n    return this;\r\n  },\r\n\r\n  text(text, x, y, options) {\r\n    return this._text(text, x, y, options, this._line);\r\n  },\r\n\r\n  widthOfString(string, options = {}) {\r\n    return (\r\n      this._font.widthOfString(string, this._fontSize, options.features) +\r\n      (options.characterSpacing || 0) * (string.length - 1)\r\n    );\r\n  },\r\n\r\n  heightOfString(text, options) {\r\n    const { x, y } = this;\r\n\r\n    options = this._initOptions(options);\r\n    options.height = Infinity; // don't break pages\r\n\r\n    const lineGap = options.lineGap || this._lineGap || 0;\r\n    this._text(text, this.x, this.y, options, () => {\r\n      return (this.y += this.currentLineHeight(true) + lineGap);\r\n    });\r\n\r\n    const height = this.y - y;\r\n    this.x = x;\r\n    this.y = y;\r\n\r\n    return height;\r\n  },\r\n\r\n  list(list, x, y, options, wrapper) {\r\n    options = this._initOptions(x, y, options);\r\n\r\n    const listType = options.listType || 'bullet';\r\n    const unit = Math.round((this._font.ascender / 1000) * this._fontSize);\r\n    const midLine = unit / 2;\r\n    const r = options.bulletRadius || unit / 3;\r\n    const indent =\r\n      options.textIndent || (listType === 'bullet' ? r * 5 : unit * 2);\r\n    const itemIndent =\r\n      options.bulletIndent || (listType === 'bullet' ? r * 8 : unit * 2);\r\n\r\n    let level = 1;\r\n    const items = [];\r\n    const levels = [];\r\n    const numbers = [];\r\n\r\n    var flatten = function(list) {\r\n      let n = 1;\r\n      for (let i = 0; i < list.length; i++) {\r\n        const item = list[i];\r\n        if (Array.isArray(item)) {\r\n          level++;\r\n          flatten(item);\r\n          level--;\r\n        } else {\r\n          items.push(item);\r\n          levels.push(level);\r\n          if (listType !== 'bullet') {\r\n            numbers.push(n++);\r\n          }\r\n        }\r\n      }\r\n    };\r\n\r\n    flatten(list);\r\n\r\n    const label = function(n) {\r\n      switch (listType) {\r\n        case 'numbered':\r\n          return `${n}.`;\r\n        case 'lettered':\r\n          var letter = String.fromCharCode(((n - 1) % 26) + 65);\r\n          var times = Math.floor((n - 1) / 26 + 1);\r\n          var text = Array(times + 1).join(letter);\r\n          return `${text}.`;\r\n      }\r\n    };\r\n\r\n    const drawListItem = function(listItem) {\r\n      wrapper = new LineWrapper(this, options);\r\n      wrapper.on('line', this._line);\r\n\r\n      level = 1;\r\n      let i = 0;\r\n      wrapper.once('firstLine', () => {\r\n        let item, itemType, labelType, bodyType;\r\n        if (options.structParent) {\r\n          if (options.structTypes) {\r\n            [itemType, labelType, bodyType] = options.structTypes;\r\n          } else {\r\n            [itemType, labelType, bodyType] = ['LI', 'Lbl', 'LBody'];\r\n          }\r\n        }\r\n\r\n        if (itemType) {\r\n          item = this.struct(itemType);\r\n          options.structParent.add(item);\r\n        } else if (options.structParent) {\r\n          item = options.structParent;\r\n        }\r\n\r\n        let l;\r\n        if ((l = levels[i++]) !== level) {\r\n          const diff = itemIndent * (l - level);\r\n          this.x += diff;\r\n          wrapper.lineWidth -= diff;\r\n          level = l;\r\n        }\r\n\r\n        if (item && (labelType || bodyType)) {\r\n          item.add(this.struct(labelType || bodyType,\r\n            [this.markStructureContent(labelType || bodyType)]));\r\n        }\r\n        switch (listType) {\r\n          case 'bullet':\r\n            this.circle(this.x - indent + r, this.y + midLine, r);\r\n            this.fill();\r\n            break;\r\n          case 'numbered':\r\n          case 'lettered':\r\n            var text = label(numbers[i - 1]);\r\n            this._fragment(text, this.x - indent, this.y, options);\r\n            break;\r\n        }\r\n\r\n        if (item && labelType && bodyType) {\r\n          item.add(this.struct(bodyType, [this.markStructureContent(bodyType)]));\r\n        }\r\n        if (item && item !== options.structParent) {\r\n          item.end();\r\n        }\r\n      });\r\n\r\n      wrapper.on('sectionStart', () => {\r\n        const pos = indent + itemIndent * (level - 1);\r\n        this.x += pos;\r\n        return (wrapper.lineWidth -= pos);\r\n      });\r\n\r\n      wrapper.on('sectionEnd', () => {\r\n        const pos = indent + itemIndent * (level - 1);\r\n        this.x -= pos;\r\n        return (wrapper.lineWidth += pos);\r\n      });\r\n\r\n      wrapper.wrap(listItem, options);\r\n    };\r\n\r\n\r\n    for (let i = 0; i < items.length; i++) {\r\n      drawListItem.call(this, items[i]);\r\n    }\r\n\r\n    return this;\r\n  },\r\n\r\n  _initOptions(x = {}, y, options = {}) {\r\n    if (typeof x === 'object') {\r\n      options = x;\r\n      x = null;\r\n    }\r\n\r\n    // clone options object\r\n    const result = Object.assign({}, options);\r\n\r\n    // extend options with previous values for continued text\r\n    if (this._textOptions) {\r\n      for (let key in this._textOptions) {\r\n        const val = this._textOptions[key];\r\n        if (key !== 'continued') {\r\n          if (result[key] === undefined) {\r\n            result[key] = val;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    // Update the current position\r\n    if (x != null) {\r\n      this.x = x;\r\n    }\r\n    if (y != null) {\r\n      this.y = y;\r\n    }\r\n\r\n    // wrap to margins if no x or y position passed\r\n    if (result.lineBreak !== false) {\r\n      if (result.width == null) {\r\n        result.width = this.page.width - this.x - this.page.margins.right;\r\n      }\r\n      result.width = Math.max(result.width, 0);\r\n    }\r\n\r\n    if (!result.columns) {\r\n      result.columns = 0;\r\n    }\r\n    if (result.columnGap == null) {\r\n      result.columnGap = 18;\r\n    } // 1/4 inch\r\n\r\n    return result;\r\n  },\r\n\r\n  _line(text, options = {}, wrapper) {\r\n    this._fragment(text, this.x, this.y, options);\r\n    const lineGap = options.lineGap || this._lineGap || 0;\r\n\r\n    if (!wrapper) {\r\n      return (this.x += this.widthOfString(text));\r\n    } else {\r\n      return (this.y += this.currentLineHeight(true) + lineGap);\r\n    }\r\n  },\r\n\r\n  _fragment(text, x, y, options) {\r\n    let dy, encoded, i, positions, textWidth, words;\r\n    text = `${text}`.replace(/\\n/g, '');\r\n    if (text.length === 0) {\r\n      return;\r\n    }\r\n\r\n    // handle options\r\n    const align = options.align || 'left';\r\n    let wordSpacing = options.wordSpacing || 0;\r\n    const characterSpacing = options.characterSpacing || 0;\r\n\r\n    // text alignments\r\n    if (options.width) {\r\n      switch (align) {\r\n        case 'right':\r\n          textWidth = this.widthOfString(text.replace(/\\s+$/, ''), options);\r\n          x += options.lineWidth - textWidth;\r\n          break;\r\n\r\n        case 'center':\r\n          x += options.lineWidth / 2 - options.textWidth / 2;\r\n          break;\r\n\r\n        case 'justify':\r\n          // calculate the word spacing value\r\n          words = text.trim().split(/\\s+/);\r\n          textWidth = this.widthOfString(text.replace(/\\s+/g, ''), options);\r\n          var spaceWidth = this.widthOfString(' ') + characterSpacing;\r\n          wordSpacing = Math.max(\r\n            0,\r\n            (options.lineWidth - textWidth) / Math.max(1, words.length - 1) -\r\n              spaceWidth\r\n          );\r\n          break;\r\n      }\r\n    }\r\n\r\n    // text baseline alignments based on http://wiki.apache.org/xmlgraphics-fop/LineLayout/AlignmentHandling\r\n    if (typeof options.baseline === 'number') {\r\n      dy = -options.baseline;\r\n    } else {\r\n      switch (options.baseline) {\r\n        case 'svg-middle':\r\n          dy = 0.5 * this._font.xHeight;\r\n          break;\r\n        case 'middle':\r\n        case 'svg-central':\r\n          dy = 0.5 * (this._font.descender + this._font.ascender);\r\n          break;\r\n        case 'bottom':\r\n        case 'ideographic':\r\n          dy = this._font.descender;\r\n          break;\r\n        case 'alphabetic':\r\n          dy = 0;\r\n          break;\r\n        case 'mathematical':\r\n          dy = 0.5 * this._font.ascender;\r\n          break;\r\n        case 'hanging':\r\n          dy = 0.8 * this._font.ascender;\r\n          break;\r\n        case 'top':\r\n          dy = this._font.ascender;\r\n          break;\r\n        default:\r\n          dy = this._font.ascender;\r\n      }\r\n      dy = (dy / 1000) * this._fontSize;\r\n    }\r\n\r\n    // calculate the actual rendered width of the string after word and character spacing\r\n    const renderedWidth =\r\n      options.textWidth +\r\n      wordSpacing * (options.wordCount - 1) +\r\n      characterSpacing * (text.length - 1);\r\n\r\n    // create link annotations if the link option is given\r\n    if (options.link != null) {\r\n      this.link(x, y, renderedWidth, this.currentLineHeight(), options.link);\r\n    }\r\n    if (options.goTo != null) {\r\n      this.goTo(x, y, renderedWidth, this.currentLineHeight(), options.goTo);\r\n    }\r\n    if (options.destination != null) {\r\n      this.addNamedDestination(options.destination, 'XYZ', x, y, null);\r\n    }\r\n\r\n    // create underline\r\n    if (options.underline) {\r\n      this.save();\r\n      if (!options.stroke) {\r\n        this.strokeColor(...(this._fillColor || []));\r\n      }\r\n\r\n      const lineWidth =\r\n        this._fontSize < 10 ? 0.5 : Math.floor(this._fontSize / 10);\r\n      this.lineWidth(lineWidth);\r\n\r\n      let lineY = (y + this.currentLineHeight())  - lineWidth\r\n      this.moveTo(x, lineY);\r\n      this.lineTo(x + renderedWidth, lineY);\r\n      this.stroke();\r\n      this.restore();\r\n    }\r\n    \r\n    // create strikethrough line\r\n    if (options.strike) {\r\n      this.save();\r\n      if (!options.stroke) {\r\n        this.strokeColor(...(this._fillColor || []));\r\n      }\r\n\r\n      const lineWidth =\r\n        this._fontSize < 10 ? 0.5 : Math.floor(this._fontSize / 10);\r\n      this.lineWidth(lineWidth);\r\n\r\n      let lineY = y + this.currentLineHeight() / 2;\r\n      this.moveTo(x, lineY);\r\n      this.lineTo(x + renderedWidth, lineY);\r\n      this.stroke();\r\n      this.restore();\r\n    }\r\n\r\n    this.save();\r\n\r\n    // oblique (angle in degrees or boolean)\r\n    if (options.oblique) {\r\n      let skew;\r\n      if (typeof options.oblique === 'number') {\r\n        skew = -Math.tan((options.oblique * Math.PI) / 180);\r\n      } else {\r\n        skew = -0.25;\r\n      }\r\n      this.transform(1, 0, 0, 1, x, y);\r\n      this.transform(1, 0, skew, 1, -skew * dy, 0);\r\n      this.transform(1, 0, 0, 1, -x, -y);\r\n    }\r\n\r\n    // flip coordinate system\r\n    this.transform(1, 0, 0, -1, 0, this.page.height);\r\n    y = this.page.height - y - dy;\r\n\r\n    // add current font to page if necessary\r\n    if (this.page.fonts[this._font.id] == null) {\r\n      this.page.fonts[this._font.id] = this._font.ref();\r\n    }\r\n\r\n    // begin the text object\r\n    this.addContent('BT');\r\n\r\n    // text position\r\n    this.addContent(`1 0 0 1 ${number(x)} ${number(y)} Tm`);\r\n\r\n    // font and font size\r\n    this.addContent(`/${this._font.id} ${number(this._fontSize)} Tf`);\r\n\r\n    // rendering mode\r\n    const mode = options.fill && options.stroke ? 2 : options.stroke ? 1 : 0;\r\n    if (mode) {\r\n      this.addContent(`${mode} Tr`);\r\n    }\r\n\r\n    // Character spacing\r\n    if (characterSpacing) {\r\n      this.addContent(`${number(characterSpacing)} Tc`);\r\n    }\r\n\r\n    // Add the actual text\r\n    // If we have a word spacing value, we need to encode each word separately\r\n    // since the normal Tw operator only works on character code 32, which isn't\r\n    // used for embedded fonts.\r\n    if (wordSpacing) {\r\n      words = text.trim().split(/\\s+/);\r\n      wordSpacing += this.widthOfString(' ') + characterSpacing;\r\n      wordSpacing *= 1000 / this._fontSize;\r\n\r\n      encoded = [];\r\n      positions = [];\r\n      for (let word of words) {\r\n        const [encodedWord, positionsWord] = this._font.encode(\r\n          word,\r\n          options.features\r\n        );\r\n        encoded = encoded.concat(encodedWord);\r\n        positions = positions.concat(positionsWord);\r\n\r\n        // add the word spacing to the end of the word\r\n        // clone object because of cache\r\n        const space = {};\r\n        const object = positions[positions.length - 1];\r\n        for (let key in object) {\r\n          const val = object[key];\r\n          space[key] = val;\r\n        }\r\n        space.xAdvance += wordSpacing;\r\n        positions[positions.length - 1] = space;\r\n      }\r\n    } else {\r\n      [encoded, positions] = this._font.encode(text, options.features);\r\n    }\r\n\r\n    const scale = this._fontSize / 1000;\r\n    const commands = [];\r\n    let last = 0;\r\n    let hadOffset = false;\r\n\r\n    // Adds a segment of text to the TJ command buffer\r\n    const addSegment = cur => {\r\n      if (last < cur) {\r\n        const hex = encoded.slice(last, cur).join('');\r\n        const advance =\r\n          positions[cur - 1].xAdvance - positions[cur - 1].advanceWidth;\r\n        commands.push(`<${hex}> ${number(-advance)}`);\r\n      }\r\n\r\n      return (last = cur);\r\n    };\r\n\r\n    // Flushes the current TJ commands to the output stream\r\n    const flush = i => {\r\n      addSegment(i);\r\n\r\n      if (commands.length > 0) {\r\n        this.addContent(`[${commands.join(' ')}] TJ`);\r\n        return (commands.length = 0);\r\n      }\r\n    };\r\n\r\n    for (i = 0; i < positions.length; i++) {\r\n      // If we have an x or y offset, we have to break out of the current TJ command\r\n      // so we can move the text position.\r\n      const pos = positions[i];\r\n      if (pos.xOffset || pos.yOffset) {\r\n        // Flush the current buffer\r\n        flush(i);\r\n\r\n        // Move the text position and flush just the current character\r\n        this.addContent(\r\n          `1 0 0 1 ${number(x + pos.xOffset * scale)} ${number(\r\n            y + pos.yOffset * scale\r\n          )} Tm`\r\n        );\r\n        flush(i + 1);\r\n\r\n        hadOffset = true;\r\n      } else {\r\n        // If the last character had an offset, reset the text position\r\n        if (hadOffset) {\r\n          this.addContent(`1 0 0 1 ${number(x)} ${number(y)} Tm`);\r\n          hadOffset = false;\r\n        }\r\n\r\n        // Group segments that don't have any advance adjustments\r\n        if (pos.xAdvance - pos.advanceWidth !== 0) {\r\n          addSegment(i + 1);\r\n        }\r\n      }\r\n\r\n      x += pos.xAdvance * scale;\r\n    }\r\n\r\n    // Flush any remaining commands\r\n    flush(i);\r\n\r\n    // end the text object\r\n    this.addContent('ET');\r\n\r\n    // restore flipped coordinate system\r\n    return this.restore();\r\n  }\r\n};\r\n", "import exif from 'jpeg-exif';\r\n\r\nconst MARKERS = [\r\n  0xffc0,\r\n  0xffc1,\r\n  0xffc2,\r\n  0xffc3,\r\n  0xffc5,\r\n  0xffc6,\r\n  0xffc7,\r\n  0xffc8,\r\n  0xffc9,\r\n  0xffca,\r\n  0xffcb,\r\n  0xffcc,\r\n  0xffcd,\r\n  0xffce,\r\n  0xffcf\r\n];\r\n\r\nconst COLOR_SPACE_MAP = {\r\n  1: 'DeviceGray',\r\n  3: 'DeviceRGB',\r\n  4: 'DeviceCMYK'\r\n};\r\n\r\nclass JPEG {\r\n  constructor(data, label) {\r\n    let marker;\r\n    this.data = data;\r\n    this.label = label;\r\n    if (this.data.readUInt16BE(0) !== 0xffd8) {\r\n      throw 'SOI not found in JPEG';\r\n    }\r\n\r\n    // Parse the EXIF orientation\r\n    this.orientation = exif.fromBuffer(this.data).Orientation || 1;\r\n\r\n    let pos = 2;\r\n    while (pos < this.data.length) {\r\n      marker = this.data.readUInt16BE(pos);\r\n      pos += 2;\r\n      if (MARKERS.includes(marker)) {\r\n        break;\r\n      }\r\n      pos += this.data.readUInt16BE(pos);\r\n    }\r\n\r\n    if (!MARKERS.includes(marker)) {\r\n      throw 'Invalid JPEG.';\r\n    }\r\n    pos += 2;\r\n\r\n    this.bits = this.data[pos++];\r\n    this.height = this.data.readUInt16BE(pos);\r\n    pos += 2;\r\n\r\n    this.width = this.data.readUInt16BE(pos);\r\n    pos += 2;\r\n\r\n    const channels = this.data[pos++];\r\n    this.colorSpace = COLOR_SPACE_MAP[channels];\r\n\r\n    this.obj = null;\r\n  }\r\n\r\n  embed(document) {\r\n    if (this.obj) {\r\n      return;\r\n    }\r\n\r\n    this.obj = document.ref({\r\n      Type: 'XObject',\r\n      Subtype: 'Image',\r\n      BitsPerComponent: this.bits,\r\n      Width: this.width,\r\n      Height: this.height,\r\n      ColorSpace: this.colorSpace,\r\n      Filter: 'DCTDecode'\r\n    });\r\n\r\n    // add extra decode params for CMYK images. By swapping the\r\n    // min and max values from the default, we invert the colors. See\r\n    // section 4.8.4 of the spec.\r\n    if (this.colorSpace === 'DeviceCMYK') {\r\n      this.obj.data['Decode'] = [1.0, 0.0, 1.0, 0.0, 1.0, 0.0, 1.0, 0.0];\r\n    }\r\n\r\n    this.obj.end(this.data);\r\n\r\n    // free memory\r\n    return (this.data = null);\r\n  }\r\n}\r\n\r\nexport default JPEG;\r\n", "import zlib from 'zlib';\r\nimport PNG from 'png-js';\r\n\r\nclass PNGImage {\r\n  constructor(data, label) {\r\n    this.label = label;\r\n    this.image = new PNG(data);\r\n    this.width = this.image.width;\r\n    this.height = this.image.height;\r\n    this.imgData = this.image.imgData;\r\n    this.obj = null;\r\n  }\r\n\r\n  embed(document) {\r\n    let dataDecoded = false;\r\n\r\n    this.document = document;\r\n    if (this.obj) {\r\n      return;\r\n    }\r\n\r\n    const hasAlphaChannel = this.image.hasAlphaChannel;\r\n    const isInterlaced = this.image.interlaceMethod === 1;\r\n\r\n    this.obj = this.document.ref({\r\n      Type: 'XObject',\r\n      Subtype: 'Image',\r\n      BitsPerComponent: hasAlphaChannel ? 8 : this.image.bits,\r\n      Width: this.width,\r\n      Height: this.height,\r\n      Filter: 'FlateDecode'\r\n    });\r\n\r\n    if (!hasAlphaChannel) {\r\n      const params = this.document.ref({\r\n        Predictor: isInterlaced ? 1 : 15,\r\n        Colors: this.image.colors,\r\n        BitsPerComponent: this.image.bits,\r\n        Columns: this.width\r\n      });\r\n\r\n      this.obj.data['DecodeParms'] = params;\r\n      params.end();\r\n    }\r\n\r\n    if (this.image.palette.length === 0) {\r\n      this.obj.data['ColorSpace'] = this.image.colorSpace;\r\n    } else {\r\n      // embed the color palette in the PDF as an object stream\r\n      const palette = this.document.ref();\r\n      palette.end(Buffer.from(this.image.palette));\r\n\r\n      // build the color space array for the image\r\n      this.obj.data['ColorSpace'] = [\r\n        'Indexed',\r\n        'DeviceRGB',\r\n        this.image.palette.length / 3 - 1,\r\n        palette\r\n      ];\r\n    }\r\n\r\n    // For PNG color types 0, 2 and 3, the transparency data is stored in\r\n    // a dedicated PNG chunk.\r\n    if (this.image.transparency.grayscale != null) {\r\n      // Use Color Key Masking (spec section 4.8.5)\r\n      // An array with N elements, where N is two times the number of color components.\r\n      const val = this.image.transparency.grayscale;\r\n      this.obj.data['Mask'] = [val, val];\r\n    } else if (this.image.transparency.rgb) {\r\n      // Use Color Key Masking (spec section 4.8.5)\r\n      // An array with N elements, where N is two times the number of color components.\r\n      const { rgb } = this.image.transparency;\r\n      const mask = [];\r\n      for (let x of rgb) {\r\n        mask.push(x, x);\r\n      }\r\n\r\n      this.obj.data['Mask'] = mask;\r\n    } else if (this.image.transparency.indexed) {\r\n      // Create a transparency SMask for the image based on the data\r\n      // in the PLTE and tRNS sections. See below for details on SMasks.\r\n      dataDecoded = true;\r\n      return this.loadIndexedAlphaChannel();\r\n    } else if (hasAlphaChannel) {\r\n      // For PNG color types 4 and 6, the transparency data is stored as a alpha\r\n      // channel mixed in with the main image data. Separate this data out into an\r\n      // SMask object and store it separately in the PDF.\r\n      dataDecoded = true;\r\n      return this.splitAlphaChannel();\r\n    }\r\n\r\n    if (isInterlaced && !dataDecoded) {\r\n      return this.decodeData();\r\n    }\r\n\r\n    this.finalize();\r\n  }\r\n\r\n  finalize() {\r\n    if (this.alphaChannel) {\r\n      const sMask = this.document.ref({\r\n        Type: 'XObject',\r\n        Subtype: 'Image',\r\n        Height: this.height,\r\n        Width: this.width,\r\n        BitsPerComponent: 8,\r\n        Filter: 'FlateDecode',\r\n        ColorSpace: 'DeviceGray',\r\n        Decode: [0, 1]\r\n      });\r\n\r\n      sMask.end(this.alphaChannel);\r\n      this.obj.data['SMask'] = sMask;\r\n    }\r\n\r\n    // add the actual image data\r\n    this.obj.end(this.imgData);\r\n\r\n    // free memory\r\n    this.image = null;\r\n    return (this.imgData = null);\r\n  }\r\n\r\n  splitAlphaChannel() {\r\n    return this.image.decodePixels(pixels => {\r\n      let a, p;\r\n      const colorCount = this.image.colors;\r\n      const pixelCount = this.width * this.height;\r\n      const imgData = Buffer.alloc(pixelCount * colorCount);\r\n      const alphaChannel = Buffer.alloc(pixelCount);\r\n\r\n      let i = (p = a = 0);\r\n      const len = pixels.length;\r\n      // For 16bit images copy only most significant byte (MSB) - PNG data is always stored in network byte order (MSB first)\r\n      const skipByteCount = this.image.bits === 16 ? 1 : 0;\r\n      while (i < len) {\r\n        for (let colorIndex = 0; colorIndex < colorCount; colorIndex++) {\r\n          imgData[p++] = pixels[i++];\r\n          i += skipByteCount;\r\n        }\r\n        alphaChannel[a++] = pixels[i++];\r\n        i += skipByteCount;\r\n      }\r\n\r\n      this.imgData = zlib.deflateSync(imgData);\r\n      this.alphaChannel = zlib.deflateSync(alphaChannel);\r\n      return this.finalize();\r\n    });\r\n  }\r\n\r\n  loadIndexedAlphaChannel() {\r\n    const transparency = this.image.transparency.indexed;\r\n    return this.image.decodePixels(pixels => {\r\n      const alphaChannel = Buffer.alloc(this.width * this.height);\r\n\r\n      let i = 0;\r\n      for (let j = 0, end = pixels.length; j < end; j++) {\r\n        alphaChannel[i++] = transparency[pixels[j]];\r\n      }\r\n\r\n      this.alphaChannel = zlib.deflateSync(alphaChannel);\r\n      return this.finalize();\r\n    });\r\n  }\r\n\r\n  decodeData() {\r\n    this.image.decodePixels(pixels => {\r\n      this.imgData = zlib.deflateSync(pixels);\r\n      this.finalize();\r\n    });\r\n  }\r\n}\r\n\r\nexport default PNGImage;\r\n", "/*\r\nPDFImage - embeds images in PDF documents\r\nBy Devon Govett\r\n*/\r\n\r\nimport fs from 'fs';\r\nimport JPEG from './image/jpeg';\r\nimport PNG from './image/png';\r\n\r\nclass PDFImage {\r\n  static open(src, label) {\r\n    let data;\r\n    if (Buffer.isBuffer(src)) {\r\n      data = src;\r\n    } else if (src instanceof ArrayBuffer) {\r\n      data = Buffer.from(new Uint8Array(src));\r\n    } else {\r\n      let match;\r\n      if ((match = /^data:.+?;base64,(.*)$/.exec(src))) {\r\n        data = Buffer.from(match[1], 'base64');\r\n      } else {\r\n        data = fs.readFileSync(src);\r\n        if (!data) {\r\n          return;\r\n        }\r\n      }\r\n    }\r\n\r\n    if (data[0] === 0xff && data[1] === 0xd8) {\r\n      return new JPEG(data, label);\r\n    } else if (data[0] === 0x89 && data.toString('ascii', 1, 4) === 'PNG') {\r\n      return new PNG(data, label);\r\n    } else {\r\n      throw new Error('Unknown image format.');\r\n    }\r\n  }\r\n}\r\n\r\nexport default PDFImage;\r\n", "import PDFImage from '../image';\r\n\r\nexport default {\r\n  initImages() {\r\n    this._imageRegistry = {};\r\n    return (this._imageCount = 0);\r\n  },\r\n\r\n  image(src, x, y, options = {}) {\r\n    let bh, bp, bw, image, ip, left, left1, rotateAngle, originX, originY;\r\n    if (typeof x === 'object') {\r\n      options = x;\r\n      x = null;\r\n    }\r\n\r\n    // Ignore orientation based on document options or image options\r\n    const ignoreOrientation =\r\n      options.ignoreOrientation ||\r\n      (options.ignoreOrientation !== false && this.options.ignoreOrientation);\r\n\r\n    x = (left = x != null ? x : options.x) != null ? left : this.x;\r\n    y = (left1 = y != null ? y : options.y) != null ? left1 : this.y;\r\n\r\n    if (typeof src === 'string') {\r\n      image = this._imageRegistry[src];\r\n    }\r\n\r\n    if (!image) {\r\n      if (src.width && src.height) {\r\n        image = src;\r\n      } else {\r\n        image = this.openImage(src);\r\n      }\r\n    }\r\n\r\n    if (!image.obj) {\r\n      image.embed(this);\r\n    }\r\n\r\n    if (this.page.xobjects[image.label] == null) {\r\n      this.page.xobjects[image.label] = image.obj;\r\n    }\r\n\r\n    let { width, height } = image;\r\n\r\n    // If EXIF orientation calls for it, swap width and height\r\n    if (!ignoreOrientation && image.orientation > 4) {\r\n      [width, height] = [height, width];\r\n    }\r\n\r\n    let w = options.width || width;\r\n    let h = options.height || height;\r\n\r\n    if (options.width && !options.height) {\r\n      const wp = w / width;\r\n      w = width * wp;\r\n      h = height * wp;\r\n    } else if (options.height && !options.width) {\r\n      const hp = h / height;\r\n      w = width * hp;\r\n      h = height * hp;\r\n    } else if (options.scale) {\r\n      w = width * options.scale;\r\n      h = height * options.scale;\r\n    } else if (options.fit) {\r\n      [bw, bh] = options.fit;\r\n      bp = bw / bh;\r\n      ip = width / height;\r\n      if (ip > bp) {\r\n        w = bw;\r\n        h = bw / ip;\r\n      } else {\r\n        h = bh;\r\n        w = bh * ip;\r\n      }\r\n    } else if (options.cover) {\r\n      [bw, bh] = options.cover;\r\n      bp = bw / bh;\r\n      ip = width / height;\r\n      if (ip > bp) {\r\n        h = bh;\r\n        w = bh * ip;\r\n      } else {\r\n        w = bw;\r\n        h = bw / ip;\r\n      }\r\n    }\r\n\r\n    if (options.fit || options.cover) {\r\n      if (options.align === 'center') {\r\n        x = x + bw / 2 - w / 2;\r\n      } else if (options.align === 'right') {\r\n        x = x + bw - w;\r\n      }\r\n\r\n      if (options.valign === 'center') {\r\n        y = y + bh / 2 - h / 2;\r\n      } else if (options.valign === 'bottom') {\r\n        y = y + bh - h;\r\n      }\r\n    }\r\n\r\n    if (!ignoreOrientation) {\r\n      switch (image.orientation) {\r\n        // No orientation (need to flip image, though, because of the default transform matrix on the document)\r\n        default:\r\n        case 1:\r\n          h = -h;\r\n          y -= h;\r\n\r\n          rotateAngle = 0;\r\n          break;\r\n        // Flip Horizontal\r\n        case 2:\r\n          w = -w;\r\n          h = -h;\r\n          x -= w;\r\n          y -= h;\r\n\r\n          rotateAngle = 0;\r\n          break;\r\n        // Rotate 180 degrees\r\n        case 3:\r\n          originX = x;\r\n          originY = y;\r\n\r\n          h = -h;\r\n          x -= w;\r\n\r\n          rotateAngle = 180;\r\n          break;\r\n        // Flip vertical\r\n        case 4:\r\n          // Do nothing, image will be flipped\r\n\r\n          break;\r\n        // Flip horizontally and rotate 270 degrees CW\r\n        case 5:\r\n          originX = x;\r\n          originY = y;\r\n\r\n          [w, h] = [h, w];\r\n          y -= h;\r\n\r\n          rotateAngle = 90;\r\n          break;\r\n        // Rotate 90 degrees CW\r\n        case 6:\r\n          originX = x;\r\n          originY = y;\r\n\r\n          [w, h] = [h, w];\r\n          h = -h;\r\n\r\n          rotateAngle = 90;\r\n          break;\r\n        // Flip horizontally and rotate 90 degrees CW\r\n        case 7:\r\n          originX = x;\r\n          originY = y;\r\n\r\n          [w, h] = [h, w];\r\n          h = -h;\r\n          w = -w;\r\n          x -= w;\r\n\r\n          rotateAngle = 90;\r\n          break;\r\n        // Rotate 270 degrees CW\r\n        case 8:\r\n          originX = x;\r\n          originY = y;\r\n\r\n          [w, h] = [h, w];\r\n          h = -h;\r\n          x -= w;\r\n          y -= h;\r\n\r\n          rotateAngle = -90;\r\n          break;\r\n      }\r\n    } else {\r\n      h = -h;\r\n      y -= h;\r\n      rotateAngle = 0;\r\n    }\r\n\r\n    // create link annotations if the link option is given\r\n    if (options.link != null) {\r\n      this.link(x, y, w, h, options.link);\r\n    }\r\n    if (options.goTo != null) {\r\n      this.goTo(x, y, w, h, options.goTo);\r\n    }\r\n    if (options.destination != null) {\r\n      this.addNamedDestination(options.destination, 'XYZ', x, y, null);\r\n    }\r\n\r\n    // Set the current y position to below the image if it is in the document flow\r\n    if (this.y === y) {\r\n      this.y += h;\r\n    }\r\n\r\n    this.save();\r\n\r\n    if (rotateAngle) {\r\n      this.rotate(rotateAngle, {\r\n        origin: [originX, originY]\r\n      });\r\n    }\r\n\r\n    this.transform(w, 0, 0, h, x, y);\r\n    this.addContent(`/${image.label} Do`);\r\n    this.restore();\r\n\r\n    return this;\r\n  },\r\n\r\n  openImage(src) {\r\n    let image;\r\n    if (typeof src === 'string') {\r\n      image = this._imageRegistry[src];\r\n    }\r\n\r\n    if (!image) {\r\n      image = PDFImage.open(src, `I${++this._imageCount}`);\r\n      if (typeof src === 'string') {\r\n        this._imageRegistry[src] = image;\r\n      }\r\n    }\r\n\r\n    return image;\r\n  }\r\n};\r\n", "export default {\r\n  annotate(x, y, w, h, options) {\r\n    options.Type = 'Annot';\r\n    options.Rect = this._convertRect(x, y, w, h);\r\n    options.Border = [0, 0, 0];\r\n\r\n    if (options.Subtype === 'Link' && typeof options.F === 'undefined') {\r\n      options.F = 1 << 2; // Print Annotation Flag\r\n    }\r\n\r\n    if (options.Subtype !== 'Link') {\r\n      if (options.C == null) {\r\n        options.C = this._normalizeColor(options.color || [0, 0, 0]);\r\n      }\r\n    } // convert colors\r\n    delete options.color;\r\n\r\n    if (typeof options.Dest === 'string') {\r\n      options.Dest = new String(options.Dest);\r\n    }\r\n\r\n    // Capitalize keys\r\n    for (let key in options) {\r\n      const val = options[key];\r\n      options[key[0].toUpperCase() + key.slice(1)] = val;\r\n    }\r\n\r\n    const ref = this.ref(options);\r\n    this.page.annotations.push(ref);\r\n    ref.end();\r\n    return this;\r\n  },\r\n\r\n  note(x, y, w, h, contents, options = {}) {\r\n    options.Subtype = 'Text';\r\n    options.Contents = new String(contents);\r\n    options.Name = 'Comment';\r\n    if (options.color == null) {\r\n      options.color = [243, 223, 92];\r\n    }\r\n    return this.annotate(x, y, w, h, options);\r\n  },\r\n\r\n  goTo(x, y, w, h, name, options = {}) {\r\n    options.Subtype = 'Link';\r\n    options.A = this.ref({\r\n      S: 'GoTo',\r\n      D: new String(name)\r\n    });\r\n    options.A.end();\r\n    return this.annotate(x, y, w, h, options);\r\n  },\r\n\r\n  link(x, y, w, h, url, options = {}) {\r\n    options.Subtype = 'Link';\r\n\r\n    if (typeof url === 'number') {\r\n      // Link to a page in the document (the page must already exist)\r\n      const pages = this._root.data.Pages.data;\r\n      if (url >= 0 && url < pages.Kids.length) {\r\n        options.A = this.ref({\r\n          S: 'GoTo',\r\n          D: [pages.Kids[url], 'XYZ', null, null, null]\r\n        });\r\n        options.A.end();\r\n      } else {\r\n        throw new Error(`The document has no page ${url}`);\r\n      }\r\n    } else {\r\n      // Link to an external url\r\n      options.A = this.ref({\r\n        S: 'URI',\r\n        URI: new String(url)\r\n      });\r\n      options.A.end();\r\n    }\r\n\r\n    return this.annotate(x, y, w, h, options);\r\n  },\r\n\r\n  _markup(x, y, w, h, options = {}) {\r\n    const [x1, y1, x2, y2] = this._convertRect(x, y, w, h);\r\n    options.QuadPoints = [x1, y2, x2, y2, x1, y1, x2, y1];\r\n    options.Contents = new String();\r\n    return this.annotate(x, y, w, h, options);\r\n  },\r\n\r\n  highlight(x, y, w, h, options = {}) {\r\n    options.Subtype = 'Highlight';\r\n    if (options.color == null) {\r\n      options.color = [241, 238, 148];\r\n    }\r\n    return this._markup(x, y, w, h, options);\r\n  },\r\n\r\n  underline(x, y, w, h, options = {}) {\r\n    options.Subtype = 'Underline';\r\n    return this._markup(x, y, w, h, options);\r\n  },\r\n\r\n  strike(x, y, w, h, options = {}) {\r\n    options.Subtype = 'StrikeOut';\r\n    return this._markup(x, y, w, h, options);\r\n  },\r\n\r\n  lineAnnotation(x1, y1, x2, y2, options = {}) {\r\n    options.Subtype = 'Line';\r\n    options.Contents = new String();\r\n    options.L = [x1, this.page.height - y1, x2, this.page.height - y2];\r\n    return this.annotate(x1, y1, x2, y2, options);\r\n  },\r\n\r\n  rectAnnotation(x, y, w, h, options = {}) {\r\n    options.Subtype = 'Square';\r\n    options.Contents = new String();\r\n    return this.annotate(x, y, w, h, options);\r\n  },\r\n\r\n  ellipseAnnotation(x, y, w, h, options = {}) {\r\n    options.Subtype = 'Circle';\r\n    options.Contents = new String();\r\n    return this.annotate(x, y, w, h, options);\r\n  },\r\n\r\n  textAnnotation(x, y, w, h, text, options = {}) {\r\n    options.Subtype = 'FreeText';\r\n    options.Contents = new String(text);\r\n    options.DA = new String();\r\n    return this.annotate(x, y, w, h, options);\r\n  },\r\n\r\n  fileAnnotation(x, y, w, h, file = {}, options = {}) {\r\n    // create hidden file\r\n    const filespec = this.file(\r\n      file.src,\r\n      Object.assign({ hidden: true }, file)\r\n    );\r\n\r\n    options.Subtype = 'FileAttachment';\r\n    options.FS = filespec;\r\n\r\n    // add description from filespec unless description (Contents) has already been set\r\n    if (options.Contents) {\r\n      options.Contents = new String(options.Contents);\r\n    } else if (filespec.data.Desc) {\r\n      options.Contents = filespec.data.Desc;\r\n    }\r\n\r\n    return this.annotate(x, y, w, h, options);\r\n  },\r\n\r\n  _convertRect(x1, y1, w, h) {\r\n    // flip y1 and y2\r\n    let y2 = y1;\r\n    y1 += h;\r\n\r\n    // make x2\r\n    let x2 = x1 + w;\r\n\r\n    // apply current transformation matrix to points\r\n    const [m0, m1, m2, m3, m4, m5] = this._ctm;\r\n    x1 = m0 * x1 + m2 * y1 + m4;\r\n    y1 = m1 * x1 + m3 * y1 + m5;\r\n    x2 = m0 * x2 + m2 * y2 + m4;\r\n    y2 = m1 * x2 + m3 * y2 + m5;\r\n\r\n    return [x1, y1, x2, y2];\r\n  }\r\n};\r\n", "class PDFOutline {\r\n  constructor(document, parent, title, dest, options = { expanded: false }) {\r\n    this.document = document;\r\n    this.options = options;\r\n    this.outlineData = {};\r\n\r\n    if (dest !== null) {\r\n      this.outlineData['Dest'] = [dest.dictionary, 'Fit'];\r\n    }\r\n\r\n    if (parent !== null) {\r\n      this.outlineData['Parent'] = parent;\r\n    }\r\n\r\n    if (title !== null) {\r\n      this.outlineData['Title'] = new String(title);\r\n    }\r\n\r\n    this.dictionary = this.document.ref(this.outlineData);\r\n    this.children = [];\r\n  }\r\n\r\n  addItem(title, options = { expanded: false }) {\r\n    const result = new PDFOutline(\r\n      this.document,\r\n      this.dictionary,\r\n      title,\r\n      this.document.page,\r\n      options\r\n    );\r\n    this.children.push(result);\r\n\r\n    return result;\r\n  }\r\n\r\n  endOutline() {\r\n    if (this.children.length > 0) {\r\n      if (this.options.expanded) {\r\n        this.outlineData.Count = this.children.length;\r\n      }\r\n\r\n      const first = this.children[0],\r\n        last = this.children[this.children.length - 1];\r\n      this.outlineData.First = first.dictionary;\r\n      this.outlineData.Last = last.dictionary;\r\n\r\n      for (let i = 0, len = this.children.length; i < len; i++) {\r\n        const child = this.children[i];\r\n        if (i > 0) {\r\n          child.outlineData.Prev = this.children[i - 1].dictionary;\r\n        }\r\n        if (i < this.children.length - 1) {\r\n          child.outlineData.Next = this.children[i + 1].dictionary;\r\n        }\r\n        child.endOutline();\r\n      }\r\n    }\r\n\r\n    return this.dictionary.end();\r\n  }\r\n}\r\n\r\nexport default PDFOutline;\r\n", "import PDFOutline from '../outline';\r\n\r\nexport default {\r\n  initOutline() {\r\n    return (this.outline = new PDFOutline(this, null, null, null));\r\n  },\r\n\r\n  endOutline() {\r\n    this.outline.endOutline();\r\n    if (this.outline.children.length > 0) {\r\n      this._root.data.Outlines = this.outline.dictionary;\r\n      return (this._root.data.PageMode = 'UseOutlines');\r\n    }\r\n  }\r\n};\r\n", "/*\r\nPDFStructureContent - a reference to a marked structure content\r\nBy <PERSON>\r\n*/\r\n\r\nclass PDFStructureContent {\r\n  constructor(pageRef, mcid) {\r\n    this.refs = [{ pageRef, mcid }];\r\n  }\r\n\r\n  push(structContent) {\r\n    structContent.refs.forEach((ref) => this.refs.push(ref));\r\n  }\r\n}\r\n\r\nexport default PDFStructureContent;\r\n", "/*\r\nPDFStructureElement - represents an element in the PDF logical structure tree\r\nBy <PERSON>\r\n*/\r\n\r\nimport PDFStructureContent from \"./structure_content\";\r\n\r\nclass PDFStructureElement {\r\n  constructor(document, type, options = {}, children = null) {\r\n    this.document = document;\r\n\r\n    this._attached = false;\r\n    this._ended = false;\r\n    this._flushed = false;\r\n    this.dictionary = document.ref({\r\n      // Type: \"StructElem\",\r\n      S: type\r\n    });\r\n\r\n    const data = this.dictionary.data;\r\n\r\n    if (Array.isArray(options) || this._isValidChild(options)) {\r\n      children = options;\r\n      options = {};\r\n    }\r\n\r\n    if (typeof options.title !== 'undefined') {\r\n      data.T = new String(options.title);\r\n    }\r\n    if (typeof options.lang !== 'undefined') {\r\n      data.Lang = new String(options.lang);\r\n    }\r\n    if (typeof options.alt !== 'undefined') {\r\n      data.Alt = new String(options.alt);\r\n    }\r\n    if (typeof options.expanded !== 'undefined') {\r\n      data.E = new String(options.expanded);\r\n    }\r\n    if (typeof options.actual !== 'undefined') {\r\n      data.ActualText = new String(options.actual);\r\n    }\r\n\r\n    this._children = [];\r\n\r\n    if (children) {\r\n      if (!Array.isArray(children)) {\r\n        children = [children];\r\n      }\r\n      children.forEach((child) => this.add(child));\r\n      this.end();\r\n    }\r\n  }\r\n\r\n  add(child) {\r\n    if (this._ended) {\r\n      throw new Error(`Cannot add child to already-ended structure element`);\r\n    }\r\n\r\n    if (!this._isValidChild(child)) {\r\n      throw new Error(`Invalid structure element child`);\r\n    }\r\n\r\n    if (child instanceof PDFStructureElement) {\r\n      child.setParent(this.dictionary);\r\n      if (this._attached) {\r\n        child.setAttached();\r\n      }\r\n    }\r\n\r\n    if (child instanceof PDFStructureContent) {\r\n      this._addContentToParentTree(child);\r\n    }\r\n\r\n    if (typeof child === 'function' && this._attached) {\r\n      // _contentForClosure() adds the content to the parent tree\r\n      child = this._contentForClosure(child);\r\n    }\r\n\r\n    this._children.push(child);\r\n\r\n    return this;\r\n  }\r\n\r\n  _addContentToParentTree(content) {\r\n    content.refs.forEach(({ pageRef, mcid }) => {\r\n      const pageStructParents = this.document.getStructParentTree()\r\n        .get(pageRef.data.StructParents);\r\n      pageStructParents[mcid] = this.dictionary;\r\n    });\r\n  }\r\n\r\n  setParent(parentRef) {\r\n    if (this.dictionary.data.P) {\r\n      throw new Error(`Structure element added to more than one parent`);\r\n    }\r\n\r\n    this.dictionary.data.P = parentRef;\r\n\r\n    this._flush();\r\n  }\r\n\r\n  setAttached() {\r\n    if (this._attached) {\r\n      return;\r\n    }\r\n\r\n    this._children.forEach((child, index) => {\r\n      if (child instanceof PDFStructureElement) {\r\n        child.setAttached();\r\n      }\r\n      if (typeof child === 'function') {\r\n        this._children[index] = this._contentForClosure(child);\r\n      }\r\n    });\r\n\r\n    this._attached = true;\r\n\r\n    this._flush();\r\n  }\r\n\r\n  end() {\r\n    if (this._ended) {\r\n      return;\r\n    }\r\n\r\n    this._children\r\n      .filter((child) => child instanceof PDFStructureElement)\r\n      .forEach((child) => child.end());\r\n\r\n    this._ended = true;\r\n\r\n    this._flush();\r\n  }\r\n\r\n  _isValidChild(child) {\r\n    return child instanceof PDFStructureElement ||\r\n        child instanceof PDFStructureContent ||\r\n        typeof child === 'function';\r\n  }\r\n\r\n  _contentForClosure(closure) {\r\n    const content = this.document.markStructureContent(this.dictionary.data.S);\r\n    closure();\r\n    this.document.endMarkedContent();\r\n\r\n    this._addContentToParentTree(content);\r\n\r\n    return content;\r\n  }\r\n\r\n  _isFlushable() {\r\n    if (!this.dictionary.data.P || !this._ended) {\r\n      return false;\r\n    }\r\n\r\n    return this._children.every((child) => {\r\n      if (typeof child === 'function') {\r\n        return false;\r\n      }\r\n      if (child instanceof PDFStructureElement) {\r\n        return child._isFlushable();\r\n      }\r\n      return true;\r\n    });\r\n  }\r\n\r\n  _flush() {\r\n    if (this._flushed || !this._isFlushable()) {\r\n      return;\r\n    }\r\n\r\n    this.dictionary.data.K = [];\r\n\r\n    this._children.forEach((child) => this._flushChild(child));\r\n\r\n    this.dictionary.end();\r\n\r\n    // free memory used by children; the dictionary itself may still be\r\n    // referenced by a parent structure element or root, but we can\r\n    // at least trim the tree here\r\n    this._children = [];\r\n    this.dictionary.data.K = null;\r\n\r\n    this._flushed = true;\r\n  }\r\n\r\n  _flushChild(child) {\r\n    if (child instanceof PDFStructureElement) {\r\n      this.dictionary.data.K.push(child.dictionary);\r\n    }\r\n\r\n    if (child instanceof PDFStructureContent) {\r\n      child.refs.forEach(({ pageRef, mcid }) => {\r\n        if (!this.dictionary.data.Pg) {\r\n          this.dictionary.data.Pg = pageRef;\r\n        }\r\n\r\n        if (this.dictionary.data.Pg === pageRef) {\r\n          this.dictionary.data.K.push(mcid);\r\n        } else {\r\n          this.dictionary.data.K.push({\r\n            Type: \"MCR\",\r\n            Pg: pageRef,\r\n            MCID: mcid\r\n          });\r\n        }\r\n      });\r\n    }\r\n  }\r\n}\r\n\r\nexport default PDFStructureElement;\r\n", "/*\r\nPDFNumberTree - represents a number tree object\r\n*/\r\n\r\nimport PDFTree from \"./tree\";\r\n\r\nclass PDFNumberTree extends PDFTree {\r\n  _compareKeys(a, b) {\r\n    return parseInt(a) - parseInt(b);\r\n  }\r\n\r\n  _keysName() {\r\n    return \"Nums\";\r\n  }\r\n\r\n  _dataForKey(k) {\r\n    return parseInt(k);\r\n  }\r\n}\r\n\r\nexport default PDFNumberTree;\r\n", "/*\r\nMarkings mixin - support marked content sequences in content streams\r\nBy <PERSON>\r\n*/\r\n\r\nimport PDFStructureElement from \"../structure_element\";\r\nimport PDFStructureContent from \"../structure_content\";\r\nimport PDFNumberTree from \"../number_tree\";\r\nimport PDFObject from \"../object\";\r\n\r\nexport default {\r\n\r\n  initMarkings(options) {\r\n    this.structChildren = [];\r\n\r\n    if (options.tagged) {\r\n      this.getMarkInfoDictionary().data.Marked = true;\r\n      this.getStructTreeRoot();\r\n    }\r\n  },\r\n\r\n  markContent(tag, options = null) {\r\n    if (tag === 'Artifact' || (options && options.mcid)) {\r\n      let toClose = 0;\r\n      this.page.markings.forEach((marking) => {\r\n        if (toClose || marking.structContent || marking.tag === 'Artifact') {\r\n          toClose++;\r\n        }\r\n      });\r\n      while (toClose--) {\r\n        this.endMarkedContent();\r\n      }\r\n    }\r\n\r\n    if (!options) {\r\n      this.page.markings.push({ tag });\r\n      this.addContent(`/${tag} BMC`);\r\n      return this;\r\n    }\r\n\r\n    this.page.markings.push({ tag, options });\r\n\r\n    const dictionary = {};\r\n\r\n    if (typeof options.mcid !== 'undefined') {\r\n      dictionary.MCID = options.mcid;\r\n    }\r\n    if (tag === 'Artifact') {\r\n      if (typeof options.type === 'string') {\r\n        dictionary.Type = options.type;\r\n      }\r\n      if (Array.isArray(options.bbox)) {\r\n        dictionary.BBox = [options.bbox[0], this.page.height - options.bbox[3],\r\n          options.bbox[2], this.page.height - options.bbox[1]];\r\n      }\r\n      if (Array.isArray(options.attached) &&\r\n        options.attached.every(val => typeof val === 'string')) {\r\n        dictionary.Attached = options.attached;\r\n      }\r\n    }\r\n    if (tag === 'Span') {\r\n      if (options.lang) {\r\n        dictionary.Lang = new String(options.lang);\r\n      }\r\n      if (options.alt) {\r\n        dictionary.Alt = new String(options.alt);\r\n      }\r\n      if (options.expanded) {\r\n        dictionary.E = new String(options.expanded);\r\n      }\r\n      if (options.actual) {\r\n        dictionary.ActualText = new String(options.actual);\r\n      }\r\n    }\r\n\r\n    this.addContent(`/${tag} ${PDFObject.convert(dictionary)} BDC`);\r\n    return this;\r\n  },\r\n\r\n  markStructureContent(tag, options = {}) {\r\n    const pageStructParents = this.getStructParentTree().get(this.page.structParentTreeKey);\r\n    const mcid = pageStructParents.length;\r\n    pageStructParents.push(null);\r\n\r\n    this.markContent(tag, { ...options, mcid });\r\n\r\n    const structContent = new PDFStructureContent(this.page.dictionary, mcid);\r\n    this.page.markings.slice(-1)[0].structContent = structContent;\r\n    return structContent;\r\n  },\r\n\r\n  endMarkedContent() {\r\n    this.page.markings.pop();\r\n    this.addContent('EMC');\r\n    return this;\r\n  },\r\n\r\n  struct(type, options = {}, children = null) {\r\n    return new PDFStructureElement(this, type, options, children);\r\n  },\r\n\r\n  addStructure(structElem) {\r\n    const structTreeRoot = this.getStructTreeRoot();\r\n    structElem.setParent(structTreeRoot);\r\n    structElem.setAttached();\r\n    this.structChildren.push(structElem);\r\n    if (!structTreeRoot.data.K) {\r\n      structTreeRoot.data.K = [];\r\n    }\r\n    structTreeRoot.data.K.push(structElem.dictionary);\r\n    return this;\r\n  },\r\n\r\n  initPageMarkings(pageMarkings) {\r\n    pageMarkings.forEach((marking) => {\r\n      if (marking.structContent) {\r\n        const structContent = marking.structContent;\r\n        const newStructContent = this.markStructureContent(marking.tag, marking.options);\r\n        structContent.push(newStructContent);\r\n        this.page.markings.slice(-1)[0].structContent = structContent;\r\n      } else {\r\n        this.markContent(marking.tag, marking.options);\r\n      }\r\n    });\r\n  },\r\n\r\n  endPageMarkings(page) {\r\n    const pageMarkings = page.markings;\r\n    pageMarkings.forEach(() => page.write('EMC'));\r\n    page.markings = [];\r\n    return pageMarkings;\r\n  },\r\n\r\n  getMarkInfoDictionary() {\r\n    if (!this._root.data.MarkInfo) {\r\n      this._root.data.MarkInfo = this.ref({});\r\n    }\r\n    return this._root.data.MarkInfo;\r\n  },\r\n\r\n  getStructTreeRoot() {\r\n    if (!this._root.data.StructTreeRoot) {\r\n      this._root.data.StructTreeRoot = this.ref({\r\n        Type: 'StructTreeRoot',\r\n        ParentTree: new PDFNumberTree(),\r\n        ParentTreeNextKey: 0\r\n      });\r\n    }\r\n    return this._root.data.StructTreeRoot;\r\n  },\r\n\r\n  getStructParentTree() {\r\n    return this.getStructTreeRoot().data.ParentTree;\r\n  },\r\n\r\n  createStructParentTreeNextKey() {\r\n    // initialise the MarkInfo dictionary\r\n    this.getMarkInfoDictionary();\r\n\r\n    const structTreeRoot = this.getStructTreeRoot();\r\n    const key = structTreeRoot.data.ParentTreeNextKey++;\r\n    structTreeRoot.data.ParentTree.add(key, []);\r\n    return key;\r\n  },\r\n\r\n  endMarkings() {\r\n    const structTreeRoot = this._root.data.StructTreeRoot;\r\n    if (structTreeRoot) {\r\n      structTreeRoot.end();\r\n      this.structChildren.forEach((structElem) => structElem.end());\r\n    }\r\n    if (this._root.data.MarkInfo) {\r\n      this._root.data.MarkInfo.end();\r\n    }\r\n  }\r\n\r\n};\r\n", "const FIELD_FLAGS = {\r\n  readOnly: 1,\r\n  required: 2,\r\n  noExport: 4,\r\n  multiline: 0x1000,\r\n  password: 0x2000,\r\n  toggleToOffButton: 0x4000,\r\n  radioButton: 0x8000,\r\n  pushButton: 0x10000,\r\n  combo: 0x20000,\r\n  edit: 0x40000,\r\n  sort: 0x80000,\r\n  multiSelect: 0x200000,\r\n  noSpell: 0x400000\r\n};\r\nconst FIELD_JUSTIFY = {\r\n  left: 0,\r\n  center: 1,\r\n  right: 2\r\n};\r\nconst VALUE_MAP = { value: 'V', defaultValue: 'DV' };\r\nconst FORMAT_SPECIAL = {\r\n  zip: '0',\r\n  zipPlus4: '1',\r\n  zip4: '1',\r\n  phone: '2',\r\n  ssn: '3'\r\n};\r\nconst FORMAT_DEFAULT = {\r\n  number: {\r\n    nDec: 0,\r\n    sepComma: false,\r\n    negStyle: 'MinusBlack',\r\n    currency: '',\r\n    currencyPrepend: true\r\n  },\r\n  percent: {\r\n    nDec: 0,\r\n    sepComma: false\r\n  }\r\n};\r\n\r\nexport default {\r\n  /**\r\n   * Must call if adding AcroForms to a document. Must also call font() before\r\n   * this method to set the default font.\r\n   */\r\n  initForm() {\r\n    if (!this._font) {\r\n      throw new Error('Must set a font before calling initForm method');\r\n    }\r\n    this._acroform = {\r\n      fonts: {},\r\n      defaultFont: this._font.name\r\n    };\r\n    this._acroform.fonts[this._font.id] = this._font.ref();\r\n\r\n    let data = {\r\n      Fields: [],\r\n      NeedAppearances: true,\r\n      DA: new String(`/${this._font.id} 0 Tf 0 g`),\r\n      DR: {\r\n        Font: {}\r\n      }\r\n    };\r\n    data.DR.Font[this._font.id] = this._font.ref();\r\n    const AcroForm = this.ref(data);\r\n    this._root.data.AcroForm = AcroForm;\r\n    return this;\r\n  },\r\n\r\n  /**\r\n   * Called automatically by document.js\r\n   */\r\n  endAcroForm() {\r\n    if (this._root.data.AcroForm) {\r\n      if (\r\n        !Object.keys(this._acroform.fonts).length &&\r\n        !this._acroform.defaultFont\r\n      ) {\r\n        throw new Error('No fonts specified for PDF form');\r\n      }\r\n      let fontDict = this._root.data.AcroForm.data.DR.Font;\r\n      Object.keys(this._acroform.fonts).forEach(name => {\r\n        fontDict[name] = this._acroform.fonts[name];\r\n      });\r\n      this._root.data.AcroForm.data.Fields.forEach(fieldRef => {\r\n        this._endChild(fieldRef);\r\n      });\r\n      this._root.data.AcroForm.end();\r\n    }\r\n    return this;\r\n  },\r\n\r\n  _endChild(ref) {\r\n    if (Array.isArray(ref.data.Kids)) {\r\n      ref.data.Kids.forEach(childRef => {\r\n        this._endChild(childRef);\r\n      });\r\n      ref.end();\r\n    }\r\n    return this;\r\n  },\r\n\r\n  /**\r\n   * Creates and adds a form field to the document. Form fields are intermediate\r\n   * nodes in a PDF form that are used to specify form name heirarchy and form\r\n   * value defaults.\r\n   * @param {string} name - field name (T attribute in field dictionary)\r\n   * @param {object} options  - other attributes to include in field dictionary\r\n   */\r\n  formField(name, options = {}) {\r\n    let fieldDict = this._fieldDict(name, null, options);\r\n    let fieldRef = this.ref(fieldDict);\r\n    this._addToParent(fieldRef);\r\n    return fieldRef;\r\n  },\r\n\r\n  /**\r\n   * Creates and adds a Form Annotation to the document. Form annotations are\r\n   * called Widget annotations internally within a PDF file.\r\n   * @param {string} name - form field name (T attribute of widget annotation\r\n   * dictionary)\r\n   * @param {number} x\r\n   * @param {number} y\r\n   * @param {number} w\r\n   * @param {number} h\r\n   * @param {object} options\r\n   */\r\n  formAnnotation(name, type, x, y, w, h, options = {}) {\r\n    let fieldDict = this._fieldDict(name, type, options);\r\n    fieldDict.Subtype = 'Widget';\r\n    if (fieldDict.F === undefined) {\r\n      fieldDict.F = 4; // print the annotation\r\n    }\r\n\r\n    // Add Field annot to page, and get it's ref\r\n    this.annotate(x, y, w, h, fieldDict);\r\n    let annotRef = this.page.annotations[this.page.annotations.length - 1];\r\n\r\n    return this._addToParent(annotRef);\r\n  },\r\n\r\n  formText(name, x, y, w, h, options = {}) {\r\n    return this.formAnnotation(name, 'text', x, y, w, h, options);\r\n  },\r\n\r\n  formPushButton(name, x, y, w, h, options = {}) {\r\n    return this.formAnnotation(name, 'pushButton', x, y, w, h, options);\r\n  },\r\n\r\n  formCombo(name, x, y, w, h, options = {}) {\r\n    return this.formAnnotation(name, 'combo', x, y, w, h, options);\r\n  },\r\n\r\n  formList(name, x, y, w, h, options = {}) {\r\n    return this.formAnnotation(name, 'list', x, y, w, h, options);\r\n  },\r\n\r\n  formRadioButton(name, x, y, w, h, options = {}) {\r\n    return this.formAnnotation(name, 'radioButton', x, y, w, h, options);\r\n  },\r\n\r\n  formCheckbox(name, x, y, w, h, options = {}) {\r\n    return this.formAnnotation(name, 'checkbox', x, y, w, h, options);\r\n  },\r\n\r\n  _addToParent(fieldRef) {\r\n    let parent = fieldRef.data.Parent;\r\n    if (parent) {\r\n      if (!parent.data.Kids) {\r\n        parent.data.Kids = [];\r\n      }\r\n      parent.data.Kids.push(fieldRef);\r\n    } else {\r\n      this._root.data.AcroForm.data.Fields.push(fieldRef);\r\n    }\r\n    return this;\r\n  },\r\n\r\n  _fieldDict(name, type, options = {}) {\r\n    if (!this._acroform) {\r\n      throw new Error(\r\n        'Call document.initForms() method before adding form elements to document'\r\n      );\r\n    }\r\n    let opts = Object.assign({}, options);\r\n    if (type !== null) {\r\n      opts = this._resolveType(type, options);\r\n    }\r\n    opts = this._resolveFlags(opts);\r\n    opts = this._resolveJustify(opts);\r\n    opts = this._resolveFont(opts);\r\n    opts = this._resolveStrings(opts);\r\n    opts = this._resolveColors(opts);\r\n    opts = this._resolveFormat(opts);\r\n    opts.T = new String(name);\r\n    if (opts.parent) {\r\n      opts.Parent = opts.parent;\r\n      delete opts.parent;\r\n    }\r\n    return opts;\r\n  },\r\n\r\n  _resolveType(type, opts) {\r\n    if (type === 'text') {\r\n      opts.FT = 'Tx';\r\n    } else if (type === 'pushButton') {\r\n      opts.FT = 'Btn';\r\n      opts.pushButton = true;\r\n    } else if (type === 'radioButton') {\r\n      opts.FT = 'Btn';\r\n      opts.radioButton = true;\r\n    } else if (type === 'checkbox') {\r\n      opts.FT = 'Btn';\r\n    } else if (type === 'combo') {\r\n      opts.FT = 'Ch';\r\n      opts.combo = true;\r\n    } else if (type === 'list') {\r\n      opts.FT = 'Ch';\r\n    } else {\r\n      throw new Error(`Invalid form annotation type '${type}'`);\r\n    }\r\n    return opts;\r\n  },\r\n\r\n  _resolveFormat(opts) {\r\n    const f = opts.format;\r\n    if (f && f.type) {\r\n      let fnKeystroke;\r\n      let fnFormat;\r\n      let params = '';\r\n      if (FORMAT_SPECIAL[f.type] !== undefined) {\r\n        fnKeystroke = `AFSpecial_Keystroke`;\r\n        fnFormat = `AFSpecial_Format`;\r\n        params = FORMAT_SPECIAL[f.type];\r\n      } else {\r\n        let format = f.type.charAt(0).toUpperCase() + f.type.slice(1);\r\n        fnKeystroke = `AF${format}_Keystroke`;\r\n        fnFormat = `AF${format}_Format`;\r\n\r\n        if (f.type === 'date') {\r\n          fnKeystroke += 'Ex';\r\n          params = String(f.param);\r\n        } else if (f.type === 'time') {\r\n          params = String(f.param);\r\n        } else if (f.type === 'number') {\r\n          let p = Object.assign({}, FORMAT_DEFAULT.number, f);\r\n          params = String(\r\n            [\r\n              String(p.nDec),\r\n              p.sepComma ? '0' : '1',\r\n              '\"' + p.negStyle + '\"',\r\n              'null',\r\n              '\"' + p.currency + '\"',\r\n              String(p.currencyPrepend)\r\n            ].join(',')\r\n          );\r\n        } else if (f.type === 'percent') {\r\n          let p = Object.assign({}, FORMAT_DEFAULT.percent, f);\r\n          params = String([String(p.nDec), p.sepComma ? '0' : '1'].join(','));\r\n        }\r\n      }\r\n      opts.AA = opts.AA ? opts.AA : {};\r\n      opts.AA.K = {\r\n        S: 'JavaScript',\r\n        JS: new String(`${fnKeystroke}(${params});`)\r\n      };\r\n      opts.AA.F = {\r\n        S: 'JavaScript',\r\n        JS: new String(`${fnFormat}(${params});`)\r\n      };\r\n    }\r\n    delete opts.format;\r\n    return opts;\r\n  },\r\n\r\n  _resolveColors(opts) {\r\n    let color = this._normalizeColor(opts.backgroundColor);\r\n    if (color) {\r\n      if (!opts.MK) {\r\n        opts.MK = {};\r\n      }\r\n      opts.MK.BG = color;\r\n    }\r\n    color = this._normalizeColor(opts.borderColor);\r\n    if (color) {\r\n      if (!opts.MK) {\r\n        opts.MK = {};\r\n      }\r\n      opts.MK.BC = color;\r\n    }\r\n    delete opts.backgroundColor;\r\n    delete opts.borderColor;\r\n    return opts;\r\n  },\r\n\r\n  _resolveFlags(options) {\r\n    let result = 0;\r\n    Object.keys(options).forEach(key => {\r\n      if (FIELD_FLAGS[key]) {\r\n        if (options[key]) {\r\n          result |= FIELD_FLAGS[key];\r\n        }\r\n        delete options[key];\r\n      }\r\n    });\r\n    if (result !== 0) {\r\n      options.Ff = options.Ff ? options.Ff : 0;\r\n      options.Ff |= result;\r\n    }\r\n    return options;\r\n  },\r\n\r\n  _resolveJustify(options) {\r\n    let result = 0;\r\n    if (options.align !== undefined) {\r\n      if (typeof FIELD_JUSTIFY[options.align] === 'number') {\r\n        result = FIELD_JUSTIFY[options.align];\r\n      }\r\n      delete options.align;\r\n    }\r\n    if (result !== 0) {\r\n      options.Q = result; // default\r\n    }\r\n    return options;\r\n  },\r\n\r\n  _resolveFont(options) {\r\n    // add current font to document-level AcroForm dict if necessary\r\n    if (this._acroform.fonts[this._font.id] === null) {\r\n      this._acroform.fonts[this._font.id] = this._font.ref();\r\n    }\r\n\r\n    // add current font to field's resource dict (RD) if not the default acroform font\r\n    if (this._acroform.defaultFont !== this._font.name) {\r\n      options.DR = { Font: {} };\r\n\r\n      // Get the fontSize option. If not set use auto sizing\r\n      const fontSize = options.fontSize || 0;\r\n\r\n      options.DR.Font[this._font.id] = this._font.ref();\r\n      options.DA = new String(`/${this._font.id} ${fontSize} Tf 0 g`);\r\n    }\r\n    return options;\r\n  },\r\n\r\n  _resolveStrings(options) {\r\n    let select = [];\r\n    function appendChoices(a) {\r\n      if (Array.isArray(a)) {\r\n        for (let idx = 0; idx < a.length; idx++) {\r\n          if (typeof a[idx] === 'string') {\r\n            select.push(new String(a[idx]));\r\n          } else {\r\n            select.push(a[idx]);\r\n          }\r\n        }\r\n      }\r\n    }\r\n    appendChoices(options.Opt);\r\n    if (options.select) {\r\n      appendChoices(options.select);\r\n      delete options.select;\r\n    }\r\n    if (select.length) {\r\n      options.Opt = select;\r\n    }\r\n\r\n    Object.keys(VALUE_MAP).forEach(key => {\r\n      if (options[key] !== undefined) {\r\n        options[VALUE_MAP[key]] = options[key];\r\n        delete options[key];\r\n      }\r\n    });\r\n    ['V', 'DV'].forEach(key => {\r\n      if (typeof options[key] === 'string') {\r\n        options[key] = new String(options[key]);\r\n      }\r\n    });\r\n\r\n    if (options.MK && options.MK.CA) {\r\n      options.MK.CA = new String(options.MK.CA);\r\n    }\r\n    if (options.label) {\r\n      options.MK = options.MK ? options.MK : {};\r\n      options.MK.CA = new String(options.label);\r\n      delete options.label;\r\n    }\r\n    return options;\r\n  }\r\n};\r\n", "import fs from 'fs';\r\nimport CryptoJS from 'crypto-js';\r\n\r\nexport default {\r\n  /**\r\n   * Embed contents of `src` in PDF\r\n   * @param {Buffer | ArrayBuffer | string} src input Buffer, ArrayBuffer, base64 encoded string or path to file\r\n   * @param {object} options\r\n   *  * options.name: filename to be shown in PDF, will use `src` if none set\r\n   *  * options.type: filetype to be shown in PDF\r\n   *  * options.description: description to be shown in PDF\r\n   *  * options.hidden: if true, do not add attachment to EmbeddedFiles dictionary. Useful for file attachment annotations\r\n   *  * options.creationDate: override creation date\r\n   *  * options.modifiedDate: override modified date\r\n   * @returns filespec reference\r\n   */\r\n  file(src, options = {}) {\r\n    options.name = options.name || src;\r\n\r\n    const refBody = {\r\n      Type: 'EmbeddedFile',\r\n      Params: {}\r\n    };\r\n    let data;\r\n\r\n    if (!src) {\r\n      throw new Error('No src specified');\r\n    }\r\n    if (Buffer.isBuffer(src)) {\r\n      data = src;\r\n    } else if (src instanceof ArrayBuffer) {\r\n      data = Buffer.from(new Uint8Array(src));\r\n    } else {\r\n      let match;\r\n      if ((match = /^data:(.*?);base64,(.*)$/.exec(src))) {\r\n        if (match[1]) {\r\n          refBody.Subtype = match[1].replace('/', '#2F');\r\n        }\r\n        data = Buffer.from(match[2], 'base64');\r\n      } else {\r\n        data = fs.readFileSync(src);\r\n        if (!data) {\r\n          throw new Error(`Could not read contents of file at filepath ${src}`);\r\n        }\r\n\r\n        // update CreationDate and ModDate\r\n        const { birthtime, ctime } = fs.statSync(src);\r\n        refBody.Params.CreationDate = birthtime;\r\n        refBody.Params.ModDate = ctime;\r\n      }\r\n    }\r\n\r\n    // override creation date and modified date\r\n    if (options.creationDate instanceof Date) {\r\n      refBody.Params.CreationDate = options.creationDate;\r\n    }\r\n    if (options.modifiedDate instanceof Date) {\r\n      refBody.Params.ModDate = options.modifiedDate;\r\n    }\r\n    // add optional subtype\r\n    if (options.type) {\r\n      refBody.Subtype = options.type.replace('/', '#2F');\r\n    }\r\n\r\n    // add checksum and size information\r\n    const checksum = CryptoJS.MD5(\r\n      CryptoJS.lib.WordArray.create(new Uint8Array(data))\r\n    );\r\n    refBody.Params.CheckSum = new String(checksum);\r\n    refBody.Params.Size = data.byteLength;\r\n\r\n    // save some space when embedding the same file again\r\n    // if a file with the same name and metadata exists, reuse its reference\r\n    let ref;\r\n    if (!this._fileRegistry) this._fileRegistry = {};\r\n    let file = this._fileRegistry[options.name];\r\n    if (file && isEqual(refBody, file)) {\r\n      ref = file.ref;\r\n    } else {\r\n      ref = this.ref(refBody);\r\n      ref.end(data);\r\n\r\n      this._fileRegistry[options.name] = { ...refBody, ref };\r\n    }\r\n    // add filespec for embedded file\r\n    const fileSpecBody = {\r\n      Type: 'Filespec',\r\n      F: new String(options.name),\r\n      EF: { F: ref },\r\n      UF: new String(options.name)\r\n    };\r\n    if (options.description) {\r\n      fileSpecBody.Desc = new String(options.description);\r\n    }\r\n    const filespec = this.ref(fileSpecBody);\r\n    filespec.end();\r\n\r\n    if (!options.hidden) {\r\n      this.addNamedEmbeddedFile(options.name, filespec);\r\n    }\r\n\r\n    return filespec;\r\n  }\r\n};\r\n\r\n/** check two embedded file metadata objects for equality */\r\nfunction isEqual(a, b) {\r\n  return (\r\n    a.Subtype === b.Subtype &&\r\n    a.Params.CheckSum.toString() === b.Params.CheckSum.toString() &&\r\n    a.Params.Size === b.Params.Size &&\r\n    a.Params.CreationDate === b.Params.CreationDate &&\r\n    a.Params.ModDate === b.Params.ModDate\r\n  );\r\n}\r\n", "import fs from 'fs';\r\n\r\nexport default {\r\n\r\n    initPDFA(pSubset) {\r\n        if (pSubset.charAt(pSubset.length - 3) === '-') {\r\n            this.subset_conformance = pSubset.charAt(pSubset.length - 1).toUpperCase();\r\n            this.subset = parseInt(pSubset.charAt(pSubset.length - 2));\r\n        } else {\r\n            // Default to Basic conformance when user doesn't specify\r\n            this.subset_conformance = 'B';\r\n            this.subset = parseInt(pSubset.charAt(pSubset.length - 1));\r\n        }\r\n    },\r\n\r\n    endSubset() {\r\n        this._addPdfaMetadata();\r\n        const jsPath = `${__dirname}/data/sRGB_IEC61966_2_1.icc`\r\n        const jestPath = `${__dirname}/../color_profiles/sRGB_IEC61966_2_1.icc`\r\n        this._addColorOutputIntent(fs.existsSync(jsPath) ? jsPath : jestPath);\r\n    },\r\n\r\n    _addColorOutputIntent(pICCPath) {\r\n        const iccProfile = fs.readFileSync(pICCPath);\r\n\r\n        const colorProfileRef = this.ref({\r\n            Length: iccProfile.length,\r\n            N: 3\r\n        });\r\n        colorProfileRef.write(iccProfile);\r\n        colorProfileRef.end();\r\n\r\n        const intentRef = this.ref({\r\n            Type: 'OutputIntent',\r\n            S: 'GTS_PDFA1',\r\n            Info: new String('sRGB IEC61966-2.1'),\r\n            OutputConditionIdentifier: new String('sRGB IEC61966-2.1'),\r\n            DestOutputProfile: colorProfileRef,\r\n        });\r\n        intentRef.end();\r\n\r\n        this._root.data.OutputIntents = [intentRef];\r\n    },\r\n\r\n    _getPdfaid() {\r\n        return `\r\n        <rdf:Description xmlns:pdfaid=\"http://www.aiim.org/pdfa/ns/id/\" rdf:about=\"\">\r\n            <pdfaid:part>${this.subset}</pdfaid:part>\r\n            <pdfaid:conformance>${this.subset_conformance}</pdfaid:conformance>\r\n        </rdf:Description>\r\n        `;\r\n    },\r\n\r\n    _addPdfaMetadata() {\r\n        this.appendXML(this._getPdfaid());\r\n    },\r\n\r\n}", "\r\nexport default {\r\n\r\n    initPDFUA() {\r\n        this.subset = 1;\r\n    },\r\n\r\n    endSubset() {\r\n        this._addPdfuaMetadata();\r\n    },\r\n\r\n    _addPdfuaMetadata() {\r\n        this.appendXML(this._getPdfuaid());\r\n    },\r\n\r\n    _getPdfuaid() {\r\n        return `\r\n        <rdf:Description xmlns:pdfuaid=\"http://www.aiim.org/pdfua/ns/id/\" rdf:about=\"\">\r\n            <pdfuaid:part>${this.subset}</pdfuaid:part>\r\n        </rdf:Description>\r\n        `;\r\n    },\r\n\r\n}", "import PDFA from './pdfa';\r\nimport PDFUA from './pdfua';\r\n\r\nexport default {\r\n    _importSubset(subset) {\r\n        Object.assign(this, subset)\r\n    },\r\n\r\n    initSubset(options) {\r\n\r\n        switch (options.subset) {\r\n            case 'PDF/A-1':\r\n            case 'PDF/A-1a':\r\n            case 'PDF/A-1b':\r\n            case 'PDF/A-2':\r\n            case 'PDF/A-2a':\r\n            case 'PDF/A-2b':\r\n            case 'PDF/A-3':\r\n            case 'PDF/A-3a':\r\n            case 'PDF/A-3b':\r\n                this._importSubset(PDFA);\r\n                this.initPDFA(options.subset);\r\n                break;\r\n            case 'PDF/UA':\r\n                this._importSubset(PDFUA);\r\n                this.initPDFUA();\r\n                break;\r\n        }\r\n    }\r\n}", "\r\nclass PDFMetadata {\r\n    constructor() {\r\n        this._metadata = `\r\n        <?xpacket begin=\"\\ufeff\" id=\"W5M0MpCehiHzreSzNTczkc9d\"?>\r\n            <x:xmpmeta xmlns:x=\"adobe:ns:meta/\">\r\n                <rdf:RDF xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\r\n        `;\r\n    }\r\n    \r\n    _closeTags() {\r\n        this._metadata = this._metadata.concat(`\r\n                </rdf:RDF>\r\n            </x:xmpmeta>\r\n        <?xpacket end=\"w\"?>\r\n        `);\r\n    }\r\n\r\n    append(xml, newline=true) {\r\n        this._metadata = this._metadata.concat(xml); \r\n        if (newline)\r\n            this._metadata = this._metadata.concat('\\n'); \r\n    }\r\n\r\n    getXML() { return this._metadata; }\r\n\r\n    getLength() { return this._metadata.length; }\r\n\r\n    end() {\r\n        this._closeTags();\r\n        this._metadata = this._metadata.trim();\r\n    }\r\n}\r\n\r\nexport default PDFMetadata;", "import PDFMetadata from \"../metadata\"\r\n\r\nexport default {\r\n    initMetadata() {\r\n        this.metadata = new PDFMetadata();\r\n    },\r\n\r\n    appendXML(xml, newline=true) { this.metadata.append(xml,newline); },\r\n\r\n    _addInfo() {\r\n        this.appendXML(`\r\n        <rdf:Description rdf:about=\"\" xmlns:xmp=\"http://ns.adobe.com/xap/1.0/\">\r\n            <xmp:CreateDate>${this.info.CreationDate.toISOString().split('.')[0]+\"Z\"}</xmp:CreateDate>\r\n            <xmp:CreatorTool>${this.info.Creator}</xmp:CreatorTool>\r\n        </rdf:Description>\r\n        `\r\n        );\r\n\r\n        if (this.info.Title || this.info.Author || this.info.Subject) {\r\n            this.appendXML(`\r\n            <rdf:Description rdf:about=\"\" xmlns:dc=\"http://purl.org/dc/elements/1.1/\">\r\n            `);\r\n            \r\n            if (this.info.Title) {\r\n                this.appendXML(`\r\n                <dc:title>\r\n                    <rdf:Alt>\r\n                        <rdf:li xml:lang=\"x-default\">${this.info.Title}</rdf:li>\r\n                    </rdf:Alt>\r\n                </dc:title>\r\n                `);\r\n            }\r\n\r\n            if (this.info.Author) {\r\n                this.appendXML(`\r\n                <dc:creator>\r\n                    <rdf:Seq>\r\n                        <rdf:li>${this.info.Author}</rdf:li>\r\n                    </rdf:Seq>\r\n                </dc:creator>\r\n                `);\r\n            }\r\n\r\n            if (this.info.Subject) {\r\n                this.appendXML(`\r\n                <dc:description>\r\n                    <rdf:Alt>\r\n                        <rdf:li xml:lang=\"x-default\">${this.info.Subject}</rdf:li>\r\n                    </rdf:Alt>\r\n                </dc:description>\r\n                `);\r\n            }\r\n\r\n            this.appendXML(`\r\n            </rdf:Description>\r\n            `);\r\n        }\r\n\r\n        this.appendXML(`\r\n        <rdf:Description rdf:about=\"\" xmlns:pdf=\"http://ns.adobe.com/pdf/1.3/\">\r\n            <pdf:Producer>${this.info.Creator}</pdf:Producer>`, false);\r\n\r\n        if (this.info.Keywords) {\r\n            this.appendXML(`\r\n            <pdf:Keywords>${this.info.Keywords}</pdf:Keywords>`, false);\r\n        }\r\n\r\n        this.appendXML(`\r\n        </rdf:Description>\r\n        `);\r\n    },\r\n\r\n    endMetadata() {\r\n        this._addInfo();\r\n    \r\n        this.metadata.end();\r\n\r\n        /*\r\n        Metadata was introduced in PDF 1.4, so adding it to 1.3 \r\n        will likely only take up more space.\r\n        */\r\n        if (this.version != 1.3) {\r\n            this.metadataRef = this.ref({\r\n                length: this.metadata.getLength(),\r\n                Type: 'Metadata',\r\n                Subtype: 'XML'\r\n            });\r\n            this.metadataRef.compress = false;\r\n            this.metadataRef.write(Buffer.from(this.metadata.getXML(), 'utf-8'));\r\n            this.metadataRef.end();\r\n            this._root.data.Metadata = this.metadataRef;\r\n        }\r\n    }\r\n}", "/*\r\nPDFDocument - represents an entire PDF document\r\nBy <PERSON>\r\n*/\r\n\r\nimport stream from 'stream';\r\nimport fs from 'fs';\r\nimport PDFObject from './object';\r\nimport PDFReference from './reference';\r\nimport PDFPage from './page';\r\nimport PDFNameTree from './name_tree';\r\nimport PDFSecurity from './security';\r\nimport ColorMixin from './mixins/color';\r\nimport VectorMixin from './mixins/vector';\r\nimport FontsMixin from './mixins/fonts';\r\nimport TextMixin from './mixins/text';\r\nimport ImagesMixin from './mixins/images';\r\nimport AnnotationsMixin from './mixins/annotations';\r\nimport OutlineMixin from './mixins/outline';\r\nimport MarkingsMixin from './mixins/markings';\r\nimport AcroFormMixin from './mixins/acroform';\r\nimport AttachmentsMixin from './mixins/attachments';\r\nimport LineWrapper from './line_wrapper';\r\nimport SubsetMixin from './mixins/subsets';\r\nimport MetadataMixin from './mixins/metadata';\r\n\r\nclass PDFDocument extends stream.Readable {\r\n  constructor(options = {}) {\r\n    super(options);\r\n    this.options = options;\r\n\r\n    // PDF version\r\n    switch (options.pdfVersion) {\r\n      case '1.4':\r\n        this.version = 1.4;\r\n        break;\r\n      case '1.5':\r\n        this.version = 1.5;\r\n        break;\r\n      case '1.6':\r\n        this.version = 1.6;\r\n        break;\r\n      case '1.7':\r\n      case '1.7ext3':\r\n        this.version = 1.7;\r\n        break;\r\n      default:\r\n        this.version = 1.3;\r\n        break;\r\n    }\r\n\r\n    // Whether streams should be compressed\r\n    this.compress =\r\n      this.options.compress != null ? this.options.compress : true;\r\n\r\n    this._pageBuffer = [];\r\n    this._pageBufferStart = 0;\r\n\r\n    // The PDF object store\r\n    this._offsets = [];\r\n    this._waiting = 0;\r\n    this._ended = false;\r\n    this._offset = 0;\r\n    const Pages = this.ref({\r\n      Type: 'Pages',\r\n      Count: 0,\r\n      Kids: []\r\n    });\r\n\r\n    const Names = this.ref({\r\n      Dests: new PDFNameTree()\r\n    });\r\n\r\n    this._root = this.ref({\r\n      Type: 'Catalog',\r\n      Pages,\r\n      Names\r\n    });\r\n\r\n    if (this.options.lang) {\r\n      this._root.data.Lang = new String(this.options.lang);\r\n    }\r\n\r\n    // The current page\r\n    this.page = null;\r\n\r\n    // Initialize mixins\r\n    this.initMetadata();\r\n    this.initColor();\r\n    this.initVector();\r\n    this.initFonts(options.font);\r\n    this.initText();\r\n    this.initImages();\r\n    this.initOutline();\r\n    this.initMarkings(options);\r\n    this.initSubset(options);\r\n\r\n    // Initialize the metadata\r\n    this.info = {\r\n      Producer: 'PDFKit',\r\n      Creator: 'PDFKit',\r\n      CreationDate: new Date()\r\n    };\r\n\r\n    if (this.options.info) {\r\n      for (let key in this.options.info) {\r\n        const val = this.options.info[key];\r\n        this.info[key] = val;\r\n      }\r\n    }\r\n\r\n    if (this.options.displayTitle) {\r\n      this._root.data.ViewerPreferences = this.ref({\r\n        DisplayDocTitle: true\r\n      });\r\n    }\r\n\r\n    // Generate file ID\r\n    this._id = PDFSecurity.generateFileID(this.info);\r\n\r\n    // Initialize security settings\r\n    this._security = PDFSecurity.create(this, options);\r\n\r\n    // Write the header\r\n    // PDF version\r\n    this._write(`%PDF-${this.version}`);\r\n\r\n    // 4 binary chars, as recommended by the spec\r\n    this._write('%\\xFF\\xFF\\xFF\\xFF');\r\n\r\n    // Add the first page\r\n    if (this.options.autoFirstPage !== false) {\r\n      this.addPage();\r\n    }\r\n  }\r\n\r\n  addPage(options) {\r\n    if (options == null) {\r\n      ({ options } = this);\r\n    }\r\n\r\n    // end the current page if needed\r\n    if (!this.options.bufferPages) {\r\n      this.flushPages();\r\n    }\r\n\r\n    // create a page object\r\n    this.page = new PDFPage(this, options);\r\n    this._pageBuffer.push(this.page);\r\n\r\n    // add the page to the object store\r\n    const pages = this._root.data.Pages.data;\r\n    pages.Kids.push(this.page.dictionary);\r\n    pages.Count++;\r\n\r\n    // reset x and y coordinates\r\n    this.x = this.page.margins.left;\r\n    this.y = this.page.margins.top;\r\n\r\n    // flip PDF coordinate system so that the origin is in\r\n    // the top left rather than the bottom left\r\n    this._ctm = [1, 0, 0, 1, 0, 0];\r\n    this.transform(1, 0, 0, -1, 0, this.page.height);\r\n\r\n    this.emit('pageAdded');\r\n\r\n    return this;\r\n  }\r\n\r\n  continueOnNewPage(options) {\r\n    const pageMarkings = this.endPageMarkings(this.page);\r\n\r\n    this.addPage(options);\r\n\r\n    this.initPageMarkings(pageMarkings);\r\n\r\n    return this;\r\n  }\r\n\r\n  bufferedPageRange() {\r\n    return { start: this._pageBufferStart, count: this._pageBuffer.length };\r\n  }\r\n\r\n  switchToPage(n) {\r\n    let page;\r\n    if (!(page = this._pageBuffer[n - this._pageBufferStart])) {\r\n      throw new Error(\r\n        `switchToPage(${n}) out of bounds, current buffer covers pages ${\r\n          this._pageBufferStart\r\n        } to ${this._pageBufferStart + this._pageBuffer.length - 1}`\r\n      );\r\n    }\r\n\r\n    return (this.page = page);\r\n  }\r\n\r\n  flushPages() {\r\n    // this local variable exists so we're future-proof against\r\n    // reentrant calls to flushPages.\r\n    const pages = this._pageBuffer;\r\n    this._pageBuffer = [];\r\n    this._pageBufferStart += pages.length;\r\n    for (let page of pages) {\r\n      this.endPageMarkings(page);\r\n      page.end();\r\n    }\r\n  }\r\n\r\n  addNamedDestination(name, ...args) {\r\n    if (args.length === 0) {\r\n      args = ['XYZ', null, null, null];\r\n    }\r\n    if (args[0] === 'XYZ' && args[2] !== null) {\r\n      args[2] = this.page.height - args[2];\r\n    }\r\n    args.unshift(this.page.dictionary);\r\n    this._root.data.Names.data.Dests.add(name, args);\r\n  }\r\n\r\n  addNamedEmbeddedFile(name, ref) {\r\n    if (!this._root.data.Names.data.EmbeddedFiles) {\r\n      // disabling /Limits for this tree fixes attachments not showing in Adobe Reader\r\n      this._root.data.Names.data.EmbeddedFiles = new PDFNameTree({ limits: false });\r\n    }\r\n\r\n    // add filespec to EmbeddedFiles\r\n    this._root.data.Names.data.EmbeddedFiles.add(name, ref);\r\n  }\r\n\r\n  addNamedJavaScript(name, js) {\r\n    if (!this._root.data.Names.data.JavaScript) {\r\n      this._root.data.Names.data.JavaScript = new PDFNameTree();\r\n    }\r\n    let data = {\r\n      JS: new String(js),\r\n      S: 'JavaScript'\r\n    };\r\n    this._root.data.Names.data.JavaScript.add(name, data);\r\n  }\r\n\r\n  ref(data) {\r\n    const ref = new PDFReference(this, this._offsets.length + 1, data);\r\n    this._offsets.push(null); // placeholder for this object's offset once it is finalized\r\n    this._waiting++;\r\n    return ref;\r\n  }\r\n\r\n  _read() {}\r\n  // do nothing, but this method is required by node\r\n\r\n  _write(data) {\r\n    if (!Buffer.isBuffer(data)) {\r\n      data = Buffer.from(data + '\\n', 'binary');\r\n    }\r\n\r\n    this.push(data);\r\n    return (this._offset += data.length);\r\n  }\r\n\r\n  addContent(data) {\r\n    this.page.write(data);\r\n    return this;\r\n  }\r\n\r\n  _refEnd(ref) {\r\n    this._offsets[ref.id - 1] = ref.offset;\r\n    if (--this._waiting === 0 && this._ended) {\r\n      this._finalize();\r\n      return (this._ended = false);\r\n    }\r\n  }\r\n\r\n  write(filename, fn) {\r\n    // print a deprecation warning with a stacktrace\r\n    const err = new Error(`\\\r\nPDFDocument#write is deprecated, and will be removed in a future version of PDFKit. \\\r\nPlease pipe the document into a Node stream.\\\r\n`);\r\n\r\n    console.warn(err.stack);\r\n\r\n    this.pipe(fs.createWriteStream(filename));\r\n    this.end();\r\n    return this.once('end', fn);\r\n  }\r\n\r\n  end() {\r\n    this.flushPages();\r\n\r\n    this._info = this.ref();\r\n    for (let key in this.info) {\r\n      let val = this.info[key];\r\n      if (typeof val === 'string') {\r\n        val = new String(val);\r\n      }\r\n\r\n      let entry = this.ref(val);\r\n      entry.end();\r\n\r\n      this._info.data[key] = entry;\r\n    }\r\n\r\n    this._info.end();\r\n\r\n    for (let name in this._fontFamilies) {\r\n      const font = this._fontFamilies[name];\r\n      font.finalize();\r\n    }\r\n\r\n    this.endOutline();\r\n    this.endMarkings();\r\n\r\n    if (this.subset) {\r\n      this.endSubset();\r\n    }\r\n\r\n    this.endMetadata();\r\n\r\n    this._root.end();\r\n    this._root.data.Pages.end();\r\n    this._root.data.Names.end();\r\n    this.endAcroForm();\r\n\r\n    if (this._root.data.ViewerPreferences) {\r\n      this._root.data.ViewerPreferences.end();\r\n    }\r\n\r\n    if (this._security) {\r\n      this._security.end();\r\n    }\r\n\r\n    if (this._waiting === 0) {\r\n      return this._finalize();\r\n    } else {\r\n      return (this._ended = true);\r\n    }\r\n  }\r\n\r\n  _finalize() {\r\n    // generate xref\r\n    const xRefOffset = this._offset;\r\n    this._write('xref');\r\n    this._write(`0 ${this._offsets.length + 1}`);\r\n    this._write('0000000000 65535 f ');\r\n\r\n    for (let offset of this._offsets) {\r\n      offset = `0000000000${offset}`.slice(-10);\r\n      this._write(offset + ' 00000 n ');\r\n    }\r\n\r\n    // trailer\r\n    const trailer = {\r\n      Size: this._offsets.length + 1,\r\n      Root: this._root,\r\n      Info: this._info,\r\n      ID: [this._id, this._id]\r\n    };\r\n    if (this._security) {\r\n      trailer.Encrypt = this._security.dictionary;\r\n    }\r\n\r\n    this._write('trailer');\r\n    this._write(PDFObject.convert(trailer));\r\n\r\n    this._write('startxref');\r\n    this._write(`${xRefOffset}`);\r\n    this._write('%%EOF');\r\n\r\n    // end the stream\r\n    return this.push(null);\r\n  }\r\n\r\n  toString() {\r\n    return '[object PDFDocument]';\r\n  }\r\n}\r\n\r\nconst mixin = methods => {\r\n  Object.assign(PDFDocument.prototype, methods);\r\n};\r\n\r\nmixin(MetadataMixin);\r\nmixin(ColorMixin);\r\nmixin(VectorMixin);\r\nmixin(FontsMixin);\r\nmixin(TextMixin);\r\nmixin(ImagesMixin);\r\nmixin(AnnotationsMixin);\r\nmixin(OutlineMixin);\r\nmixin(MarkingsMixin);\r\nmixin(AcroFormMixin);\r\nmixin(AttachmentsMixin);\r\nmixin(SubsetMixin);\r\n\r\nPDFDocument.LineWrapper = LineWrapper;\r\n\r\nexport default PDFDocument;\r\n"], "names": ["PDFAbstractReference", "toString", "Error", "PDFTree", "constructor", "options", "_items", "limits", "add", "key", "val", "get", "sortedKeys", "Object", "keys", "sort", "a", "b", "_compareKeys", "out", "length", "first", "last", "push", "PDFObject", "convert", "_dataForKey", "_keysName", "join", "pad", "str", "Array", "slice", "escapableRe", "escapable", "swapBytes", "buff", "l", "i", "end", "object", "encryptFn", "String", "string", "isUnicode", "charCodeAt", "stringBuffer", "<PERSON><PERSON><PERSON>", "from", "valueOf", "replace", "c", "<PERSON><PERSON><PERSON><PERSON>", "Date", "getUTCFullYear", "getUTCMonth", "getUTCDate", "getUTCHours", "getUTCMinutes", "getUTCSeconds", "isArray", "items", "map", "e", "call", "number", "n", "Math", "round", "PDFReference", "document", "id", "data", "gen", "compress", "Filter", "uncompressedLength", "buffer", "write", "chunk", "Length", "finalize", "offset", "_offset", "_security", "getEncryptFn", "concat", "zlib", "deflateSync", "_write", "_refEnd", "DEFAULT_MARGINS", "top", "left", "bottom", "right", "SIZES", "A0", "A1", "A2", "A3", "A4", "A5", "A6", "A7", "A8", "A9", "A10", "B0", "B1", "B2", "B3", "B4", "B5", "B6", "B7", "B8", "B9", "B10", "C0", "C1", "C2", "C3", "C4", "C5", "C6", "C7", "C8", "C9", "C10", "RA0", "RA1", "RA2", "RA3", "RA4", "SRA0", "SRA1", "SRA2", "SRA3", "SRA4", "EXECUTIVE", "FOLIO", "LEGAL", "LETTER", "TABLOID", "PDFPage", "size", "layout", "margin", "margins", "dimensions", "toUpperCase", "width", "height", "content", "ref", "resources", "ProcSet", "dictionary", "Type", "Parent", "_root", "Pages", "MediaBox", "Contents", "Resources", "markings", "fonts", "Font", "xobjects", "XObject", "ext_gstates", "ExtGState", "patterns", "Pattern", "colorSpaces", "ColorSpace", "annotations", "Ann<PERSON>", "structParentTreeKey", "StructParents", "createStructParentTreeNextKey", "maxY", "PDFNameTree", "localeCompare", "k", "inRange", "value", "rangeGroup", "startRange", "endRange", "<PERSON><PERSON><PERSON><PERSON>", "floor", "arrayIndex", "unassigned_code_points", "isUnassignedCodePoint", "character", "commonly_mapped_to_nothing", "isCommonlyMappedToNothing", "non_ASCII_space_characters", "isNonASCIISpaceCharacter", "non_ASCII_controls_characters", "non_character_codepoints", "prohibited_characters", "isProhibitedCharacter", "bidirectional_r_al", "isBidirectionalRAL", "bidirectional_l", "isBidirectionalL", "mapping2space", "mapping2nothing", "getCodePoint", "codePointAt", "x", "toCodePoints", "input", "codepoints", "before", "next", "saslprep", "opts", "TypeError", "mapped_input", "filter", "normalized_input", "fromCodePoint", "apply", "normalize", "normalized_map", "hasProhibited", "some", "allowUnassigned", "hasUnassigned", "hasBidiRAL", "hasBidiL", "isFirstBidiRAL", "isLastBidiRAL", "PDFSecurity", "generateFileID", "info", "infoStr", "CreationDate", "getTime", "hasOwnProperty", "wordArrayToBuffer", "CryptoJS", "MD5", "generateRandomWordArray", "bytes", "lib", "WordArray", "random", "create", "ownerPassword", "userPassword", "_setupEncryption", "pdfVersion", "version", "encDict", "_setupEncryptionV1V2V4", "_setupEncryptionV5", "v", "r", "permissions", "keyBits", "getPermissionsR2", "getPermissionsR3", "paddedUserPassword", "processPasswordR2R3R4", "paddedOwnerPassword", "ownerPasswordEntry", "getOwnerPasswordR2R3R4", "<PERSON><PERSON><PERSON>", "getEncryptionKeyR2R3R4", "_id", "userPasswordEntry", "getUserPasswordR2", "getUserPasswordR3R4", "V", "CF", "StdCF", "AuthEvent", "CFM", "StmF", "StrF", "R", "O", "U", "P", "processedUserPassword", "processPasswordR5", "processedOwnerPassword", "getEncryptionKeyR5", "getUserPasswordR5", "userKeySalt", "words", "userEncryptionKeyEntry", "getUserEncryptionKeyR5", "getOwnerPasswordR5", "ownerKeySalt", "ownerEncryptionKeyEntry", "getOwnerEncryptionKeyR5", "permsEntry", "getEncryptedPermissionsR5", "OE", "UE", "Perms", "obj", "digest", "clone", "sigBytes", "min", "RC4", "encrypt", "ciphertext", "iv", "mode", "CBC", "padding", "Pkcs7", "AES", "permissionObject", "printing", "modifying", "copying", "annotating", "fillingForms", "contentAccessibility", "documentAssembly", "documentId", "cipher", "xorRound", "ceil", "j", "lsbFirstWord", "validationSalt", "keySalt", "SHA256", "NoPadding", "ECB", "password", "alloc", "index", "code", "PASSWORD_PADDING", "unescape", "encodeURIComponent", "wordArray", "byteArray", "PDFGradient", "doc", "stops", "embedded", "transform", "stop", "pos", "color", "opacity", "_normalizeColor", "_colorSpace", "max", "setTransform", "m11", "m12", "m21", "m22", "dx", "dy", "embed", "m", "fn", "<PERSON><PERSON><PERSON><PERSON>", "matrix", "bounds", "encode", "FunctionType", "Domain", "N", "Functions", "Bounds", "Encode", "_gradCount", "shader", "pattern", "PatternType", "Shading", "Matrix", "grad", "opacityGradient", "pageBBox", "page", "form", "Subtype", "FormType", "BBox", "Group", "S", "CS", "Sh1", "gstate", "SMask", "G", "opacityPattern", "PaintType", "TilingType", "XStep", "YStep", "Gs1", "stroke", "m0", "m1", "m2", "m3", "m4", "m5", "_ctm", "_setColorSpace", "op", "addContent", "PDFLinearGradient", "x1", "y1", "x2", "y2", "ShadingType", "<PERSON><PERSON><PERSON>", "Function", "Extend", "PDFRadialGradient", "r1", "r2", "underlyingColorSpaces", "PDFTilingPattern", "bBox", "xStep", "yStep", "stream", "createPattern", "toFixed", "embedPatternColorSpaces", "for<PERSON>ach", "csName", "csId", "getPatternColorSpaceId", "cs", "underlyingColorspace", "_patternCount", "patternColor", "normalizedColor", "_getColorSpace", "Gradient", "initColor", "_opacityRegistry", "_opacityCount", "char<PERSON>t", "hex", "parseInt", "namedColors", "part", "_setColor", "_setColorCore", "space", "fillColor", "set", "fillOpacity", "_fillColor", "strokeColor", "strokeOpacity", "_doOpacity", "name", "ca", "CA", "linearGradient", "radialGradient", "bbox", "aliceblue", "antiquewhite", "aqua", "aquamarine", "azure", "beige", "bisque", "black", "blanche<PERSON><PERSON>", "blue", "blueviolet", "brown", "burlywood", "cadetblue", "chartreuse", "chocolate", "coral", "cornflowerblue", "cornsilk", "crimson", "cyan", "darkblue", "dark<PERSON>an", "darkgoldenrod", "darkgray", "darkgreen", "<PERSON><PERSON>rey", "<PERSON><PERSON><PERSON>", "darkmagenta", "darkolivegreen", "darkorange", "darkorchid", "darkred", "<PERSON><PERSON><PERSON>", "darkseagreen", "darkslateblue", "darkslategray", "darkslateg<PERSON>", "darkturquoise", "darkviolet", "deeppink", "deepskyblue", "dimgray", "<PERSON><PERSON><PERSON>", "dodgerblue", "firebrick", "<PERSON><PERSON><PERSON><PERSON>", "forestgreen", "fuchsia", "gainsboro", "ghostwhite", "gold", "goldenrod", "gray", "grey", "green", "greenyellow", "honeydew", "hotpink", "indianred", "indigo", "ivory", "khaki", "lavender", "lavenderblush", "lawngreen", "lemon<PERSON>ffon", "lightblue", "lightcoral", "lightcyan", "lightgoldenrodyellow", "lightgray", "lightgreen", "<PERSON><PERSON>rey", "lightpink", "<PERSON><PERSON><PERSON>", "lightseagreen", "lightskyblue", "lightslategray", "lightslategrey", "lightsteelblue", "lightyellow", "lime", "limegreen", "linen", "magenta", "maroon", "mediumaquamarine", "mediumblue", "mediumorchid", "mediumpurple", "mediumseagreen", "mediumslateblue", "mediumspringgreen", "mediumturquoise", "mediumvioletred", "midnightblue", "mintcream", "mistyrose", "moccasin", "navajowhite", "navy", "oldlace", "olive", "<PERSON><PERSON><PERSON>", "orange", "orangered", "orchid", "palegoldenrod", "palegreen", "paleturquoise", "palevioletred", "papayawhip", "peachpuff", "peru", "pink", "plum", "powderblue", "purple", "red", "rosybrown", "royalblue", "saddlebrown", "salmon", "sandybrown", "seagreen", "seashell", "sienna", "silver", "skyblue", "slateblue", "slategray", "<PERSON><PERSON><PERSON>", "snow", "springgreen", "steelblue", "tan", "teal", "thistle", "tomato", "turquoise", "violet", "wheat", "white", "whitesmoke", "yellow", "yellowgreen", "cx", "cy", "px", "py", "sx", "sy", "parameters", "A", "C", "H", "h", "L", "M", "Q", "q", "s", "T", "t", "Z", "z", "parse", "path", "cmd", "ret", "args", "curArg", "foundDecimal", "params", "includes", "commands", "runners", "moveTo", "bezierCurveTo", "quadraticCurveTo", "solveArc", "lineTo", "closePath", "y", "coords", "rx", "ry", "rot", "large", "sweep", "ex", "ey", "segs", "arcToSegments", "seg", "bez", "segmentToBezier", "rotateX", "ox", "oy", "th", "PI", "sin_th", "sin", "cos_th", "cos", "abs", "pl", "sqrt", "a00", "a01", "a10", "a11", "x0", "y0", "d", "sfactor_sq", "sfactor", "xc", "yc", "th0", "atan2", "th1", "th_arc", "segments", "result", "th2", "th3", "th_half", "x3", "y3", "SVGPath", "KAPPA", "initVector", "_ctmStack", "save", "restore", "pop", "lineWidth", "w", "_CAP_STYLES", "BUTT", "ROUND", "SQUARE", "lineCap", "_JOIN_STYLES", "MITER", "BEVEL", "lineJoin", "miterLimit", "dash", "original<PERSON>ength", "valid", "every", "Number", "isFinite", "JSON", "stringify", "phase", "undash", "cp1x", "cp1y", "cp2x", "cp2y", "cpx", "cpy", "rect", "roundedRect", "ellipse", "xe", "ye", "xm", "ym", "circle", "radius", "arc", "startAngle", "endAngle", "anticlockwise", "TWO_PI", "HALF_PI", "deltaAng", "dir", "numSegs", "segAng", "handleLen", "curAng", "deltaCx", "deltaCy", "ax", "ay", "segIdx", "polygon", "points", "shift", "point", "_windingRule", "rule", "test", "fill", "fillAndStroke", "isFillRule", "clip", "values", "translate", "rotate", "angle", "rad", "origin", "scale", "xFactor", "yFactor", "WIN_ANSI_MAP", "characters", "split", "AFMFont", "open", "filename", "fs", "readFileSync", "contents", "attributes", "glyphWidths", "boundingBoxes", "kernPairs", "char<PERSON><PERSON><PERSON>", "char", "ascender", "descender", "xHeight", "capHeight", "lineGap", "section", "line", "match", "encodeText", "text", "res", "len", "glyphsForString", "glyphs", "charCode", "characterToGlyph", "widthOfGlyph", "glyph", "getKernPair", "advancesForGlyphs", "advances", "PDFFont", "widthOfString", "lineHeight", "includeGap", "gap", "STANDARD_FONTS", "Courier", "__dirname", "Helvetica", "Symbol", "ZapfDingbats", "StandardFont", "font", "BaseFont", "Encoding", "encoded", "positions", "xAdvance", "yAdvance", "xOffset", "yOffset", "advanceWidth", "advance", "isStandardFont", "toHex", "num", "EmbeddedFont", "subset", "createSubset", "unicode", "widths", "getGlyph", "postscriptName", "unitsPerEm", "ascent", "descent", "fontLayoutCache", "layoutCache", "layoutRun", "features", "run", "position", "layoutCached", "cached", "only<PERSON><PERSON><PERSON>", "needle", "gid", "includeGlyph", "codePoints", "isCFF", "cff", "fontFile", "encodeStream", "on", "familyClass", "sFamilyClass", "undefined", "flags", "post", "isFixedPitch", "head", "macStyle", "italic", "tag", "fromCharCode", "descriptor", "FontName", "Flags", "FontBBox", "minX", "minY", "maxX", "ItalicAngle", "italicAngle", "Ascent", "Descent", "CapHeight", "XHeight", "StemV", "FontFile3", "FontFile2", "CIDSet", "CIDSetRef", "descendantFontData", "CIDSystemInfo", "Registry", "Ordering", "Supplement", "FontDescriptor", "W", "CIDToGIDMap", "descendantFont", "DescendantFonts", "ToUnicode", "toUnicodeCmap", "cmap", "entries", "chunkSize", "chunks", "ranges", "start", "PDFFontFactory", "src", "family", "fontkit", "Uint8Array", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "initFonts", "defaultFont", "_fontFamilies", "_fontCount", "_fontSize", "_font", "_registeredFonts", "cache<PERSON>ey", "fontSize", "currentLineHeight", "registerFont", "SOFT_HYPHEN", "HYPHEN", "LineWrapper", "EventEmitter", "indent", "characterSpacing", "wordSpacing", "columns", "columnGap", "spaceLeft", "startX", "startY", "column", "ellipsis", "continuedX", "once", "continued", "align", "lastLine", "paragraphGap", "wordWidth", "word", "canFit", "eachWord", "bk", "breaker", "LineBreaker", "wordWidths", "nextBreak", "shouldC<PERSON><PERSON>ue", "lbk", "fbk", "<PERSON><PERSON>row", "mustShrink", "required", "wrap", "nextY", "nextSection", "textWidth", "wc", "lc", "emitLine", "wordCount", "emit", "lh", "continueOnNewPage", "initText", "_line", "bind", "_lineGap", "moveDown", "lines", "moveUp", "_text", "lineCallback", "_initOptions", "addStructure", "structParent", "struct", "structType", "mark<PERSON><PERSON><PERSON><PERSON><PERSON>nt", "wrapper", "_wrapper", "_textOptions", "heightOfString", "Infinity", "list", "listType", "unit", "midLine", "bulletRadius", "textIndent", "itemIndent", "bulletIndent", "level", "levels", "numbers", "flatten", "item", "label", "letter", "times", "drawListItem", "listItem", "itemType", "labelType", "bodyType", "structTypes", "diff", "_fragment", "assign", "lineBreak", "trim", "spaceWidth", "baseline", "rendered<PERSON><PERSON><PERSON>", "link", "goTo", "destination", "addNamedDestination", "underline", "lineY", "strike", "oblique", "skew", "encodedWord", "positionsWord", "hadOffset", "addSegment", "cur", "flush", "MARKERS", "COLOR_SPACE_MAP", "JPEG", "marker", "readUInt16BE", "orientation", "exif", "fromBuffer", "Orientation", "bits", "channels", "colorSpace", "BitsPerComponent", "<PERSON><PERSON><PERSON>", "Height", "PNGImage", "image", "PNG", "imgData", "dataDecoded", "hasAlphaChannel", "isInterlaced", "interlace<PERSON>ethod", "Predictor", "Colors", "colors", "Columns", "palette", "transparency", "grayscale", "rgb", "mask", "indexed", "loadIndexedAlphaChannel", "splitAlphaChannel", "decodeData", "alphaChannel", "sMask", "Decode", "decodePixels", "pixels", "p", "colorCount", "pixelCount", "skipByteCount", "colorIndex", "PDFImage", "exec", "initImages", "_imageRegistry", "_imageCount", "bh", "bp", "bw", "ip", "left1", "rotateAngle", "originX", "originY", "ignoreOrientation", "openImage", "wp", "hp", "fit", "cover", "valign", "annotate", "Rect", "_convertRect", "Border", "F", "Dest", "note", "Name", "D", "url", "pages", "Kids", "URI", "_markup", "QuadPoints", "highlight", "lineAnnotation", "rectAnnotation", "ellipseAnnotation", "textAnnotation", "DA", "fileAnnotation", "file", "filespec", "hidden", "FS", "Desc", "PDFOutline", "parent", "title", "dest", "expanded", "outlineData", "children", "addItem", "endOutline", "Count", "First", "Last", "child", "Prev", "Next", "initOutline", "outline", "Outlines", "PageMode", "PDFStructureContent", "pageRef", "mcid", "refs", "structContent", "PDFStructureElement", "type", "_attached", "_ended", "_flushed", "_is<PERSON><PERSON>d<PERSON><PERSON><PERSON>", "lang", "<PERSON>", "alt", "Alt", "E", "actual", "ActualText", "_children", "setParent", "setAttached", "_addContentToParentTree", "_contentForClosure", "pageStructParents", "getStructParentTree", "parentRef", "_flush", "closure", "endMarkedContent", "_is<PERSON><PERSON>hable", "K", "_<PERSON><PERSON><PERSON>d", "Pg", "MCID", "PDFNumberTree", "initMarkings", "struct<PERSON><PERSON><PERSON><PERSON>", "tagged", "getMarkInfoDictionary", "Marked", "getStructTreeRoot", "<PERSON><PERSON><PERSON><PERSON>", "toClose", "marking", "attached", "Attached", "structElem", "structTreeRoot", "initPageMarkings", "pageMarkings", "newStructContent", "endPageMarkings", "MarkInfo", "StructTreeRoot", "<PERSON><PERSON><PERSON><PERSON>", "ParentTreeNextKey", "endMarkings", "FIELD_FLAGS", "readOnly", "noExport", "multiline", "toggleToOffButton", "radioButton", "pushButton", "combo", "edit", "multiSelect", "noSpell", "FIELD_JUSTIFY", "center", "VALUE_MAP", "defaultValue", "FORMAT_SPECIAL", "zip", "zipPlus4", "zip4", "phone", "ssn", "FORMAT_DEFAULT", "nDec", "sepComma", "negStyle", "currency", "currencyPrepend", "percent", "initForm", "_acroform", "Fields", "NeedAppearances", "DR", "AcroForm", "endAcroForm", "fontDict", "fieldRef", "_endChild", "childRef", "formField", "fieldDict", "_fieldDict", "_addToParent", "formAnnotation", "annotRef", "formText", "formPushButton", "formCombo", "formList", "formRadioButton", "formCheckbox", "_resolveType", "_resolveFlags", "_resolveJustify", "_resolveFont", "_resolveStrings", "_resolveColors", "_resolveFormat", "FT", "f", "format", "fnKeystroke", "fnFormat", "param", "AA", "JS", "backgroundColor", "MK", "BG", "borderColor", "BC", "Ff", "select", "appendChoices", "idx", "<PERSON><PERSON>", "refBody", "Params", "birthtime", "ctime", "statSync", "ModDate", "creationDate", "modifiedDate", "checksum", "CheckSum", "Size", "byteLength", "_fileRegistry", "isEqual", "fileSpecBody", "EF", "UF", "description", "addNamedEmbeddedFile", "initPDFA", "pSubset", "subset_conformance", "endSubset", "_addPdfaMetadata", "jsPath", "jest<PERSON><PERSON>", "_addColorOutputIntent", "existsSync", "p<PERSON><PERSON>ath", "iccProfile", "colorProfileRef", "intentRef", "Info", "OutputConditionIdentifier", "DestOutputProfile", "OutputIntents", "_get<PERSON>d<PERSON>id", "appendXML", "initPDFUA", "_addPdfuaMetadata", "_getPdfuaid", "_importSubset", "initSubset", "PDFA", "PDFUA", "PDFMetadata", "_metadata", "_closeTags", "append", "xml", "newline", "getXML", "<PERSON><PERSON><PERSON><PERSON>", "initMetadata", "metadata", "_addInfo", "toISOString", "Creator", "Title", "Author", "Subject", "Keywords", "endMetadata", "metadataRef", "<PERSON><PERSON><PERSON>", "PDFDocument", "Readable", "_pageBuffer", "_pageBufferStart", "_offsets", "_waiting", "Names", "Des<PERSON>", "Producer", "displayTitle", "ViewerPreferences", "DisplayDocTitle", "autoFirstPage", "addPage", "bufferPages", "flushPages", "bufferedPageRange", "count", "switchToPage", "unshift", "EmbeddedFiles", "addNamedJavaScript", "js", "JavaScript", "_read", "_finalize", "err", "console", "warn", "stack", "pipe", "createWriteStream", "_info", "entry", "xRefOffset", "trailer", "Root", "ID", "Encrypt", "mixin", "methods", "prototype", "MetadataMixin", "ColorMixin", "VectorMixin", "FontsMixin", "TextMixin", "ImagesMixin", "AnnotationsMixin", "OutlineMixin", "MarkingsMixin", "AcroFormMixin", "AttachmentsMixin", "SubsetMixin"], "mappings": ";;;;;;;;;;AAAA;;;AAIA,MAAMA,oBAAN,CAA2B;EACzBC,QAAQ,GAAG;UACH,IAAIC,KAAJ,CAAU,mCAAV,CAAN;;;;;ACNJ;;;AAIA;AAEA,MAAMC,OAAN,CAAc;EACZC,WAAW,GAAe;QAAdC,OAAc,uEAAJ,EAAI;SACnBC,MAAL,GAAc,EAAd,CADwB;;SAGnBC,MAAL,GACE,OAAOF,OAAO,CAACE,MAAf,KAA0B,SAA1B,GAAsCF,OAAO,CAACE,MAA9C,GAAuD,IADzD;;;EAIFC,GAAG,CAACC,GAAD,EAAMC,GAAN,EAAW;WACJ,KAAKJ,MAAL,CAAYG,GAAZ,IAAmBC,GAA3B;;;EAGFC,GAAG,CAACF,GAAD,EAAM;WACA,KAAKH,MAAL,CAAYG,GAAZ,CAAP;;;EAGFR,QAAQ,GAAG;;QAEHW,UAAU,GAAGC,MAAM,CAACC,IAAP,CAAY,KAAKR,MAAjB,EAAyBS,IAAzB,CAA8B,CAACC,CAAD,EAAIC,CAAJ,KAC/C,KAAKC,YAAL,CAAkBF,CAAlB,EAAqBC,CAArB,CADiB,CAAnB;QAIME,GAAG,GAAG,CAAC,IAAD,CAAZ;;QACI,KAAKZ,MAAL,IAAeK,UAAU,CAACQ,MAAX,GAAoB,CAAvC,EAA0C;UAClCC,KAAK,GAAGT,UAAU,CAAC,CAAD,CAAxB;UACEU,IAAI,GAAGV,UAAU,CAACA,UAAU,CAACQ,MAAX,GAAoB,CAArB,CADnB;MAEAD,GAAG,CAACI,IAAJ,qBACeC,SAAS,CAACC,OAAV,CAAkB,CAAC,KAAKC,WAAL,CAAiBL,KAAjB,CAAD,EAA0B,KAAKK,WAAL,CAAiBJ,IAAjB,CAA1B,CAAlB,CADf;;;IAIFH,GAAG,CAACI,IAAJ,cAAe,KAAKI,SAAL,EAAf;;SACK,IAAIlB,GAAT,IAAgBG,UAAhB,EAA4B;MAC1BO,GAAG,CAACI,IAAJ,eACSC,SAAS,CAACC,OAAV,CAAkB,KAAKC,WAAL,CAAiBjB,GAAjB,CAAlB,CADT,cACqDe,SAAS,CAACC,OAAV,CACjD,KAAKnB,MAAL,CAAYG,GAAZ,CADiD,CADrD;;;IAMFU,GAAG,CAACI,IAAJ,CAAS,GAAT;IACAJ,GAAG,CAACI,IAAJ,CAAS,IAAT;WACOJ,GAAG,CAACS,IAAJ,CAAS,IAAT,CAAP;;;EAGFV,YAAY;;;UACJ,IAAIhB,KAAJ,CAAU,mCAAV,CAAN;;;EAGFyB,SAAS,GAAG;UACJ,IAAIzB,KAAJ,CAAU,mCAAV,CAAN;;;EAGFwB,WAAW;;;UACH,IAAIxB,KAAJ,CAAU,mCAAV,CAAN;;;;;AC1DJ;;;;AAKA;AAGA,IAAM2B,GAAG,GAAG,CAACC,GAAD,EAAMV,MAAN,KAAiB,CAACW,KAAK,CAACX,MAAM,GAAG,CAAV,CAAL,CAAkBQ,IAAlB,CAAuB,GAAvB,IAA8BE,GAA/B,EAAoCE,KAApC,CAA0C,CAACZ,MAA3C,CAA7B;;AAEA,IAAMa,WAAW,GAAG,mBAApB;AACA,IAAMC,SAAS,GAAG;QACV,KADU;QAEV,KAFU;QAGV,KAHU;QAIV,KAJU;QAKV,KALU;QAMV,MANU;OAOX,KAPW;OAQX;CARP;;AAYA,IAAMC,SAAS,GAAG,SAAZA,SAAY,CAASC,IAAT,EAAe;MACzBC,CAAC,GAAGD,IAAI,CAAChB,MAAf;;MACIiB,CAAC,GAAG,IAAR,EAAc;UACN,IAAInC,KAAJ,CAAU,4BAAV,CAAN;GADF,MAEO;SACA,IAAIoC,CAAC,GAAG,CAAR,EAAWC,GAAG,GAAGF,CAAC,GAAG,CAA1B,EAA6BC,CAAC,GAAGC,GAAjC,EAAsCD,CAAC,IAAI,CAA3C,EAA8C;UACtCtB,CAAC,GAAGoB,IAAI,CAACE,CAAD,CAAd;MACAF,IAAI,CAACE,CAAD,CAAJ,GAAUF,IAAI,CAACE,CAAC,GAAG,CAAL,CAAd;MACAF,IAAI,CAACE,CAAC,GAAG,CAAL,CAAJ,GAActB,CAAd;;;;SAIGoB,IAAP;CAZF;;AAeA,MAAMZ,SAAN,CAAgB;SACPC,OAAP,CAAee,MAAf,EAAyC;QAAlBC,SAAkB,uEAAN,IAAM;;;QAEnC,OAAOD,MAAP,KAAkB,QAAtB,EAAgC;wBACnBA,MAAX,EAD8B;KAAhC,MAIO,IAAIA,MAAM,YAAYE,MAAtB,EAA8B;UAC/BC,MAAM,GAAGH,MAAb,CADmC;;UAG/BI,SAAS,GAAG,KAAhB;;WACK,IAAIN,CAAC,GAAG,CAAR,EAAWC,GAAG,GAAGI,MAAM,CAACvB,MAA7B,EAAqCkB,CAAC,GAAGC,GAAzC,EAA8CD,CAAC,EAA/C,EAAmD;YAC7CK,MAAM,CAACE,UAAP,CAAkBP,CAAlB,IAAuB,IAA3B,EAAiC;UAC/BM,SAAS,GAAG,IAAZ;;;OAN+B;;;UAY/BE,YAAJ;;UACIF,SAAJ,EAAe;QACbE,YAAY,GAAGX,SAAS,CAACY,MAAM,CAACC,IAAP,iBAAqBL,MAArB,GAA+B,SAA/B,CAAD,CAAxB;OADF,MAEO;QACLG,YAAY,GAAGC,MAAM,CAACC,IAAP,CAAYL,MAAM,CAACM,OAAP,EAAZ,EAA8B,OAA9B,CAAf;OAhBiC;;;UAoB/BR,SAAJ,EAAe;QACbE,MAAM,GAAGF,SAAS,CAACK,YAAD,CAAT,CAAwB7C,QAAxB,CAAiC,QAAjC,CAAT;OADF,MAEO;QACL0C,MAAM,GAAGG,YAAY,CAAC7C,QAAb,CAAsB,QAAtB,CAAT;OAvBiC;;;MA2BnC0C,MAAM,GAAGA,MAAM,CAACO,OAAP,CAAejB,WAAf,EAA4BkB,CAAC,IAAIjB,SAAS,CAACiB,CAAD,CAA1C,CAAT;wBAEWR,MAAX,OA7BmC;KAA9B,MAgCA,IAAII,MAAM,CAACK,QAAP,CAAgBZ,MAAhB,CAAJ,EAA6B;wBACvBA,MAAM,CAACvC,QAAP,CAAgB,KAAhB,CAAX;KADK,MAEA,IACLuC,MAAM,YAAYxC,oBAAlB,IACAwC,MAAM,YAAYrC,OAFb,EAGL;aACOqC,MAAM,CAACvC,QAAP,EAAP;KAJK,MAKA,IAAIuC,MAAM,YAAYa,IAAtB,EAA4B;UAC7BV,OAAM,GACR,YAAKd,GAAG,CAACW,MAAM,CAACc,cAAP,EAAD,EAA0B,CAA1B,CAAR,IACAzB,GAAG,CAACW,MAAM,CAACe,WAAP,KAAuB,CAAxB,EAA2B,CAA3B,CADH,GAEA1B,GAAG,CAACW,MAAM,CAACgB,UAAP,EAAD,EAAsB,CAAtB,CAFH,GAGA3B,GAAG,CAACW,MAAM,CAACiB,WAAP,EAAD,EAAuB,CAAvB,CAHH,GAIA5B,GAAG,CAACW,MAAM,CAACkB,aAAP,EAAD,EAAyB,CAAzB,CAJH,GAKA7B,GAAG,CAACW,MAAM,CAACmB,aAAP,EAAD,EAAyB,CAAzB,CALH,GAMA,GAPF,CADiC;;;UAW7BlB,SAAJ,EAAe;QACbE,OAAM,GAAGF,SAAS,CAACM,MAAM,CAACC,IAAP,CAAYL,OAAZ,EAAoB,OAApB,CAAD,CAAT,CAAwC1C,QAAxC,CAAiD,QAAjD,CAAT,CADa;;QAIb0C,OAAM,GAAGA,OAAM,CAACO,OAAP,CAAejB,WAAf,EAA4BkB,CAAC,IAAIjB,SAAS,CAACiB,CAAD,CAA1C,CAAT;;;wBAGSR,OAAX;KAlBK,MAmBA,IAAIZ,KAAK,CAAC6B,OAAN,CAAcpB,MAAd,CAAJ,EAA2B;UAC1BqB,KAAK,GAAGrB,MAAM,CAACsB,GAAP,CAAWC,CAAC,IAAIvC,SAAS,CAACC,OAAV,CAAkBsC,CAAlB,EAAqBtB,SAArB,CAAhB,EAAiDb,IAAjD,CAAsD,GAAtD,CAAd;wBACWiC,KAAX;KAFK,MAGA,IAAI,GAAG5D,QAAH,CAAY+D,IAAZ,CAAiBxB,MAAjB,MAA6B,iBAAjC,EAAoD;UACnDrB,GAAG,GAAG,CAAC,IAAD,CAAZ;;WACK,IAAIV,GAAT,IAAgB+B,MAAhB,EAAwB;YAChB9B,GAAG,GAAG8B,MAAM,CAAC/B,GAAD,CAAlB;QACAU,GAAG,CAACI,IAAJ,YAAad,GAAb,cAAoBe,SAAS,CAACC,OAAV,CAAkBf,GAAlB,EAAuB+B,SAAvB,CAApB;;;MAGFtB,GAAG,CAACI,IAAJ,CAAS,IAAT;aACOJ,GAAG,CAACS,IAAJ,CAAS,IAAT,CAAP;KARK,MASA,IAAI,OAAOY,MAAP,KAAkB,QAAtB,EAAgC;aAC9BhB,SAAS,CAACyC,MAAV,CAAiBzB,MAAjB,CAAP;KADK,MAEA;uBACKA,MAAV;;;;SAIGyB,MAAP,CAAcC,CAAd,EAAiB;QACXA,CAAC,GAAG,CAAC,IAAL,IAAaA,CAAC,GAAG,IAArB,EAA2B;aAClBC,IAAI,CAACC,KAAL,CAAWF,CAAC,GAAG,GAAf,IAAsB,GAA7B;;;UAGI,IAAIhE,KAAJ,+BAAiCgE,CAAjC,EAAN;;;;;AC/HJ;;;;AAKA;AAIA,MAAMG,YAAN,SAA2BrE,oBAA3B,CAAgD;EAC9CI,WAAW,CAACkE,QAAD,EAAWC,EAAX,EAA0B;QAAXC,IAAW,uEAAJ,EAAI;;SAE9BF,QAAL,GAAgBA,QAAhB;SACKC,EAAL,GAAUA,EAAV;SACKC,IAAL,GAAYA,IAAZ;SACKC,GAAL,GAAW,CAAX;SACKC,QAAL,GAAgB,KAAKJ,QAAL,CAAcI,QAAd,IAA0B,CAAC,KAAKF,IAAL,CAAUG,MAArD;SACKC,kBAAL,GAA0B,CAA1B;SACKC,MAAL,GAAc,EAAd;;;EAGFC,KAAK,CAACC,KAAD,EAAQ;QACP,CAAChC,MAAM,CAACK,QAAP,CAAgB2B,KAAhB,CAAL,EAA6B;MAC3BA,KAAK,GAAGhC,MAAM,CAACC,IAAP,CAAY+B,KAAK,GAAG,IAApB,EAA0B,QAA1B,CAAR;;;SAGGH,kBAAL,IAA2BG,KAAK,CAAC3D,MAAjC;;QACI,KAAKoD,IAAL,CAAUQ,MAAV,IAAoB,IAAxB,EAA8B;WACvBR,IAAL,CAAUQ,MAAV,GAAmB,CAAnB;;;SAEGH,MAAL,CAAYtD,IAAZ,CAAiBwD,KAAjB;SACKP,IAAL,CAAUQ,MAAV,IAAoBD,KAAK,CAAC3D,MAA1B;;QACI,KAAKsD,QAAT,EAAmB;aACT,KAAKF,IAAL,CAAUG,MAAV,GAAmB,aAA3B;;;;EAIJpC,GAAG,CAACwC,KAAD,EAAQ;QACLA,KAAJ,EAAW;WACJD,KAAL,CAAWC,KAAX;;;WAEK,KAAKE,QAAL,EAAP;;;EAGFA,QAAQ,GAAG;SACJC,MAAL,GAAc,KAAKZ,QAAL,CAAca,OAA5B;QAEM1C,SAAS,GAAG,KAAK6B,QAAL,CAAcc,SAAd,GACd,KAAKd,QAAL,CAAcc,SAAd,CAAwBC,YAAxB,CAAqC,KAAKd,EAA1C,EAA8C,KAAKE,GAAnD,CADc,GAEd,IAFJ;;QAII,KAAKI,MAAL,CAAYzD,MAAhB,EAAwB;WACjByD,MAAL,GAAc9B,MAAM,CAACuC,MAAP,CAAc,KAAKT,MAAnB,CAAd;;UACI,KAAKH,QAAT,EAAmB;aACZG,MAAL,GAAcU,IAAI,CAACC,WAAL,CAAiB,KAAKX,MAAtB,CAAd;;;UAGEpC,SAAJ,EAAe;aACRoC,MAAL,GAAcpC,SAAS,CAAC,KAAKoC,MAAN,CAAvB;;;WAGGL,IAAL,CAAUQ,MAAV,GAAmB,KAAKH,MAAL,CAAYzD,MAA/B;;;SAGGkD,QAAL,CAAcmB,MAAd,WAAwB,KAAKlB,EAA7B,cAAmC,KAAKE,GAAxC;;SACKH,QAAL,CAAcmB,MAAd,CAAqBjE,SAAS,CAACC,OAAV,CAAkB,KAAK+C,IAAvB,EAA6B/B,SAA7B,CAArB;;QAEI,KAAKoC,MAAL,CAAYzD,MAAhB,EAAwB;WACjBkD,QAAL,CAAcmB,MAAd,CAAqB,QAArB;;WACKnB,QAAL,CAAcmB,MAAd,CAAqB,KAAKZ,MAA1B;;WAEKA,MAAL,GAAc,EAAd,CAJsB;;WAKjBP,QAAL,CAAcmB,MAAd,CAAqB,aAArB;;;SAGGnB,QAAL,CAAcmB,MAAd,CAAqB,QAArB;;SACKnB,QAAL,CAAcoB,OAAd,CAAsB,IAAtB;;;EAEFzF,QAAQ,GAAG;qBACC,KAAKsE,EAAf,cAAqB,KAAKE,GAA1B;;;;;AC/EJ;;;;AAKA,IAAMkB,eAAe,GAAG;EACtBC,GAAG,EAAE,EADiB;EAEtBC,IAAI,EAAE,EAFgB;EAGtBC,MAAM,EAAE,EAHc;EAItBC,KAAK,EAAE;CAJT;AAOA,IAAMC,KAAK,GAAG;SACL,CAAC,OAAD,EAAU,OAAV,CADK;SAEL,CAAC,OAAD,EAAU,OAAV,CAFK;EAGZC,EAAE,EAAE,CAAC,OAAD,EAAU,OAAV,CAHQ;EAIZC,EAAE,EAAE,CAAC,OAAD,EAAU,OAAV,CAJQ;EAKZC,EAAE,EAAE,CAAC,OAAD,EAAU,OAAV,CALQ;EAMZC,EAAE,EAAE,CAAC,MAAD,EAAS,OAAT,CANQ;EAOZC,EAAE,EAAE,CAAC,MAAD,EAAS,MAAT,CAPQ;EAQZC,EAAE,EAAE,CAAC,MAAD,EAAS,MAAT,CARQ;EASZC,EAAE,EAAE,CAAC,MAAD,EAAS,MAAT,CATQ;EAUZC,EAAE,EAAE,CAAC,MAAD,EAAS,MAAT,CAVQ;EAWZC,EAAE,EAAE,CAAC,KAAD,EAAQ,MAAR,CAXQ;EAYZC,EAAE,EAAE,CAAC,MAAD,EAAS,KAAT,CAZQ;EAaZC,GAAG,EAAE,CAAC,IAAD,EAAO,MAAP,CAbO;EAcZC,EAAE,EAAE,CAAC,OAAD,EAAU,OAAV,CAdQ;EAeZC,EAAE,EAAE,CAAC,OAAD,EAAU,OAAV,CAfQ;EAgBZC,EAAE,EAAE,CAAC,OAAD,EAAU,OAAV,CAhBQ;EAiBZC,EAAE,EAAE,CAAC,OAAD,EAAU,OAAV,CAjBQ;EAkBZC,EAAE,EAAE,CAAC,MAAD,EAAS,OAAT,CAlBQ;EAmBZC,EAAE,EAAE,CAAC,KAAD,EAAQ,MAAR,CAnBQ;EAoBZC,EAAE,EAAE,CAAC,MAAD,EAAS,KAAT,CApBQ;EAqBZC,EAAE,EAAE,CAAC,MAAD,EAAS,MAAT,CArBQ;EAsBZC,EAAE,EAAE,CAAC,MAAD,EAAS,MAAT,CAtBQ;EAuBZC,EAAE,EAAE,CAAC,MAAD,EAAS,MAAT,CAvBQ;EAwBZC,GAAG,EAAE,CAAC,KAAD,EAAQ,MAAR,CAxBO;EAyBZC,EAAE,EAAE,CAAC,OAAD,EAAU,OAAV,CAzBQ;EA0BZC,EAAE,EAAE,CAAC,OAAD,EAAU,OAAV,CA1BQ;EA2BZC,EAAE,EAAE,CAAC,OAAD,EAAU,OAAV,CA3BQ;EA4BZC,EAAE,EAAE,CAAC,MAAD,EAAS,OAAT,CA5BQ;EA6BZC,EAAE,EAAE,CAAC,MAAD,EAAS,MAAT,CA7BQ;EA8BZC,EAAE,EAAE,CAAC,MAAD,EAAS,MAAT,CA9BQ;EA+BZC,EAAE,EAAE,CAAC,MAAD,EAAS,MAAT,CA/BQ;EAgCZC,EAAE,EAAE,CAAC,MAAD,EAAS,MAAT,CAhCQ;EAiCZC,EAAE,EAAE,CAAC,MAAD,EAAS,MAAT,CAjCQ;EAkCZC,EAAE,EAAE,CAAC,MAAD,EAAS,MAAT,CAlCQ;EAmCZC,GAAG,EAAE,CAAC,KAAD,EAAQ,MAAR,CAnCO;EAoCZC,GAAG,EAAE,CAAC,MAAD,EAAS,OAAT,CApCO;EAqCZC,GAAG,EAAE,CAAC,OAAD,EAAU,MAAV,CArCO;EAsCZC,GAAG,EAAE,CAAC,MAAD,EAAS,OAAT,CAtCO;EAuCZC,GAAG,EAAE,CAAC,MAAD,EAAS,MAAT,CAvCO;EAwCZC,GAAG,EAAE,CAAC,MAAD,EAAS,MAAT,CAxCO;EAyCZC,IAAI,EAAE,CAAC,OAAD,EAAU,OAAV,CAzCM;EA0CZC,IAAI,EAAE,CAAC,OAAD,EAAU,OAAV,CA1CM;EA2CZC,IAAI,EAAE,CAAC,OAAD,EAAU,OAAV,CA3CM;EA4CZC,IAAI,EAAE,CAAC,MAAD,EAAS,OAAT,CA5CM;EA6CZC,IAAI,EAAE,CAAC,KAAD,EAAQ,MAAR,CA7CM;EA8CZC,SAAS,EAAE,CAAC,MAAD,EAAS,KAAT,CA9CC;EA+CZC,KAAK,EAAE,CAAC,KAAD,EAAQ,KAAR,CA/CK;EAgDZC,KAAK,EAAE,CAAC,KAAD,EAAQ,MAAR,CAhDK;EAiDZC,MAAM,EAAE,CAAC,KAAD,EAAQ,KAAR,CAjDI;EAkDZC,OAAO,EAAE,CAAC,KAAD,EAAQ,MAAR;CAlDX;;AAqDA,MAAMC,OAAN,CAAc;EACZ7I,WAAW,CAACkE,QAAD,EAAyB;QAAdjE,OAAc,uEAAJ,EAAI;SAC7BiE,QAAL,GAAgBA,QAAhB;SACK4E,IAAL,GAAY7I,OAAO,CAAC6I,IAAR,IAAgB,QAA5B;SACKC,MAAL,GAAc9I,OAAO,CAAC8I,MAAR,IAAkB,UAAhC,CAHkC;;QAM9B,OAAO9I,OAAO,CAAC+I,MAAf,KAA0B,QAA9B,EAAwC;WACjCC,OAAL,GAAe;QACbzD,GAAG,EAAEvF,OAAO,CAAC+I,MADA;QAEbvD,IAAI,EAAExF,OAAO,CAAC+I,MAFD;QAGbtD,MAAM,EAAEzF,OAAO,CAAC+I,MAHH;QAIbrD,KAAK,EAAE1F,OAAO,CAAC+I;OAJjB,CADsC;KAAxC,MASO;WACAC,OAAL,GAAehJ,OAAO,CAACgJ,OAAR,IAAmB1D,eAAlC;KAhBgC;;;QAoB5B2D,UAAU,GAAGvH,KAAK,CAAC6B,OAAN,CAAc,KAAKsF,IAAnB,IACf,KAAKA,IADU,GAEflD,KAAK,CAAC,KAAKkD,IAAL,CAAUK,WAAV,EAAD,CAFT;SAGKC,KAAL,GAAaF,UAAU,CAAC,KAAKH,MAAL,KAAgB,UAAhB,GAA6B,CAA7B,GAAiC,CAAlC,CAAvB;SACKM,MAAL,GAAcH,UAAU,CAAC,KAAKH,MAAL,KAAgB,UAAhB,GAA6B,CAA7B,GAAiC,CAAlC,CAAxB;SAEKO,OAAL,GAAe,KAAKpF,QAAL,CAAcqF,GAAd,EAAf,CA1BkC;;SA6B7BC,SAAL,GAAiB,KAAKtF,QAAL,CAAcqF,GAAd,CAAkB;MACjCE,OAAO,EAAE,CAAC,KAAD,EAAQ,MAAR,EAAgB,QAAhB,EAA0B,QAA1B,EAAoC,QAApC;KADM,CAAjB,CA7BkC;;SAkC7BC,UAAL,GAAkB,KAAKxF,QAAL,CAAcqF,GAAd,CAAkB;MAClCI,IAAI,EAAE,MAD4B;MAElCC,MAAM,EAAE,KAAK1F,QAAL,CAAc2F,KAAd,CAAoBzF,IAApB,CAAyB0F,KAFC;MAGlCC,QAAQ,EAAE,CAAC,CAAD,EAAI,CAAJ,EAAO,KAAKX,KAAZ,EAAmB,KAAKC,MAAxB,CAHwB;MAIlCW,QAAQ,EAAE,KAAKV,OAJmB;MAKlCW,SAAS,EAAE,KAAKT;KALA,CAAlB;SAQKU,QAAL,GAAgB,EAAhB;GA3CU;;;MA+CRC,KAAJ,GAAY;QACJ/F,IAAI,GAAG,KAAKoF,SAAL,CAAepF,IAA5B;WACOA,IAAI,CAACgG,IAAL,IAAa,IAAb,GAAoBhG,IAAI,CAACgG,IAAzB,GAAiChG,IAAI,CAACgG,IAAL,GAAY,EAApD;;;MAGEC,QAAJ,GAAe;QACPjG,IAAI,GAAG,KAAKoF,SAAL,CAAepF,IAA5B;WACOA,IAAI,CAACkG,OAAL,IAAgB,IAAhB,GAAuBlG,IAAI,CAACkG,OAA5B,GAAuClG,IAAI,CAACkG,OAAL,GAAe,EAA7D;;;MAGEC,WAAJ,GAAkB;QACVnG,IAAI,GAAG,KAAKoF,SAAL,CAAepF,IAA5B;WACOA,IAAI,CAACoG,SAAL,IAAkB,IAAlB,GAAyBpG,IAAI,CAACoG,SAA9B,GAA2CpG,IAAI,CAACoG,SAAL,GAAiB,EAAnE;;;MAGEC,QAAJ,GAAe;QACPrG,IAAI,GAAG,KAAKoF,SAAL,CAAepF,IAA5B;WACOA,IAAI,CAACsG,OAAL,IAAgB,IAAhB,GAAuBtG,IAAI,CAACsG,OAA5B,GAAuCtG,IAAI,CAACsG,OAAL,GAAe,EAA7D;;;MAGEC,WAAJ,GAAkB;QACVvG,IAAI,GAAG,KAAKoF,SAAL,CAAepF,IAA5B;WACOA,IAAI,CAACwG,UAAL,KAAoBxG,IAAI,CAACwG,UAAL,GAAkB,EAAtC,CAAP;;;MAGEC,WAAJ,GAAkB;QACVzG,IAAI,GAAG,KAAKsF,UAAL,CAAgBtF,IAA7B;WACOA,IAAI,CAAC0G,MAAL,IAAe,IAAf,GAAsB1G,IAAI,CAAC0G,MAA3B,GAAqC1G,IAAI,CAAC0G,MAAL,GAAc,EAA1D;;;MAGEC,mBAAJ,GAA0B;QAClB3G,IAAI,GAAG,KAAKsF,UAAL,CAAgBtF,IAA7B;WACOA,IAAI,CAAC4G,aAAL,IAAsB,IAAtB,GACH5G,IAAI,CAAC4G,aADF,GAEF5G,IAAI,CAAC4G,aAAL,GAAqB,KAAK9G,QAAL,CAAc+G,6BAAd,EAF1B;;;EAKFC,IAAI,GAAG;WACE,KAAK7B,MAAL,GAAc,KAAKJ,OAAL,CAAavD,MAAlC;;;EAGFhB,KAAK,CAACC,KAAD,EAAQ;WACJ,KAAK2E,OAAL,CAAa5E,KAAb,CAAmBC,KAAnB,CAAP;;;EAGFxC,GAAG,GAAG;SACCuH,UAAL,CAAgBvH,GAAhB;SACKqH,SAAL,CAAerH,GAAf;WACO,KAAKmH,OAAL,CAAanH,GAAb,EAAP;;;;;AChKJ;;;AAIA;AAEA,MAAMgJ,WAAN,SAA0BpL,OAA1B,CAAkC;EAChCe,YAAY,CAACF,CAAD,EAAIC,CAAJ,EAAO;WACVD,CAAC,CAACwK,aAAF,CAAgBvK,CAAhB,CAAP;;;EAGFU,SAAS,GAAG;WACH,OAAP;;;EAGFD,WAAW,CAAC+J,CAAD,EAAI;WACN,IAAI/I,MAAJ,CAAW+I,CAAX,CAAP;;;;;AChBJ;;;;;;AAMA,SAASC,OAAT,CAAiBC,KAAjB,EAAwBC,UAAxB,EAAoC;MAC9BD,KAAK,GAAGC,UAAU,CAAC,CAAD,CAAtB,EAA2B,OAAO,KAAP;MACvBC,UAAU,GAAG,CAAjB;MACIC,QAAQ,GAAGF,UAAU,CAACxK,MAAX,GAAoB,CAAnC;;SACOyK,UAAU,IAAIC,QAArB,EAA+B;QACvBC,WAAW,GAAG5H,IAAI,CAAC6H,KAAL,CAAW,CAACH,UAAU,GAAGC,QAAd,IAA0B,CAArC,CAApB,CAD6B;;QAIvBG,UAAU,GAAGF,WAAW,GAAG,CAAjC,CAJ6B;;QAQ3BJ,KAAK,IAAIC,UAAU,CAACK,UAAD,CAAnB,IACAN,KAAK,IAAIC,UAAU,CAACK,UAAU,GAAG,CAAd,CAFrB,EAGE;aACO,IAAP;;;QAGEN,KAAK,GAAGC,UAAU,CAACK,UAAU,GAAG,CAAd,CAAtB,EAAwC;;MAEtCJ,UAAU,GAAGE,WAAW,GAAG,CAA3B;KAFF,MAGO;;MAELD,QAAQ,GAAGC,WAAW,GAAG,CAAzB;;;;SAGG,KAAP;;;AC7BF;;;;;AAIA,IAAMG,sBAAsB,GAAG,CAC7B,MAD6B,EAE7B,MAF6B,EAG7B,MAH6B,EAI7B,MAJ6B,EAK7B,MAL6B,EAM7B,MAN6B,EAO7B,MAP6B,EAQ7B,MAR6B,EAS7B,MAT6B,EAU7B,MAV6B,EAW7B,MAX6B,EAY7B,MAZ6B,EAa7B,MAb6B,EAc7B,MAd6B,EAe7B,MAf6B,EAgB7B,MAhB6B,EAiB7B,MAjB6B,EAkB7B,MAlB6B,EAmB7B,MAnB6B,EAoB7B,MApB6B,EAqB7B,MArB6B,EAsB7B,MAtB6B,EAuB7B,MAvB6B,EAwB7B,MAxB6B,EAyB7B,MAzB6B,EA0B7B,MA1B6B,EA2B7B,MA3B6B,EA4B7B,MA5B6B,EA6B7B,MA7B6B,EA8B7B,MA9B6B,EA+B7B,MA/B6B,EAgC7B,MAhC6B,EAiC7B,MAjC6B,EAkC7B,MAlC6B,EAmC7B,MAnC6B,EAoC7B,MApC6B,EAqC7B,MArC6B,EAsC7B,MAtC6B,EAuC7B,MAvC6B,EAwC7B,MAxC6B,EAyC7B,MAzC6B,EA0C7B,MA1C6B,EA2C7B,MA3C6B,EA4C7B,MA5C6B,EA6C7B,MA7C6B,EA8C7B,MA9C6B,EA+C7B,MA/C6B,EAgD7B,MAhD6B,EAiD7B,MAjD6B,EAkD7B,MAlD6B,EAmD7B,MAnD6B,EAoD7B,MApD6B,EAqD7B,MArD6B,EAsD7B,MAtD6B,EAuD7B,MAvD6B,EAwD7B,MAxD6B,EAyD7B,MAzD6B,EA0D7B,MA1D6B,EA2D7B,MA3D6B,EA4D7B,MA5D6B,EA6D7B,MA7D6B,EA8D7B,MA9D6B,EA+D7B,MA/D6B,EAgE7B,MAhE6B,EAiE7B,MAjE6B,EAkE7B,MAlE6B,EAmE7B,MAnE6B,EAoE7B,MApE6B,EAqE7B,MArE6B,EAsE7B,MAtE6B,EAuE7B,MAvE6B,EAwE7B,MAxE6B,EAyE7B,MAzE6B,EA0E7B,MA1E6B,EA2E7B,MA3E6B,EA4E7B,MA5E6B,EA6E7B,MA7E6B,EA8E7B,MA9E6B,EA+E7B,MA/E6B,EAgF7B,MAhF6B,EAiF7B,MAjF6B,EAkF7B,MAlF6B,EAmF7B,MAnF6B,EAoF7B,MApF6B,EAqF7B,MArF6B,EAsF7B,MAtF6B,EAuF7B,MAvF6B,EAwF7B,MAxF6B,EAyF7B,MAzF6B,EA0F7B,MA1F6B,EA2F7B,MA3F6B,EA4F7B,MA5F6B,EA6F7B,MA7F6B,EA8F7B,MA9F6B,EA+F7B,MA/F6B,EAgG7B,MAhG6B,EAiG7B,MAjG6B,EAkG7B,MAlG6B,EAmG7B,MAnG6B,EAoG7B,MApG6B,EAqG7B,MArG6B,EAsG7B,MAtG6B,EAuG7B,MAvG6B,EAwG7B,MAxG6B,EAyG7B,MAzG6B,EA0G7B,MA1G6B,EA2G7B,MA3G6B,EA4G7B,MA5G6B,EA6G7B,MA7G6B,EA8G7B,MA9G6B,EA+G7B,MA/G6B,EAgH7B,MAhH6B,EAiH7B,MAjH6B,EAkH7B,MAlH6B,EAmH7B,MAnH6B,EAoH7B,MApH6B,EAqH7B,MArH6B,EAsH7B,MAtH6B,EAuH7B,MAvH6B,EAwH7B,MAxH6B,EAyH7B,MAzH6B,EA0H7B,MA1H6B,EA2H7B,MA3H6B,EA4H7B,MA5H6B,EA6H7B,MA7H6B,EA8H7B,MA9H6B,EA+H7B,MA/H6B,EAgI7B,MAhI6B,EAiI7B,MAjI6B,EAkI7B,MAlI6B,EAmI7B,MAnI6B,EAoI7B,MApI6B,EAqI7B,MArI6B,EAsI7B,MAtI6B,EAuI7B,MAvI6B,EAwI7B,MAxI6B,EAyI7B,MAzI6B,EA0I7B,MA1I6B,EA2I7B,MA3I6B,EA4I7B,MA5I6B,EA6I7B,MA7I6B,EA8I7B,MA9I6B,EA+I7B,MA/I6B,EAgJ7B,MAhJ6B,EAiJ7B,MAjJ6B,EAkJ7B,MAlJ6B,EAmJ7B,MAnJ6B,EAoJ7B,MApJ6B,EAqJ7B,MArJ6B,EAsJ7B,MAtJ6B,EAuJ7B,MAvJ6B,EAwJ7B,MAxJ6B,EAyJ7B,MAzJ6B,EA0J7B,MA1J6B,EA2J7B,MA3J6B,EA4J7B,MA5J6B,EA6J7B,MA7J6B,EA8J7B,MA9J6B,EA+J7B,MA/J6B,EAgK7B,MAhK6B,EAiK7B,MAjK6B,EAkK7B,MAlK6B,EAmK7B,MAnK6B,EAoK7B,MApK6B,EAqK7B,MArK6B,EAsK7B,MAtK6B,EAuK7B,MAvK6B,EAwK7B,MAxK6B,EAyK7B,MAzK6B,EA0K7B,MA1K6B,EA2K7B,MA3K6B,EA4K7B,MA5K6B,EA6K7B,MA7K6B,EA8K7B,MA9K6B,EA+K7B,MA/K6B,EAgL7B,MAhL6B,EAiL7B,MAjL6B,EAkL7B,MAlL6B,EAmL7B,MAnL6B,EAoL7B,MApL6B,EAqL7B,MArL6B,EAsL7B,MAtL6B,EAuL7B,MAvL6B,EAwL7B,MAxL6B,EAyL7B,MAzL6B,EA0L7B,MA1L6B,EA2L7B,MA3L6B,EA4L7B,MA5L6B,EA6L7B,MA7L6B,EA8L7B,MA9L6B,EA+L7B,MA/L6B,EAgM7B,MAhM6B,EAiM7B,MAjM6B,EAkM7B,MAlM6B,EAmM7B,MAnM6B,EAoM7B,MApM6B,EAqM7B,MArM6B,EAsM7B,MAtM6B,EAuM7B,MAvM6B,EAwM7B,MAxM6B,EAyM7B,MAzM6B,EA0M7B,MA1M6B,EA2M7B,MA3M6B,EA4M7B,MA5M6B,EA6M7B,MA7M6B,EA8M7B,MA9M6B,EA+M7B,MA/M6B,EAgN7B,MAhN6B,EAiN7B,MAjN6B,EAkN7B,MAlN6B,EAmN7B,MAnN6B,EAoN7B,MApN6B,EAqN7B,MArN6B,EAsN7B,MAtN6B,EAuN7B,MAvN6B,EAwN7B,MAxN6B,EAyN7B,MAzN6B,EA0N7B,MA1N6B,EA2N7B,MA3N6B,EA4N7B,MA5N6B,EA6N7B,MA7N6B,EA8N7B,MA9N6B,EA+N7B,MA/N6B,EAgO7B,MAhO6B,EAiO7B,MAjO6B,EAkO7B,MAlO6B,EAmO7B,MAnO6B,EAoO7B,MApO6B,EAqO7B,MArO6B,EAsO7B,MAtO6B,EAuO7B,MAvO6B,EAwO7B,MAxO6B,EAyO7B,MAzO6B,EA0O7B,MA1O6B,EA2O7B,MA3O6B,EA4O7B,MA5O6B,EA6O7B,MA7O6B,EA8O7B,MA9O6B,EA+O7B,MA/O6B,EAgP7B,MAhP6B,EAiP7B,MAjP6B,EAkP7B,MAlP6B,EAmP7B,MAnP6B,EAoP7B,MApP6B,EAqP7B,MArP6B,EAsP7B,MAtP6B,EAuP7B,MAvP6B,EAwP7B,MAxP6B,EAyP7B,MAzP6B,EA0P7B,MA1P6B,EA2P7B,MA3P6B,EA4P7B,MA5P6B,EA6P7B,MA7P6B,EA8P7B,MA9P6B,EA+P7B,MA/P6B,EAgQ7B,MAhQ6B,EAiQ7B,MAjQ6B,EAkQ7B,MAlQ6B,EAmQ7B,MAnQ6B,EAoQ7B,MApQ6B,EAqQ7B,MArQ6B,EAsQ7B,MAtQ6B,EAuQ7B,MAvQ6B,EAwQ7B,MAxQ6B,EAyQ7B,MAzQ6B,EA0Q7B,MA1Q6B,EA2Q7B,MA3Q6B,EA4Q7B,MA5Q6B,EA6Q7B,MA7Q6B,EA8Q7B,MA9Q6B,EA+Q7B,MA/Q6B,EAgR7B,MAhR6B,EAiR7B,MAjR6B,EAkR7B,MAlR6B,EAmR7B,MAnR6B,EAoR7B,MApR6B,EAqR7B,MArR6B,EAsR7B,MAtR6B,EAuR7B,MAvR6B,EAwR7B,MAxR6B,EAyR7B,MAzR6B,EA0R7B,MA1R6B,EA2R7B,MA3R6B,EA4R7B,MA5R6B,EA6R7B,MA7R6B,EA8R7B,MA9R6B,EA+R7B,MA/R6B,EAgS7B,MAhS6B,EAiS7B,MAjS6B,EAkS7B,MAlS6B,EAmS7B,MAnS6B,EAoS7B,MApS6B,EAqS7B,MArS6B,EAsS7B,MAtS6B,EAuS7B,MAvS6B,EAwS7B,MAxS6B,EAyS7B,MAzS6B,EA0S7B,MA1S6B,EA2S7B,MA3S6B,EA4S7B,MA5S6B,EA6S7B,MA7S6B,EA8S7B,MA9S6B,EA+S7B,MA/S6B,EAgT7B,MAhT6B,EAiT7B,MAjT6B,EAkT7B,MAlT6B,EAmT7B,MAnT6B,EAoT7B,MApT6B,EAqT7B,MArT6B,EAsT7B,MAtT6B,EAuT7B,MAvT6B,EAwT7B,MAxT6B,EAyT7B,MAzT6B,EA0T7B,MA1T6B,EA2T7B,MA3T6B,EA4T7B,MA5T6B,EA6T7B,MA7T6B,EA8T7B,MA9T6B,EA+T7B,MA/T6B,EAgU7B,MAhU6B,EAiU7B,MAjU6B,EAkU7B,MAlU6B,EAmU7B,MAnU6B,EAoU7B,MApU6B,EAqU7B,MArU6B,EAsU7B,MAtU6B,EAuU7B,MAvU6B,EAwU7B,MAxU6B,EAyU7B,MAzU6B,EA0U7B,MA1U6B,EA2U7B,MA3U6B,EA4U7B,MA5U6B,EA6U7B,MA7U6B,EA8U7B,MA9U6B,EA+U7B,MA/U6B,EAgV7B,MAhV6B,EAiV7B,MAjV6B,EAkV7B,MAlV6B,EAmV7B,MAnV6B,EAoV7B,MApV6B,EAqV7B,MArV6B,EAsV7B,MAtV6B,EAuV7B,MAvV6B,EAwV7B,MAxV6B,EAyV7B,MAzV6B,EA0V7B,MA1V6B,EA2V7B,MA3V6B,EA4V7B,MA5V6B,EA6V7B,MA7V6B,EA8V7B,MA9V6B,EA+V7B,MA/V6B,EAgW7B,MAhW6B,EAiW7B,MAjW6B,EAkW7B,MAlW6B,EAmW7B,MAnW6B,EAoW7B,MApW6B,EAqW7B,MArW6B,EAsW7B,MAtW6B,EAuW7B,MAvW6B,EAwW7B,MAxW6B,EAyW7B,MAzW6B,EA0W7B,MA1W6B,EA2W7B,MA3W6B,EA4W7B,MA5W6B,EA6W7B,MA7W6B,EA8W7B,MA9W6B,EA+W7B,MA/W6B,EAgX7B,MAhX6B,EAiX7B,MAjX6B,EAkX7B,MAlX6B,EAmX7B,MAnX6B,EAoX7B,MApX6B,EAqX7B,MArX6B,EAsX7B,MAtX6B,EAuX7B,MAvX6B,EAwX7B,MAxX6B,EAyX7B,MAzX6B,EA0X7B,MA1X6B,EA2X7B,MA3X6B,EA4X7B,MA5X6B,EA6X7B,MA7X6B,EA8X7B,MA9X6B,EA+X7B,MA/X6B,EAgY7B,MAhY6B,EAiY7B,MAjY6B,EAkY7B,MAlY6B,EAmY7B,MAnY6B,EAoY7B,MApY6B,EAqY7B,MArY6B,EAsY7B,MAtY6B,EAuY7B,MAvY6B,EAwY7B,MAxY6B,EAyY7B,MAzY6B,EA0Y7B,MA1Y6B,EA2Y7B,MA3Y6B,EA4Y7B,MA5Y6B,EA6Y7B,MA7Y6B,EA8Y7B,MA9Y6B,EA+Y7B,MA/Y6B,EAgZ7B,MAhZ6B,EAiZ7B,MAjZ6B,EAkZ7B,MAlZ6B,EAmZ7B,MAnZ6B,EAoZ7B,MApZ6B,EAqZ7B,MArZ6B,EAsZ7B,MAtZ6B,EAuZ7B,MAvZ6B,EAwZ7B,MAxZ6B,EAyZ7B,MAzZ6B,EA0Z7B,MA1Z6B,EA2Z7B,MA3Z6B,EA4Z7B,MA5Z6B,EA6Z7B,MA7Z6B,EA8Z7B,MA9Z6B,EA+Z7B,MA/Z6B,EAga7B,MAha6B,EAia7B,MAja6B,EAka7B,MAla6B,EAma7B,MAna6B,EAoa7B,MApa6B,EAqa7B,MAra6B,EAsa7B,MAta6B,EAua7B,MAva6B,EAwa7B,MAxa6B,EAya7B,MAza6B,EA0a7B,MA1a6B,EA2a7B,MA3a6B,EA4a7B,MA5a6B,EA6a7B,MA7a6B,EA8a7B,MA9a6B,EA+a7B,MA/a6B,EAgb7B,MAhb6B,EAib7B,MAjb6B,EAkb7B,MAlb6B,EAmb7B,MAnb6B,EAob7B,MApb6B,EAqb7B,MArb6B,EAsb7B,MAtb6B,EAub7B,MAvb6B,EAwb7B,MAxb6B,EAyb7B,MAzb6B,EA0b7B,MA1b6B,EA2b7B,MA3b6B,EA4b7B,MA5b6B,EA6b7B,MA7b6B,EA8b7B,MA9b6B,EA+b7B,MA/b6B,EAgc7B,MAhc6B,EAic7B,MAjc6B,EAkc7B,MAlc6B,EAmc7B,MAnc6B,EAoc7B,MApc6B,EAqc7B,MArc6B,EAsc7B,MAtc6B,EAuc7B,MAvc6B,EAwc7B,MAxc6B,EAyc7B,MAzc6B,EA0c7B,MA1c6B,EA2c7B,MA3c6B,EA4c7B,MA5c6B,EA6c7B,MA7c6B,EA8c7B,MA9c6B,EA+c7B,MA/c6B,EAgd7B,MAhd6B,EAid7B,MAjd6B,EAkd7B,MAld6B,EAmd7B,MAnd6B,EAod7B,MApd6B,EAqd7B,MArd6B,EAsd7B,MAtd6B,EAud7B,MAvd6B,EAwd7B,MAxd6B,EAyd7B,MAzd6B,EA0d7B,MA1d6B,EA2d7B,MA3d6B,EA4d7B,MA5d6B,EA6d7B,MA7d6B,EA8d7B,MA9d6B,EA+d7B,MA/d6B,EAge7B,MAhe6B,EAie7B,MAje6B,EAke7B,MAle6B,EAme7B,MAne6B,EAoe7B,MApe6B,EAqe7B,MAre6B,EAse7B,MAte6B,EAue7B,MAve6B,EAwe7B,MAxe6B,EAye7B,MAze6B,EA0e7B,MA1e6B,EA2e7B,MA3e6B,EA4e7B,MA5e6B,EA6e7B,MA7e6B,EA8e7B,MA9e6B,EA+e7B,MA/e6B,EAgf7B,MAhf6B,EAif7B,MAjf6B,EAkf7B,MAlf6B,EAmf7B,MAnf6B,EAof7B,MApf6B,EAqf7B,MArf6B,EAsf7B,MAtf6B,EAuf7B,MAvf6B,EAwf7B,MAxf6B,EAyf7B,MAzf6B,EA0f7B,MA1f6B,EA2f7B,MA3f6B,EA4f7B,MA5f6B,EA6f7B,MA7f6B,EA8f7B,MA9f6B,EA+f7B,MA/f6B,EAggB7B,MAhgB6B,EAigB7B,MAjgB6B,EAkgB7B,MAlgB6B,EAmgB7B,MAngB6B,EAogB7B,MApgB6B,EAqgB7B,MArgB6B,EAsgB7B,MAtgB6B,EAugB7B,MAvgB6B,EAwgB7B,MAxgB6B,EAygB7B,MAzgB6B,EA0gB7B,MA1gB6B,EA2gB7B,MA3gB6B,EA4gB7B,MA5gB6B,EA6gB7B,MA7gB6B,EA8gB7B,MA9gB6B,EA+gB7B,MA/gB6B,EAghB7B,MAhhB6B,EAihB7B,MAjhB6B,EAkhB7B,MAlhB6B,EAmhB7B,MAnhB6B,EAohB7B,MAphB6B,EAqhB7B,MArhB6B,EAshB7B,MAthB6B,EAuhB7B,MAvhB6B,EAwhB7B,MAxhB6B,EAyhB7B,MAzhB6B,EA0hB7B,MA1hB6B,EA2hB7B,MA3hB6B,EA4hB7B,MA5hB6B,EA6hB7B,MA7hB6B,EA8hB7B,MA9hB6B,EA+hB7B,MA/hB6B,EAgiB7B,MAhiB6B,EAiiB7B,MAjiB6B,EAkiB7B,MAliB6B,EAmiB7B,MAniB6B,EAoiB7B,MApiB6B,EAqiB7B,MAriB6B,EAsiB7B,MAtiB6B,EAuiB7B,MAviB6B,EAwiB7B,MAxiB6B,EAyiB7B,MAziB6B,EA0iB7B,MA1iB6B,EA2iB7B,MA3iB6B,EA4iB7B,MA5iB6B,EA6iB7B,MA7iB6B,EA8iB7B,MA9iB6B,EA+iB7B,MA/iB6B,EAgjB7B,MAhjB6B,EAijB7B,MAjjB6B,EAkjB7B,MAljB6B,EAmjB7B,MAnjB6B,EAojB7B,MApjB6B,EAqjB7B,MArjB6B,EAsjB7B,MAtjB6B,EAujB7B,MAvjB6B,EAwjB7B,MAxjB6B,EAyjB7B,MAzjB6B,EA0jB7B,MA1jB6B,EA2jB7B,MA3jB6B,EA4jB7B,MA5jB6B,EA6jB7B,MA7jB6B,EA8jB7B,MA9jB6B,EA+jB7B,MA/jB6B,EAgkB7B,MAhkB6B,EAikB7B,MAjkB6B,EAkkB7B,MAlkB6B,EAmkB7B,MAnkB6B,EAokB7B,MApkB6B,EAqkB7B,MArkB6B,EAskB7B,MAtkB6B,EAukB7B,MAvkB6B,EAwkB7B,MAxkB6B,EAykB7B,MAzkB6B,EA0kB7B,MA1kB6B,EA2kB7B,MA3kB6B,EA4kB7B,MA5kB6B,EA6kB7B,MA7kB6B,EA8kB7B,MA9kB6B,EA+kB7B,MA/kB6B,EAglB7B,MAhlB6B,EAilB7B,MAjlB6B,EAklB7B,MAllB6B,EAmlB7B,MAnlB6B,EAolB7B,MAplB6B,EAqlB7B,MArlB6B,EAslB7B,MAtlB6B,EAulB7B,MAvlB6B,EAwlB7B,MAxlB6B,EAylB7B,MAzlB6B,EA0lB7B,MA1lB6B,EA2lB7B,MA3lB6B,EA4lB7B,MA5lB6B,EA6lB7B,MA7lB6B,EA8lB7B,MA9lB6B,EA+lB7B,MA/lB6B,EAgmB7B,MAhmB6B,EAimB7B,MAjmB6B,EAkmB7B,MAlmB6B,EAmmB7B,MAnmB6B,EAomB7B,MApmB6B,EAqmB7B,MArmB6B,EAsmB7B,MAtmB6B,EAumB7B,MAvmB6B,EAwmB7B,MAxmB6B,EAymB7B,MAzmB6B,EA0mB7B,MA1mB6B,EA2mB7B,MA3mB6B,EA4mB7B,MA5mB6B,EA6mB7B,MA7mB6B,EA8mB7B,MA9mB6B,EA+mB7B,MA/mB6B,EAgnB7B,MAhnB6B,EAinB7B,MAjnB6B,EAknB7B,MAlnB6B,EAmnB7B,MAnnB6B,EAonB7B,MApnB6B,EAqnB7B,MArnB6B,EAsnB7B,MAtnB6B,EAunB7B,MAvnB6B,EAwnB7B,MAxnB6B,EAynB7B,MAznB6B,EA0nB7B,MA1nB6B,EA2nB7B,MA3nB6B,EA4nB7B,MA5nB6B,EA6nB7B,MA7nB6B,EA8nB7B,MA9nB6B,EA+nB7B,MA/nB6B,EAgoB7B,MAhoB6B,EAioB7B,MAjoB6B,EAkoB7B,MAloB6B,EAmoB7B,MAnoB6B,EAooB7B,MApoB6B,EAqoB7B,MAroB6B,EAsoB7B,MAtoB6B,EAuoB7B,MAvoB6B,EAwoB7B,MAxoB6B,EAyoB7B,MAzoB6B,EA0oB7B,MA1oB6B,EA2oB7B,MA3oB6B,EA4oB7B,MA5oB6B,EA6oB7B,MA7oB6B,EA8oB7B,MA9oB6B,EA+oB7B,MA/oB6B,EAgpB7B,MAhpB6B,EAipB7B,MAjpB6B,EAkpB7B,MAlpB6B,EAmpB7B,MAnpB6B,EAopB7B,MAppB6B,EAqpB7B,MArpB6B,EAspB7B,MAtpB6B,EAupB7B,MAvpB6B,EAwpB7B,MAxpB6B,EAypB7B,MAzpB6B,EA0pB7B,MA1pB6B,EA2pB7B,MA3pB6B,EA4pB7B,MA5pB6B,EA6pB7B,MA7pB6B,EA8pB7B,MA9pB6B,EA+pB7B,MA/pB6B,EAgqB7B,MAhqB6B,EAiqB7B,MAjqB6B,EAkqB7B,MAlqB6B,EAmqB7B,MAnqB6B,EAoqB7B,MApqB6B,EAqqB7B,MArqB6B,EAsqB7B,MAtqB6B,EAuqB7B,MAvqB6B,EAwqB7B,MAxqB6B,EAyqB7B,MAzqB6B,EA0qB7B,MA1qB6B,EA2qB7B,MA3qB6B,EA4qB7B,MA5qB6B,EA6qB7B,MA7qB6B,EA8qB7B,MA9qB6B,EA+qB7B,MA/qB6B,EAgrB7B,MAhrB6B,EAirB7B,MAjrB6B,EAkrB7B,MAlrB6B,EAmrB7B,MAnrB6B,EAorB7B,MAprB6B,EAqrB7B,MArrB6B,EAsrB7B,MAtrB6B,EAurB7B,MAvrB6B,EAwrB7B,MAxrB6B,EAyrB7B,MAzrB6B,EA0rB7B,MA1rB6B,EA2rB7B,OA3rB6B,EA4rB7B,OA5rB6B,EA6rB7B,OA7rB6B,EA8rB7B,OA9rB6B,EA+rB7B,OA/rB6B,EAgsB7B,OAhsB6B,EAisB7B,OAjsB6B,EAksB7B,OAlsB6B,EAmsB7B,OAnsB6B,EAosB7B,OApsB6B,EAqsB7B,OArsB6B,EAssB7B,OAtsB6B,EAusB7B,OAvsB6B,EAwsB7B,OAxsB6B,EAysB7B,OAzsB6B,EA0sB7B,OA1sB6B,EA2sB7B,OA3sB6B,EA4sB7B,OA5sB6B,EA6sB7B,OA7sB6B,EA8sB7B,OA9sB6B,EA+sB7B,OA/sB6B,EAgtB7B,OAhtB6B,EAitB7B,OAjtB6B,EAktB7B,OAltB6B,EAmtB7B,OAntB6B,EAotB7B,OAptB6B,EAqtB7B,OArtB6B,EAstB7B,OAttB6B,EAutB7B,OAvtB6B,EAwtB7B,OAxtB6B,EAytB7B,OAztB6B,EA0tB7B,OA1tB6B,EA2tB7B,OA3tB6B,EA4tB7B,OA5tB6B,EA6tB7B,OA7tB6B,EA8tB7B,OA9tB6B,EA+tB7B,OA/tB6B,EAguB7B,OAhuB6B,EAiuB7B,OAjuB6B,EAkuB7B,OAluB6B,EAmuB7B,OAnuB6B,EAouB7B,OApuB6B,EAquB7B,OAruB6B,EAsuB7B,OAtuB6B,EAuuB7B,OAvuB6B,EAwuB7B,OAxuB6B,EAyuB7B,OAzuB6B,EA0uB7B,OA1uB6B,EA2uB7B,OA3uB6B,EA4uB7B,OA5uB6B,EA6uB7B,OA7uB6B,EA8uB7B,OA9uB6B,EA+uB7B,OA/uB6B,EAgvB7B,OAhvB6B,EAivB7B,OAjvB6B,EAkvB7B,OAlvB6B,EAmvB7B,OAnvB6B,EAovB7B,OApvB6B,EAqvB7B,OArvB6B,EAsvB7B,OAtvB6B,EAuvB7B,OAvvB6B,EAwvB7B,OAxvB6B,EAyvB7B,OAzvB6B,EA0vB7B,OA1vB6B,EA2vB7B,OA3vB6B,EA4vB7B,OA5vB6B,EA6vB7B,OA7vB6B,EA8vB7B,OA9vB6B,EA+vB7B,OA/vB6B,EAgwB7B,OAhwB6B,EAiwB7B,OAjwB6B,EAkwB7B,OAlwB6B,EAmwB7B,OAnwB6B,EAowB7B,OApwB6B,EAqwB7B,OArwB6B,EAswB7B,OAtwB6B,EAuwB7B,OAvwB6B,EAwwB7B,OAxwB6B,EAywB7B,OAzwB6B,EA0wB7B,OA1wB6B,EA2wB7B,OA3wB6B,EA4wB7B,OA5wB6B,EA6wB7B,OA7wB6B,EA8wB7B,OA9wB6B,EA+wB7B,OA/wB6B,EAgxB7B,OAhxB6B,EAixB7B,OAjxB6B,EAkxB7B,OAlxB6B,EAmxB7B,OAnxB6B,EAoxB7B,OApxB6B,EAqxB7B,OArxB6B,EAsxB7B,OAtxB6B,EAuxB7B,OAvxB6B,EAwxB7B,OAxxB6B,CAA/B;;AA4xBA,IAAMC,qBAAqB,GAAGC,SAAS,IACrCV,OAAO,CAACU,SAAD,EAAYF,sBAAZ,CADT;;;;;;;;AAQA,IAAMG,0BAA0B,GAAG,CACjC,MADiC,EAEjC,MAFiC,EAGjC,MAHiC,EAIjC,MAJiC,EAKjC,MALiC,EAMjC,MANiC,EAOjC,MAPiC,EAQjC,MARiC,EASjC,MATiC,EAUjC,MAViC,EAWjC,MAXiC,EAYjC,MAZiC,EAajC,MAbiC,EAcjC,MAdiC,EAejC,MAfiC,EAgBjC,MAhBiC,EAiBjC,MAjBiC,EAkBjC,MAlBiC,EAmBjC,MAnBiC,EAoBjC,MApBiC,EAqBjC,MArBiC,EAsBjC,MAtBiC,EAuBjC,MAvBiC,EAwBjC,MAxBiC,EAyBjC,MAzBiC,EA0BjC,MA1BiC,EA2BjC,MA3BiC,EA4BjC,MA5BiC,EA6BjC,MA7BiC,EA8BjC,MA9BiC,EA+BjC,MA/BiC,EAgCjC,MAhCiC,EAiCjC,MAjCiC,EAkCjC,MAlCiC,EAmCjC,MAnCiC,EAoCjC,MApCiC,EAqCjC,MArCiC,EAsCjC,MAtCiC,EAuCjC,MAvCiC,EAwCjC,MAxCiC,EAyCjC,MAzCiC,EA0CjC,MA1CiC,EA2CjC,MA3CiC,EA4CjC,MA5CiC,EA6CjC,MA7CiC,EA8CjC,MA9CiC,EA+CjC,MA/CiC,EAgDjC,MAhDiC,EAiDjC,MAjDiC,EAkDjC,MAlDiC,EAmDjC,MAnDiC,EAoDjC,MApDiC,EAqDjC,MArDiC,EAsDjC,MAtDiC,CAAnC;;AA0DA,IAAMC,yBAAyB,GAAGF,SAAS,IACzCV,OAAO,CAACU,SAAD,EAAYC,0BAAZ,CADT;;;;;;;;AAQA,IAAME,0BAA0B,GAAG,CACjC,MADiC,EAEjC;;EACA,MAHiC,EAIjC;;EACA,MALiC,EAMjC;;EACA,MAPiC,EAQjC;;EACA,MATiC,EAUjC;;EACA,MAXiC,EAYjC;;EACA,MAbiC,EAcjC;;EACA,MAfiC,EAgBjC;;EACA,MAjBiC,EAkBjC;;EACA,MAnBiC,EAoBjC;;EACA,MArBiC,EAsBjC;;EACA,MAvBiC,EAwBjC;;EACA,MAzBiC,EA0BjC;;EACA,MA3BiC,EA4BjC;;EACA,MA7BiC,EA8BjC;;EACA,MA/BiC,EAgCjC;;EACA,MAjCiC,EAkCjC;;CAlCF;;AAsCA,IAAMC,wBAAwB,GAAGJ,SAAS,IACxCV,OAAO,CAACU,SAAD,EAAYG,0BAAZ,CADT;;;AAIA,IAAME,6BAA6B,GAAG;;;;;AAKpC,MALoC,EAMpC;;EACA,MAPoC,EAQpC;;EACA,MAToC,EAUpC;;EACA,MAXoC,EAYpC;;EACA,MAboC,EAcpC;;EACA,MAfoC,EAgBpC;;EACA,MAjBoC,EAkBpC;;EACA,MAnBoC,EAoBpC;;EACA,MArBoC,EAsBpC;;EACA,MAvBoC,EAwBpC;;EACA,MAzBoC,EA0BpC;;EACA,MA3BoC,EA4BpC;;EACA,MA7BoC,EA8BpC;;EACA,MA/BoC,EAgCpC;;EACA,MAjCoC,EAkCpC;;EACA,OAnCoC,EAoCpC;;CApCF;AAuCA,IAAMC,wBAAwB,GAAG;;;;;AAK/B,MAL+B,EAM/B;;EACA,MAP+B,EAQ/B;;EACA,OAT+B,EAU/B;;EACA,OAX+B,EAY/B;;EACA,OAb+B,EAc/B;;EACA,OAf+B,EAgB/B;;EACA,OAjB+B,EAkB/B;;EACA,OAnB+B,EAoB/B;;EACA,OArB+B,EAsB/B;;EACA,OAvB+B,EAwB/B;;EACA,OAzB+B,EA0B/B;;EACA,OA3B+B,EA4B/B;;EACA,OA7B+B,EA8B/B;;EACA,OA/B+B,EAgC/B;;EACA,OAjC+B,EAkC/B;;EACA,OAnC+B,EAoC/B;;EACA,QArC+B,EAsC/B;;CAtCF;;;;;AA4CA,IAAMC,qBAAqB,GAAG;;;;;AAK5B,CAL4B,EAM5B;;EACA,MAP4B,EAQ5B;;;;;;;AAMA,MAd4B,EAe5B;;EACA,MAhB4B,EAiB5B;;EACA,MAlB4B,EAmB5B;;EACA,MApB4B,EAqB5B;;EACA,MAtB4B,EAuB5B;;EACA,MAxB4B,EAyB5B;;EACA,MA1B4B,EA2B5B;;EACA,MA5B4B,EA6B5B;;EACA,MA9B4B,EA+B5B;;EACA,MAhC4B,EAiC5B;;EACA,MAlC4B,EAmC5B;;EACA,MApC4B,EAqC5B;;EACA,MAtC4B,EAuC5B;;EACA,MAxC4B,EAyC5B;;EACA,MA1C4B,EA2C5B;;;;;;;AAMA,MAjD4B,EAkD5B;;;;;;;AAMA,MAxD4B,EAyD5B,MAzD4B;;;;;AA+D5B,MA/D4B,EAgE5B;;;;;;;AAMA,MAtE4B,EAuE5B;;EACA,MAxE4B,EAyE5B;;EACA,MA1E4B,EA2E5B;;EACA,MA5E4B,EA6E5B;;EACA,MA9E4B,EA+E5B;;;;;;;AAMA,OArF4B,EAsF5B;;EACA,OAvF4B,EAwF5B;;;;;;;AAOA,OA/F4B,EAgG5B;;EACA,QAjG4B,EAkG5B;;CAlGF;;AAsGA,IAAMC,qBAAqB,GAAGR,SAAS,IACrCV,OAAO,CAACU,SAAD,EAAYG,0BAAZ,CAAP,IACAb,OAAO,CAACU,SAAD,EAAYO,qBAAZ,CADP,IAEAjB,OAAO,CAACU,SAAD,EAAYK,6BAAZ,CAFP,IAGAf,OAAO,CAACU,SAAD,EAAYM,wBAAZ,CAJT;;;;;;;;AAWA,IAAMG,kBAAkB,GAAG,CACzB,MADyB,EAEzB,MAFyB,EAGzB,MAHyB,EAIzB,MAJyB,EAKzB,MALyB,EAMzB,MANyB,EAOzB,MAPyB,EAQzB,MARyB,EASzB,MATyB,EAUzB,MAVyB,EAWzB,MAXyB,EAYzB,MAZyB,EAazB,MAbyB,EAczB,MAdyB,EAezB,MAfyB,EAgBzB,MAhByB,EAiBzB,MAjByB,EAkBzB,MAlByB,EAmBzB,MAnByB,EAoBzB,MApByB,EAqBzB,MArByB,EAsBzB,MAtByB,EAuBzB,MAvByB,EAwBzB,MAxByB,EAyBzB,MAzByB,EA0BzB,MA1ByB,EA2BzB,MA3ByB,EA4BzB,MA5ByB,EA6BzB,MA7ByB,EA8BzB,MA9ByB,EA+BzB,MA/ByB,EAgCzB,MAhCyB,EAiCzB,MAjCyB,EAkCzB,MAlCyB,EAmCzB,MAnCyB,EAoCzB,MApCyB,EAqCzB,MArCyB,EAsCzB,MAtCyB,EAuCzB,MAvCyB,EAwCzB,MAxCyB,EAyCzB,MAzCyB,EA0CzB,MA1CyB,EA2CzB,MA3CyB,EA4CzB,MA5CyB,EA6CzB,MA7CyB,EA8CzB,MA9CyB,EA+CzB,MA/CyB,EAgDzB,MAhDyB,EAiDzB,MAjDyB,EAkDzB,MAlDyB,EAmDzB,MAnDyB,EAoDzB,MApDyB,EAqDzB,MArDyB,EAsDzB,MAtDyB,EAuDzB,MAvDyB,EAwDzB,MAxDyB,EAyDzB,MAzDyB,EA0DzB,MA1DyB,EA2DzB,MA3DyB,EA4DzB,MA5DyB,EA6DzB,MA7DyB,EA8DzB,MA9DyB,EA+DzB,MA/DyB,EAgEzB,MAhEyB,EAiEzB,MAjEyB,EAkEzB,MAlEyB,EAmEzB,MAnEyB,EAoEzB,MApEyB,CAA3B;;AAwEA,IAAMC,kBAAkB,GAAGV,SAAS,IAAIV,OAAO,CAACU,SAAD,EAAYS,kBAAZ,CAA/C;;;;;;;;AAOA,IAAME,eAAe,GAAG,CACtB,MADsB,EAEtB,MAFsB,EAGtB,MAHsB,EAItB,MAJsB,EAKtB,MALsB,EAMtB,MANsB,EAOtB,MAPsB,EAQtB,MARsB,EAStB,MATsB,EAUtB,MAVsB,EAWtB,MAXsB,EAYtB,MAZsB,EAatB,MAbsB,EActB,MAdsB,EAetB,MAfsB,EAgBtB,MAhBsB,EAiBtB,MAjBsB,EAkBtB,MAlBsB,EAmBtB,MAnBsB,EAoBtB,MApBsB,EAqBtB,MArBsB,EAsBtB,MAtBsB,EAuBtB,MAvBsB,EAwBtB,MAxBsB,EAyBtB,MAzBsB,EA0BtB,MA1BsB,EA2BtB,MA3BsB,EA4BtB,MA5BsB,EA6BtB,MA7BsB,EA8BtB,MA9BsB,EA+BtB,MA/BsB,EAgCtB,MAhCsB,EAiCtB,MAjCsB,EAkCtB,MAlCsB,EAmCtB,MAnCsB,EAoCtB,MApCsB,EAqCtB,MArCsB,EAsCtB,MAtCsB,EAuCtB,MAvCsB,EAwCtB,MAxCsB,EAyCtB,MAzCsB,EA0CtB,MA1CsB,EA2CtB,MA3CsB,EA4CtB,MA5CsB,EA6CtB,MA7CsB,EA8CtB,MA9CsB,EA+CtB,MA/CsB,EAgDtB,MAhDsB,EAiDtB,MAjDsB,EAkDtB,MAlDsB,EAmDtB,MAnDsB,EAoDtB,MApDsB,EAqDtB,MArDsB,EAsDtB,MAtDsB,EAuDtB,MAvDsB,EAwDtB,MAxDsB,EAyDtB,MAzDsB,EA0DtB,MA1DsB,EA2DtB,MA3DsB,EA4DtB,MA5DsB,EA6DtB,MA7DsB,EA8DtB,MA9DsB,EA+DtB,MA/DsB,EAgEtB,MAhEsB,EAiEtB,MAjEsB,EAkEtB,MAlEsB,EAmEtB,MAnEsB,EAoEtB,MApEsB,EAqEtB,MArEsB,EAsEtB,MAtEsB,EAuEtB,MAvEsB,EAwEtB,MAxEsB,EAyEtB,MAzEsB,EA0EtB,MA1EsB,EA2EtB,MA3EsB,EA4EtB,MA5EsB,EA6EtB,MA7EsB,EA8EtB,MA9EsB,EA+EtB,MA/EsB,EAgFtB,MAhFsB,EAiFtB,MAjFsB,EAkFtB,MAlFsB,EAmFtB,MAnFsB,EAoFtB,MApFsB,EAqFtB,MArFsB,EAsFtB,MAtFsB,EAuFtB,MAvFsB,EAwFtB,MAxFsB,EAyFtB,MAzFsB,EA0FtB,MA1FsB,EA2FtB,MA3FsB,EA4FtB,MA5FsB,EA6FtB,MA7FsB,EA8FtB,MA9FsB,EA+FtB,MA/FsB,EAgGtB,MAhGsB,EAiGtB,MAjGsB,EAkGtB,MAlGsB,EAmGtB,MAnGsB,EAoGtB,MApGsB,EAqGtB,MArGsB,EAsGtB,MAtGsB,EAuGtB,MAvGsB,EAwGtB,MAxGsB,EAyGtB,MAzGsB,EA0GtB,MA1GsB,EA2GtB,MA3GsB,EA4GtB,MA5GsB,EA6GtB,MA7GsB,EA8GtB,MA9GsB,EA+GtB,MA/GsB,EAgHtB,MAhHsB,EAiHtB,MAjHsB,EAkHtB,MAlHsB,EAmHtB,MAnHsB,EAoHtB,MApHsB,EAqHtB,MArHsB,EAsHtB,MAtHsB,EAuHtB,MAvHsB,EAwHtB,MAxHsB,EAyHtB,MAzHsB,EA0HtB,MA1HsB,EA2HtB,MA3HsB,EA4HtB,MA5HsB,EA6HtB,MA7HsB,EA8HtB,MA9HsB,EA+HtB,MA/HsB,EAgItB,MAhIsB,EAiItB,MAjIsB,EAkItB,MAlIsB,EAmItB,MAnIsB,EAoItB,MApIsB,EAqItB,MArIsB,EAsItB,MAtIsB,EAuItB,MAvIsB,EAwItB,MAxIsB,EAyItB,MAzIsB,EA0ItB,MA1IsB,EA2ItB,MA3IsB,EA4ItB,MA5IsB,EA6ItB,MA7IsB,EA8ItB,MA9IsB,EA+ItB,MA/IsB,EAgJtB,MAhJsB,EAiJtB,MAjJsB,EAkJtB,MAlJsB,EAmJtB,MAnJsB,EAoJtB,MApJsB,EAqJtB,MArJsB,EAsJtB,MAtJsB,EAuJtB,MAvJsB,EAwJtB,MAxJsB,EAyJtB,MAzJsB,EA0JtB,MA1JsB,EA2JtB,MA3JsB,EA4JtB,MA5JsB,EA6JtB,MA7JsB,EA8JtB,MA9JsB,EA+JtB,MA/JsB,EAgKtB,MAhKsB,EAiKtB,MAjKsB,EAkKtB,MAlKsB,EAmKtB,MAnKsB,EAoKtB,MApKsB,EAqKtB,MArKsB,EAsKtB,MAtKsB,EAuKtB,MAvKsB,EAwKtB,MAxKsB,EAyKtB,MAzKsB,EA0KtB,MA1KsB,EA2KtB,MA3KsB,EA4KtB,MA5KsB,EA6KtB,MA7KsB,EA8KtB,MA9KsB,EA+KtB,MA/KsB,EAgLtB,MAhLsB,EAiLtB,MAjLsB,EAkLtB,MAlLsB,EAmLtB,MAnLsB,EAoLtB,MApLsB,EAqLtB,MArLsB,EAsLtB,MAtLsB,EAuLtB,MAvLsB,EAwLtB,MAxLsB,EAyLtB,MAzLsB,EA0LtB,MA1LsB,EA2LtB,MA3LsB,EA4LtB,MA5LsB,EA6LtB,MA7LsB,EA8LtB,MA9LsB,EA+LtB,MA/LsB,EAgMtB,MAhMsB,EAiMtB,MAjMsB,EAkMtB,MAlMsB,EAmMtB,MAnMsB,EAoMtB,MApMsB,EAqMtB,MArMsB,EAsMtB,MAtMsB,EAuMtB,MAvMsB,EAwMtB,MAxMsB,EAyMtB,MAzMsB,EA0MtB,MA1MsB,EA2MtB,MA3MsB,EA4MtB,MA5MsB,EA6MtB,MA7MsB,EA8MtB,MA9MsB,EA+MtB,MA/MsB,EAgNtB,MAhNsB,EAiNtB,MAjNsB,EAkNtB,MAlNsB,EAmNtB,MAnNsB,EAoNtB,MApNsB,EAqNtB,MArNsB,EAsNtB,MAtNsB,EAuNtB,MAvNsB,EAwNtB,MAxNsB,EAyNtB,MAzNsB,EA0NtB,MA1NsB,EA2NtB,MA3NsB,EA4NtB,MA5NsB,EA6NtB,MA7NsB,EA8NtB,MA9NsB,EA+NtB,MA/NsB,EAgOtB,MAhOsB,EAiOtB,MAjOsB,EAkOtB,MAlOsB,EAmOtB,MAnOsB,EAoOtB,MApOsB,EAqOtB,MArOsB,EAsOtB,MAtOsB,EAuOtB,MAvOsB,EAwOtB,MAxOsB,EAyOtB,MAzOsB,EA0OtB,MA1OsB,EA2OtB,MA3OsB,EA4OtB,MA5OsB,EA6OtB,MA7OsB,EA8OtB,MA9OsB,EA+OtB,MA/OsB,EAgPtB,MAhPsB,EAiPtB,MAjPsB,EAkPtB,MAlPsB,EAmPtB,MAnPsB,EAoPtB,MApPsB,EAqPtB,MArPsB,EAsPtB,MAtPsB,EAuPtB,MAvPsB,EAwPtB,MAxPsB,EAyPtB,MAzPsB,EA0PtB,MA1PsB,EA2PtB,MA3PsB,EA4PtB,MA5PsB,EA6PtB,MA7PsB,EA8PtB,MA9PsB,EA+PtB,MA/PsB,EAgQtB,MAhQsB,EAiQtB,MAjQsB,EAkQtB,MAlQsB,EAmQtB,MAnQsB,EAoQtB,MApQsB,EAqQtB,MArQsB,EAsQtB,MAtQsB,EAuQtB,MAvQsB,EAwQtB,MAxQsB,EAyQtB,MAzQsB,EA0QtB,MA1QsB,EA2QtB,MA3QsB,EA4QtB,MA5QsB,EA6QtB,MA7QsB,EA8QtB,MA9QsB,EA+QtB,MA/QsB,EAgRtB,MAhRsB,EAiRtB,MAjRsB,EAkRtB,MAlRsB,EAmRtB,MAnRsB,EAoRtB,MApRsB,EAqRtB,MArRsB,EAsRtB,MAtRsB,EAuRtB,MAvRsB,EAwRtB,MAxRsB,EAyRtB,MAzRsB,EA0RtB,MA1RsB,EA2RtB,MA3RsB,EA4RtB,MA5RsB,EA6RtB,MA7RsB,EA8RtB,MA9RsB,EA+RtB,MA/RsB,EAgStB,MAhSsB,EAiStB,MAjSsB,EAkStB,MAlSsB,EAmStB,MAnSsB,EAoStB,MApSsB,EAqStB,MArSsB,EAsStB,MAtSsB,EAuStB,MAvSsB,EAwStB,MAxSsB,EAyStB,MAzSsB,EA0StB,MA1SsB,EA2StB,MA3SsB,EA4StB,MA5SsB,EA6StB,MA7SsB,EA8StB,MA9SsB,EA+StB,MA/SsB,EAgTtB,MAhTsB,EAiTtB,MAjTsB,EAkTtB,MAlTsB,EAmTtB,MAnTsB,EAoTtB,MApTsB,EAqTtB,MArTsB,EAsTtB,MAtTsB,EAuTtB,MAvTsB,EAwTtB,MAxTsB,EAyTtB,MAzTsB,EA0TtB,MA1TsB,EA2TtB,MA3TsB,EA4TtB,MA5TsB,EA6TtB,MA7TsB,EA8TtB,MA9TsB,EA+TtB,MA/TsB,EAgUtB,MAhUsB,EAiUtB,MAjUsB,EAkUtB,MAlUsB,EAmUtB,MAnUsB,EAoUtB,MApUsB,EAqUtB,MArUsB,EAsUtB,MAtUsB,EAuUtB,MAvUsB,EAwUtB,MAxUsB,EAyUtB,MAzUsB,EA0UtB,MA1UsB,EA2UtB,MA3UsB,EA4UtB,MA5UsB,EA6UtB,MA7UsB,EA8UtB,MA9UsB,EA+UtB,MA/UsB,EAgVtB,MAhVsB,EAiVtB,MAjVsB,EAkVtB,MAlVsB,EAmVtB,MAnVsB,EAoVtB,MApVsB,EAqVtB,MArVsB,EAsVtB,MAtVsB,EAuVtB,MAvVsB,EAwVtB,MAxVsB,EAyVtB,MAzVsB,EA0VtB,MA1VsB,EA2VtB,MA3VsB,EA4VtB,MA5VsB,EA6VtB,MA7VsB,EA8VtB,MA9VsB,EA+VtB,MA/VsB,EAgWtB,MAhWsB,EAiWtB,MAjWsB,EAkWtB,MAlWsB,EAmWtB,MAnWsB,EAoWtB,MApWsB,EAqWtB,MArWsB,EAsWtB,MAtWsB,EAuWtB,MAvWsB,EAwWtB,MAxWsB,EAyWtB,MAzWsB,EA0WtB,MA1WsB,EA2WtB,MA3WsB,EA4WtB,MA5WsB,EA6WtB,MA7WsB,EA8WtB,MA9WsB,EA+WtB,MA/WsB,EAgXtB,MAhXsB,EAiXtB,MAjXsB,EAkXtB,MAlXsB,EAmXtB,MAnXsB,EAoXtB,MApXsB,EAqXtB,MArXsB,EAsXtB,MAtXsB,EAuXtB,MAvXsB,EAwXtB,MAxXsB,EAyXtB,MAzXsB,EA0XtB,MA1XsB,EA2XtB,MA3XsB,EA4XtB,MA5XsB,EA6XtB,MA7XsB,EA8XtB,MA9XsB,EA+XtB,MA/XsB,EAgYtB,MAhYsB,EAiYtB,MAjYsB,EAkYtB,MAlYsB,EAmYtB,MAnYsB,EAoYtB,MApYsB,EAqYtB,MArYsB,EAsYtB,MAtYsB,EAuYtB,MAvYsB,EAwYtB,MAxYsB,EAyYtB,MAzYsB,EA0YtB,MA1YsB,EA2YtB,MA3YsB,EA4YtB,MA5YsB,EA6YtB,MA7YsB,EA8YtB,MA9YsB,EA+YtB,MA/YsB,EAgZtB,MAhZsB,EAiZtB,MAjZsB,EAkZtB,MAlZsB,EAmZtB,MAnZsB,EAoZtB,MApZsB,EAqZtB,MArZsB,EAsZtB,MAtZsB,EAuZtB,MAvZsB,EAwZtB,MAxZsB,EAyZtB,MAzZsB,EA0ZtB,MA1ZsB,EA2ZtB,MA3ZsB,EA4ZtB,MA5ZsB,EA6ZtB,MA7ZsB,EA8ZtB,MA9ZsB,EA+ZtB,MA/ZsB,EAgatB,MAhasB,EAiatB,MAjasB,EAkatB,MAlasB,EAmatB,MAnasB,EAoatB,MApasB,EAqatB,MArasB,EAsatB,MAtasB,EAuatB,MAvasB,EAwatB,MAxasB,EAyatB,MAzasB,EA0atB,MA1asB,EA2atB,MA3asB,EA4atB,MA5asB,EA6atB,MA7asB,EA8atB,MA9asB,EA+atB,MA/asB,EAgbtB,MAhbsB,EAibtB,MAjbsB,EAkbtB,MAlbsB,EAmbtB,MAnbsB,EAobtB,MApbsB,EAqbtB,MArbsB,EAsbtB,MAtbsB,EAubtB,MAvbsB,EAwbtB,MAxbsB,EAybtB,MAzbsB,EA0btB,MA1bsB,EA2btB,MA3bsB,EA4btB,MA5bsB,EA6btB,MA7bsB,EA8btB,MA9bsB,EA+btB,MA/bsB,EAgctB,MAhcsB,EAictB,MAjcsB,EAkctB,MAlcsB,EAmctB,MAncsB,EAoctB,MApcsB,EAqctB,MArcsB,EAsctB,MAtcsB,EAuctB,MAvcsB,EAwctB,MAxcsB,EAyctB,MAzcsB,EA0ctB,MA1csB,EA2ctB,MA3csB,EA4ctB,MA5csB,EA6ctB,MA7csB,EA8ctB,MA9csB,EA+ctB,MA/csB,EAgdtB,MAhdsB,EAidtB,MAjdsB,EAkdtB,MAldsB,EAmdtB,MAndsB,EAodtB,MApdsB,EAqdtB,MArdsB,EAsdtB,MAtdsB,EAudtB,MAvdsB,EAwdtB,MAxdsB,EAydtB,MAzdsB,EA0dtB,MA1dsB,EA2dtB,MA3dsB,EA4dtB,MA5dsB,EA6dtB,MA7dsB,EA8dtB,MA9dsB,EA+dtB,MA/dsB,EAgetB,MAhesB,EAietB,MAjesB,EAketB,MAlesB,EAmetB,MAnesB,EAoetB,MApesB,EAqetB,MAresB,EAsetB,MAtesB,EAuetB,MAvesB,EAwetB,MAxesB,EAyetB,MAzesB,EA0etB,MA1esB,EA2etB,MA3esB,EA4etB,MA5esB,EA6etB,MA7esB,EA8etB,MA9esB,EA+etB,MA/esB,EAgftB,MAhfsB,EAiftB,MAjfsB,EAkftB,MAlfsB,EAmftB,MAnfsB,EAoftB,MApfsB,EAqftB,MArfsB,EAsftB,MAtfsB,EAuftB,MAvfsB,EAwftB,MAxfsB,EAyftB,MAzfsB,EA0ftB,MA1fsB,EA2ftB,MA3fsB,EA4ftB,MA5fsB,EA6ftB,MA7fsB,EA8ftB,MA9fsB,EA+ftB,MA/fsB,EAggBtB,MAhgBsB,EAigBtB,MAjgBsB,EAkgBtB,MAlgBsB,EAmgBtB,MAngBsB,EAogBtB,MApgBsB,EAqgBtB,MArgBsB,EAsgBtB,MAtgBsB,EAugBtB,MAvgBsB,EAwgBtB,MAxgBsB,EAygBtB,MAzgBsB,EA0gBtB,MA1gBsB,EA2gBtB,MA3gBsB,EA4gBtB,MA5gBsB,EA6gBtB,MA7gBsB,EA8gBtB,MA9gBsB,EA+gBtB,MA/gBsB,EAghBtB,MAhhBsB,EAihBtB,MAjhBsB,EAkhBtB,MAlhBsB,EAmhBtB,MAnhBsB,EAohBtB,MAphBsB,EAqhBtB,MArhBsB,EAshBtB,MAthBsB,EAuhBtB,MAvhBsB,EAwhBtB,MAxhBsB,EAyhBtB,MAzhBsB,EA0hBtB,MA1hBsB,EA2hBtB,MA3hBsB,EA4hBtB,MA5hBsB,EA6hBtB,MA7hBsB,EA8hBtB,MA9hBsB,EA+hBtB,MA/hBsB,EAgiBtB,MAhiBsB,EAiiBtB,MAjiBsB,EAkiBtB,MAliBsB,EAmiBtB,MAniBsB,EAoiBtB,MApiBsB,EAqiBtB,MAriBsB,EAsiBtB,MAtiBsB,EAuiBtB,MAviBsB,EAwiBtB,MAxiBsB,EAyiBtB,MAziBsB,EA0iBtB,MA1iBsB,EA2iBtB,MA3iBsB,EA4iBtB,MA5iBsB,EA6iBtB,MA7iBsB,EA8iBtB,MA9iBsB,EA+iBtB,MA/iBsB,EAgjBtB,MAhjBsB,EAijBtB,MAjjBsB,EAkjBtB,MAljBsB,EAmjBtB,MAnjBsB,EAojBtB,MApjBsB,EAqjBtB,MArjBsB,EAsjBtB,MAtjBsB,EAujBtB,MAvjBsB,EAwjBtB,MAxjBsB,EAyjBtB,MAzjBsB,EA0jBtB,MA1jBsB,EA2jBtB,MA3jBsB,EA4jBtB,MA5jBsB,EA6jBtB,MA7jBsB,EA8jBtB,MA9jBsB,EA+jBtB,MA/jBsB,EAgkBtB,MAhkBsB,EAikBtB,MAjkBsB,EAkkBtB,MAlkBsB,EAmkBtB,MAnkBsB,EAokBtB,MApkBsB,EAqkBtB,MArkBsB,EAskBtB,MAtkBsB,EAukBtB,MAvkBsB,EAwkBtB,MAxkBsB,EAykBtB,MAzkBsB,EA0kBtB,MA1kBsB,EA2kBtB,MA3kBsB,EA4kBtB,MA5kBsB,EA6kBtB,MA7kBsB,EA8kBtB,MA9kBsB,EA+kBtB,MA/kBsB,EAglBtB,MAhlBsB,EAilBtB,MAjlBsB,EAklBtB,MAllBsB,EAmlBtB,MAnlBsB,EAolBtB,MAplBsB,EAqlBtB,MArlBsB,EAslBtB,MAtlBsB,EAulBtB,MAvlBsB,EAwlBtB,MAxlBsB,EAylBtB,MAzlBsB,EA0lBtB,MA1lBsB,EA2lBtB,MA3lBsB,EA4lBtB,MA5lBsB,EA6lBtB,MA7lBsB,EA8lBtB,MA9lBsB,EA+lBtB,MA/lBsB,EAgmBtB,MAhmBsB,EAimBtB,MAjmBsB,EAkmBtB,MAlmBsB,EAmmBtB,MAnmBsB,EAomBtB,MApmBsB,EAqmBtB,MArmBsB,EAsmBtB,MAtmBsB,EAumBtB,MAvmBsB,EAwmBtB,MAxmBsB,EAymBtB,MAzmBsB,EA0mBtB,MA1mBsB,EA2mBtB,MA3mBsB,EA4mBtB,MA5mBsB,EA6mBtB,MA7mBsB,EA8mBtB,MA9mBsB,EA+mBtB,MA/mBsB,EAgnBtB,MAhnBsB,EAinBtB,MAjnBsB,EAknBtB,MAlnBsB,EAmnBtB,MAnnBsB,EAonBtB,MApnBsB,EAqnBtB,MArnBsB,EAsnBtB,MAtnBsB,EAunBtB,MAvnBsB,EAwnBtB,MAxnBsB,EAynBtB,MAznBsB,EA0nBtB,MA1nBsB,EA2nBtB,MA3nBsB,EA4nBtB,MA5nBsB,EA6nBtB,MA7nBsB,EA8nBtB,MA9nBsB,EA+nBtB,MA/nBsB,EAgoBtB,MAhoBsB,EAioBtB,MAjoBsB,EAkoBtB,MAloBsB,EAmoBtB,MAnoBsB,EAooBtB,MApoBsB,EAqoBtB,MAroBsB,EAsoBtB,MAtoBsB,EAuoBtB,OAvoBsB,EAwoBtB,OAxoBsB,EAyoBtB,OAzoBsB,EA0oBtB,OA1oBsB,EA2oBtB,OA3oBsB,EA4oBtB,OA5oBsB,EA6oBtB,OA7oBsB,EA8oBtB,OA9oBsB,EA+oBtB,OA/oBsB,EAgpBtB,OAhpBsB,EAipBtB,OAjpBsB,EAkpBtB,OAlpBsB,EAmpBtB,OAnpBsB,EAopBtB,OAppBsB,EAqpBtB,OArpBsB,EAspBtB,OAtpBsB,EAupBtB,OAvpBsB,EAwpBtB,OAxpBsB,EAypBtB,OAzpBsB,EA0pBtB,OA1pBsB,EA2pBtB,OA3pBsB,EA4pBtB,OA5pBsB,EA6pBtB,OA7pBsB,EA8pBtB,OA9pBsB,EA+pBtB,OA/pBsB,EAgqBtB,OAhqBsB,EAiqBtB,OAjqBsB,EAkqBtB,OAlqBsB,EAmqBtB,OAnqBsB,EAoqBtB,OApqBsB,EAqqBtB,OArqBsB,EAsqBtB,OAtqBsB,EAuqBtB,OAvqBsB,EAwqBtB,OAxqBsB,EAyqBtB,OAzqBsB,EA0qBtB,OA1qBsB,EA2qBtB,OA3qBsB,EA4qBtB,OA5qBsB,EA6qBtB,OA7qBsB,EA8qBtB,OA9qBsB,EA+qBtB,OA/qBsB,EAgrBtB,OAhrBsB,EAirBtB,OAjrBsB,EAkrBtB,OAlrBsB,EAmrBtB,OAnrBsB,EAorBtB,OAprBsB,EAqrBtB,OArrBsB,EAsrBtB,OAtrBsB,EAurBtB,OAvrBsB,EAwrBtB,OAxrBsB,EAyrBtB,OAzrBsB,EA0rBtB,OA1rBsB,EA2rBtB,OA3rBsB,EA4rBtB,OA5rBsB,EA6rBtB,OA7rBsB,EA8rBtB,OA9rBsB,EA+rBtB,OA/rBsB,EAgsBtB,OAhsBsB,EAisBtB,OAjsBsB,EAksBtB,OAlsBsB,EAmsBtB,OAnsBsB,EAosBtB,OApsBsB,EAqsBtB,OArsBsB,EAssBtB,OAtsBsB,EAusBtB,OAvsBsB,EAwsBtB,OAxsBsB,EAysBtB,OAzsBsB,EA0sBtB,OA1sBsB,EA2sBtB,OA3sBsB,EA4sBtB,OA5sBsB,EA6sBtB,OA7sBsB,EA8sBtB,OA9sBsB,EA+sBtB,QA/sBsB,EAgtBtB,QAhtBsB,CAAxB;;AAotBA,IAAMC,gBAAgB,GAAGZ,SAAS,IAAIV,OAAO,CAACU,SAAD,EAAYW,eAAZ,CAA7C;;ACn3DA;;;;;AAIA,IAAME,aAAa,GAAGT,wBAAtB;;;;;;AAMA,IAAMU,eAAe,GAAGZ,yBAAxB;;AAGA,IAAMa,YAAY,GAAGf,SAAS,IAAIA,SAAS,CAACgB,WAAV,CAAsB,CAAtB,CAAlC;;AACA,IAAM/L,KAAK,GAAGgM,CAAC,IAAIA,CAAC,CAAC,CAAD,CAApB;;AACA,IAAM/L,IAAI,GAAG+L,CAAC,IAAIA,CAAC,CAACA,CAAC,CAACjM,MAAF,GAAW,CAAZ,CAAnB;;;;;;;;;;AASA,SAASkM,YAAT,CAAsBC,KAAtB,EAA6B;MACrBC,UAAU,GAAG,EAAnB;MACMtE,IAAI,GAAGqE,KAAK,CAACnM,MAAnB;;OAEK,IAAIkB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG4G,IAApB,EAA0B5G,CAAC,IAAI,CAA/B,EAAkC;QAC1BmL,MAAM,GAAGF,KAAK,CAAC1K,UAAN,CAAiBP,CAAjB,CAAf;;QAEImL,MAAM,IAAI,MAAV,IAAoBA,MAAM,IAAI,MAA9B,IAAwCvE,IAAI,GAAG5G,CAAC,GAAG,CAAvD,EAA0D;UAClDoL,IAAI,GAAGH,KAAK,CAAC1K,UAAN,CAAiBP,CAAC,GAAG,CAArB,CAAb;;UAEIoL,IAAI,IAAI,MAAR,IAAkBA,IAAI,IAAI,MAA9B,EAAsC;QACpCF,UAAU,CAACjM,IAAX,CAAgB,CAACkM,MAAM,GAAG,MAAV,IAAoB,KAApB,GAA4BC,IAA5B,GAAmC,MAAnC,GAA4C,OAA5D;QACApL,CAAC,IAAI,CAAL;;;;;IAKJkL,UAAU,CAACjM,IAAX,CAAgBkM,MAAhB;;;SAGKD,UAAP;;;;;;;;;;;AAUF,SAASG,QAAT,CAAkBJ,KAAlB,EAAoC;MAAXK,IAAW,uEAAJ,EAAI;;MAC9B,OAAOL,KAAP,KAAiB,QAArB,EAA+B;UACvB,IAAIM,SAAJ,CAAc,kBAAd,CAAN;;;MAGEN,KAAK,CAACnM,MAAN,KAAiB,CAArB,EAAwB;WACf,EAAP;GANgC;;;MAU5B0M,YAAY,GAAGR,YAAY,CAACC,KAAD,CAAZ;GAElBzJ,GAFkB,CAEdsI,SAAS,IAAKa,aAAa,CAACb,SAAD,CAAb,GAA2B,IAA3B,GAAkCA,SAFlC;GAIlB2B,MAJkB,CAIX3B,SAAS,IAAI,CAACc,eAAe,CAACd,SAAD,CAJlB,CAArB,CAVkC;;MAiB5B4B,gBAAgB,GAAGtL,MAAM,CAACuL,aAAP,CACtBC,KADsB,CAChB,IADgB,EACVJ,YADU,EAEtBK,SAFsB,CAEZ,MAFY,CAAzB;MAIMC,cAAc,GAAGd,YAAY,CAACU,gBAAD,CAAnC,CArBkC;;MAwB5BK,aAAa,GAAGD,cAAc,CAACE,IAAf,CAAoB1B,qBAApB,CAAtB;;MAEIyB,aAAJ,EAAmB;UACX,IAAInO,KAAJ,CACJ,2EADI,CAAN;GA3BgC;;;MAiC9B0N,IAAI,CAACW,eAAL,KAAyB,IAA7B,EAAmC;QAC3BC,aAAa,GAAGJ,cAAc,CAACE,IAAf,CAAoBnC,qBAApB,CAAtB;;QAEIqC,aAAJ,EAAmB;YACX,IAAItO,KAAJ,CACJ,4EADI,CAAN;;GArC8B;;;MA6C5BuO,UAAU,GAAGL,cAAc,CAACE,IAAf,CAAoBxB,kBAApB,CAAnB;MAEM4B,QAAQ,GAAGN,cAAc,CAACE,IAAf,CAAoBtB,gBAApB,CAAjB,CA/CkC;;;MAmD9ByB,UAAU,IAAIC,QAAlB,EAA4B;UACpB,IAAIxO,KAAJ,CACJ,iEACE,oDAFE,CAAN;;;;;;;;;MAYIyO,cAAc,GAAG7B,kBAAkB,CACvCK,YAAY,CAAC9L,KAAK,CAAC2M,gBAAD,CAAN,CAD2B,CAAzC;MAGMY,aAAa,GAAG9B,kBAAkB,CACtCK,YAAY,CAAC7L,IAAI,CAAC0M,gBAAD,CAAL,CAD0B,CAAxC;;MAIIS,UAAU,IAAI,EAAEE,cAAc,IAAIC,aAApB,CAAlB,EAAsD;UAC9C,IAAI1O,KAAJ,CACJ,qEACE,6EAFE,CAAN;;;SAMK8N,gBAAP;;;AC/IF;;;;AAKA;AAGA,MAAMa,WAAN,CAAkB;SACTC,cAAP,GAAiC;QAAXC,IAAW,uEAAJ,EAAI;QAC3BC,OAAO,aAAMD,IAAI,CAACE,YAAL,CAAkBC,OAAlB,EAAN,OAAX;;SAEK,IAAIzO,GAAT,IAAgBsO,IAAhB,EAAsB;;UAEhB,CAACA,IAAI,CAACI,cAAL,CAAoB1O,GAApB,CAAL,EAA+B;;;;MAG/BuO,OAAO,cAAOvO,GAAP,eAAesO,IAAI,CAACtO,GAAD,CAAJ,CAAUwC,OAAV,EAAf,OAAP;;;WAGKmM,iBAAiB,CAACC,QAAQ,CAACC,GAAT,CAAaN,OAAb,CAAD,CAAxB;;;SAGKO,uBAAP,CAA+BC,KAA/B,EAAsC;WAC7BH,QAAQ,CAACI,GAAT,CAAaC,SAAb,CAAuBC,MAAvB,CAA8BH,KAA9B,CAAP;;;SAGKI,MAAP,CAActL,QAAd,EAAsC;QAAdjE,OAAc,uEAAJ,EAAI;;QAChC,CAACA,OAAO,CAACwP,aAAT,IAA0B,CAACxP,OAAO,CAACyP,YAAvC,EAAqD;aAC5C,IAAP;;;WAEK,IAAIjB,WAAJ,CAAgBvK,QAAhB,EAA0BjE,OAA1B,CAAP;;;EAGFD,WAAW,CAACkE,QAAD,EAAyB;QAAdjE,OAAc,uEAAJ,EAAI;;QAC9B,CAACA,OAAO,CAACwP,aAAT,IAA0B,CAACxP,OAAO,CAACyP,YAAvC,EAAqD;YAC7C,IAAI5P,KAAJ,CAAU,sDAAV,CAAN;;;SAGGoE,QAAL,GAAgBA,QAAhB;;SACKyL,gBAAL,CAAsB1P,OAAtB;;;EAGF0P,gBAAgB,CAAC1P,OAAD,EAAU;YAChBA,OAAO,CAAC2P,UAAhB;WACO,KAAL;WACK,KAAL;aACOC,OAAL,GAAe,CAAf;;;WAEG,KAAL;WACK,KAAL;aACOA,OAAL,GAAe,CAAf;;;WAEG,SAAL;aACOA,OAAL,GAAe,CAAf;;;;aAGKA,OAAL,GAAe,CAAf;;;;QAIEC,OAAO,GAAG;MACdvL,MAAM,EAAE;KADV;;YAIQ,KAAKsL,OAAb;WACO,CAAL;WACK,CAAL;WACK,CAAL;aACOE,sBAAL,CAA4B,KAAKF,OAAjC,EAA0CC,OAA1C,EAAmD7P,OAAnD;;;;WAEG,CAAL;aACO+P,kBAAL,CAAwBF,OAAxB,EAAiC7P,OAAjC;;;;;SAICyJ,UAAL,GAAkB,KAAKxF,QAAL,CAAcqF,GAAd,CAAkBuG,OAAlB,CAAlB;;;EAGFC,sBAAsB,CAACE,CAAD,EAAIH,OAAJ,EAAa7P,OAAb,EAAsB;QACtCiQ,CAAJ,EAAOC,WAAP;;YACQF,CAAR;WACO,CAAL;QACEC,CAAC,GAAG,CAAJ;aACKE,OAAL,GAAe,EAAf;QACAD,WAAW,GAAGE,gBAAgB,CAACpQ,OAAO,CAACkQ,WAAT,CAA9B;;;WAEG,CAAL;QACED,CAAC,GAAG,CAAJ;aACKE,OAAL,GAAe,GAAf;QACAD,WAAW,GAAGG,gBAAgB,CAACrQ,OAAO,CAACkQ,WAAT,CAA9B;;;WAEG,CAAL;QACED,CAAC,GAAG,CAAJ;aACKE,OAAL,GAAe,GAAf;QACAD,WAAW,GAAGG,gBAAgB,CAACrQ,OAAO,CAACkQ,WAAT,CAA9B;;;;QAIEI,kBAAkB,GAAGC,qBAAqB,CAACvQ,OAAO,CAACyP,YAAT,CAAhD;QACMe,mBAAmB,GAAGxQ,OAAO,CAACwP,aAAR,GACxBe,qBAAqB,CAACvQ,OAAO,CAACwP,aAAT,CADG,GAExBc,kBAFJ;QAIMG,kBAAkB,GAAGC,sBAAsB,CAC/CT,CAD+C,EAE/C,KAAKE,OAF0C,EAG/CG,kBAH+C,EAI/CE,mBAJ+C,CAAjD;SAMKG,aAAL,GAAqBC,sBAAsB,CACzCX,CADyC,EAEzC,KAAKE,OAFoC,EAGzC,KAAKlM,QAAL,CAAc4M,GAH2B,EAIzCP,kBAJyC,EAKzCG,kBALyC,EAMzCP,WANyC,CAA3C;QAQIY,iBAAJ;;QACIb,CAAC,KAAK,CAAV,EAAa;MACXa,iBAAiB,GAAGC,iBAAiB,CAAC,KAAKJ,aAAN,CAArC;KADF,MAEO;MACLG,iBAAiB,GAAGE,mBAAmB,CACrC,KAAK/M,QAAL,CAAc4M,GADuB,EAErC,KAAKF,aAFgC,CAAvC;;;IAMFd,OAAO,CAACoB,CAAR,GAAYjB,CAAZ;;QACIA,CAAC,IAAI,CAAT,EAAY;MACVH,OAAO,CAAClL,MAAR,GAAiB,KAAKwL,OAAtB;;;QAEEH,CAAC,KAAK,CAAV,EAAa;MACXH,OAAO,CAACqB,EAAR,GAAa;QACXC,KAAK,EAAE;UACLC,SAAS,EAAE,SADN;UAELC,GAAG,EAAE,OAFA;UAGL1M,MAAM,EAAE,KAAKwL,OAAL,GAAe;;OAJ3B;MAOAN,OAAO,CAACyB,IAAR,GAAe,OAAf;MACAzB,OAAO,CAAC0B,IAAR,GAAe,OAAf;;;IAEF1B,OAAO,CAAC2B,CAAR,GAAYvB,CAAZ;IACAJ,OAAO,CAAC4B,CAAR,GAAY1C,iBAAiB,CAAC0B,kBAAD,CAA7B;IACAZ,OAAO,CAAC6B,CAAR,GAAY3C,iBAAiB,CAAC+B,iBAAD,CAA7B;IACAjB,OAAO,CAAC8B,CAAR,GAAYzB,WAAZ;;;EAGFH,kBAAkB,CAACF,OAAD,EAAU7P,OAAV,EAAmB;SAC9BmQ,OAAL,GAAe,GAAf;QACMD,WAAW,GAAGG,gBAAgB,CAACrQ,OAAO,CAACkQ,WAAT,CAApC;QAEM0B,qBAAqB,GAAGC,iBAAiB,CAAC7R,OAAO,CAACyP,YAAT,CAA/C;QACMqC,sBAAsB,GAAG9R,OAAO,CAACwP,aAAR,GAC3BqC,iBAAiB,CAAC7R,OAAO,CAACwP,aAAT,CADU,GAE3BoC,qBAFJ;SAIKjB,aAAL,GAAqBoB,kBAAkB,CACrCvD,WAAW,CAACU,uBADyB,CAAvC;QAGM4B,iBAAiB,GAAGkB,iBAAiB,CACzCJ,qBADyC,EAEzCpD,WAAW,CAACU,uBAF6B,CAA3C;QAIM+C,WAAW,GAAGjD,QAAQ,CAACI,GAAT,CAAaC,SAAb,CAAuBE,MAAvB,CAClBuB,iBAAiB,CAACoB,KAAlB,CAAwBvQ,KAAxB,CAA8B,EAA9B,EAAkC,EAAlC,CADkB,EAElB,CAFkB,CAApB;QAIMwQ,sBAAsB,GAAGC,sBAAsB,CACnDR,qBADmD,EAEnDK,WAFmD,EAGnD,KAAKtB,aAH8C,CAArD;QAKMF,kBAAkB,GAAG4B,kBAAkB,CAC3CP,sBAD2C,EAE3ChB,iBAF2C,EAG3CtC,WAAW,CAACU,uBAH+B,CAA7C;QAKMoD,YAAY,GAAGtD,QAAQ,CAACI,GAAT,CAAaC,SAAb,CAAuBE,MAAvB,CACnBkB,kBAAkB,CAACyB,KAAnB,CAAyBvQ,KAAzB,CAA+B,EAA/B,EAAmC,EAAnC,CADmB,EAEnB,CAFmB,CAArB;QAIM4Q,uBAAuB,GAAGC,uBAAuB,CACrDV,sBADqD,EAErDQ,YAFqD,EAGrDxB,iBAHqD,EAIrD,KAAKH,aAJgD,CAAvD;QAMM8B,UAAU,GAAGC,yBAAyB,CAC1CxC,WAD0C,EAE1C,KAAKS,aAFqC,EAG1CnC,WAAW,CAACU,uBAH8B,CAA5C;IAMAW,OAAO,CAACoB,CAAR,GAAY,CAAZ;IACApB,OAAO,CAAClL,MAAR,GAAiB,KAAKwL,OAAtB;IACAN,OAAO,CAACqB,EAAR,GAAa;MACXC,KAAK,EAAE;QACLC,SAAS,EAAE,SADN;QAELC,GAAG,EAAE,OAFA;QAGL1M,MAAM,EAAE,KAAKwL,OAAL,GAAe;;KAJ3B;IAOAN,OAAO,CAACyB,IAAR,GAAe,OAAf;IACAzB,OAAO,CAAC0B,IAAR,GAAe,OAAf;IACA1B,OAAO,CAAC2B,CAAR,GAAY,CAAZ;IACA3B,OAAO,CAAC4B,CAAR,GAAY1C,iBAAiB,CAAC0B,kBAAD,CAA7B;IACAZ,OAAO,CAAC8C,EAAR,GAAa5D,iBAAiB,CAACwD,uBAAD,CAA9B;IACA1C,OAAO,CAAC6B,CAAR,GAAY3C,iBAAiB,CAAC+B,iBAAD,CAA7B;IACAjB,OAAO,CAAC+C,EAAR,GAAa7D,iBAAiB,CAACoD,sBAAD,CAA9B;IACAtC,OAAO,CAAC8B,CAAR,GAAYzB,WAAZ;IACAL,OAAO,CAACgD,KAAR,GAAgB9D,iBAAiB,CAAC0D,UAAD,CAAjC;;;EAGFzN,YAAY,CAAC8N,GAAD,EAAM1O,GAAN,EAAW;QACjB2O,MAAJ;;QACI,KAAKnD,OAAL,GAAe,CAAnB,EAAsB;MACpBmD,MAAM,GAAG,KAAKpC,aAAL,CACNqC,KADM,GAEN/N,MAFM,CAGL+J,QAAQ,CAACI,GAAT,CAAaC,SAAb,CAAuBE,MAAvB,CACE,CACG,CAACuD,GAAG,GAAG,IAAP,KAAgB,EAAjB,GACG,CAACA,GAAG,GAAG,MAAP,KAAkB,CADrB,GAEIA,GAAG,IAAI,CAAR,GAAa,MAFhB,GAGG1O,GAAG,GAAG,IAJX,EAKE,CAACA,GAAG,GAAG,MAAP,KAAkB,EALpB,CADF,EAQE,CARF,CAHK,CAAT;;;QAgBE,KAAKwL,OAAL,KAAiB,CAAjB,IAAsB,KAAKA,OAAL,KAAiB,CAA3C,EAA8C;UACxCxP,IAAG,GAAG4O,QAAQ,CAACC,GAAT,CAAa8D,MAAb,CAAV;;MACA3S,IAAG,CAAC6S,QAAJ,GAAenP,IAAI,CAACoP,GAAL,CAAS,EAAT,EAAa,KAAK/C,OAAL,GAAe,CAAf,GAAmB,CAAhC,CAAf;aACO3L,MAAM,IACXuK,iBAAiB,CACfC,QAAQ,CAACmE,GAAT,CAAaC,OAAb,CAAqBpE,QAAQ,CAACI,GAAT,CAAaC,SAAb,CAAuBE,MAAvB,CAA8B/K,MAA9B,CAArB,EAA4DpE,IAA5D,EACGiT,UAFY,CADnB;;;QAOEjT,GAAJ;;QACI,KAAKwP,OAAL,KAAiB,CAArB,EAAwB;MACtBxP,GAAG,GAAG4O,QAAQ,CAACC,GAAT,CACJ8D,MAAM,CAAC9N,MAAP,CAAc+J,QAAQ,CAACI,GAAT,CAAaC,SAAb,CAAuBE,MAAvB,CAA8B,CAAC,UAAD,CAA9B,EAA4C,CAA5C,CAAd,CADI,CAAN;KADF,MAIO;MACLnP,GAAG,GAAG,KAAKuQ,aAAX;;;QAGI2C,EAAE,GAAG9E,WAAW,CAACU,uBAAZ,CAAoC,EAApC,CAAX;QACMlP,OAAO,GAAG;MACduT,IAAI,EAAEvE,QAAQ,CAACuE,IAAT,CAAcC,GADN;MAEdC,OAAO,EAAEzE,QAAQ,CAACxN,GAAT,CAAakS,KAFR;MAGdJ;KAHF;WAMO9O,MAAM,IACXuK,iBAAiB,CACfuE,EAAE,CACCN,KADH,GAEG/N,MAFH,CAGI+J,QAAQ,CAAC2E,GAAT,CAAaP,OAAb,CACEpE,QAAQ,CAACI,GAAT,CAAaC,SAAb,CAAuBE,MAAvB,CAA8B/K,MAA9B,CADF,EAEEpE,GAFF,EAGEJ,OAHF,EAIEqT,UAPN,CADe,CADnB;;;EAcFnR,GAAG,GAAG;SACCuH,UAAL,CAAgBvH,GAAhB;;;;;AAIJ,SAASkO,gBAAT,GAAiD;MAAvBwD,gBAAuB,uEAAJ,EAAI;MAC3C1D,WAAW,GAAG,cAAc,CAAhC;;MACI0D,gBAAgB,CAACC,QAArB,EAA+B;IAC7B3D,WAAW,IAAI,cAAf;;;MAEE0D,gBAAgB,CAACE,SAArB,EAAgC;IAC9B5D,WAAW,IAAI,cAAf;;;MAEE0D,gBAAgB,CAACG,OAArB,EAA8B;IAC5B7D,WAAW,IAAI,cAAf;;;MAEE0D,gBAAgB,CAACI,UAArB,EAAiC;IAC/B9D,WAAW,IAAI,cAAf;;;SAEKA,WAAP;;;AAGF,SAASG,gBAAT,GAAiD;MAAvBuD,gBAAuB,uEAAJ,EAAI;MAC3C1D,WAAW,GAAG,cAAc,CAAhC;;MACI0D,gBAAgB,CAACC,QAAjB,KAA8B,eAAlC,EAAmD;IACjD3D,WAAW,IAAI,cAAf;;;MAEE0D,gBAAgB,CAACC,QAAjB,KAA8B,gBAAlC,EAAoD;IAClD3D,WAAW,IAAI,cAAf;;;MAEE0D,gBAAgB,CAACE,SAArB,EAAgC;IAC9B5D,WAAW,IAAI,cAAf;;;MAEE0D,gBAAgB,CAACG,OAArB,EAA8B;IAC5B7D,WAAW,IAAI,cAAf;;;MAEE0D,gBAAgB,CAACI,UAArB,EAAiC;IAC/B9D,WAAW,IAAI,cAAf;;;MAEE0D,gBAAgB,CAACK,YAArB,EAAmC;IACjC/D,WAAW,IAAI,cAAf;;;MAEE0D,gBAAgB,CAACM,oBAArB,EAA2C;IACzChE,WAAW,IAAI,cAAf;;;MAEE0D,gBAAgB,CAACO,gBAArB,EAAuC;IACrCjE,WAAW,IAAI,cAAf;;;SAEKA,WAAP;;;AAGF,SAASa,iBAAT,CAA2BJ,aAA3B,EAA0C;SACjC3B,QAAQ,CAACmE,GAAT,CAAaC,OAAb,CAAqB7C,qBAAqB,EAA1C,EAA8CI,aAA9C,EACJ0C,UADH;;;AAIF,SAASrC,mBAAT,CAA6BoD,UAA7B,EAAyCzD,aAAzC,EAAwD;MAChDvQ,GAAG,GAAGuQ,aAAa,CAACqC,KAAd,EAAZ;MACIqB,MAAM,GAAGrF,QAAQ,CAACC,GAAT,CACXsB,qBAAqB,GAAGtL,MAAxB,CAA+B+J,QAAQ,CAACI,GAAT,CAAaC,SAAb,CAAuBE,MAAvB,CAA8B6E,UAA9B,CAA/B,CADW,CAAb;;OAGK,IAAInS,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,EAApB,EAAwBA,CAAC,EAAzB,EAA6B;QACrBqS,QAAQ,GAAGxQ,IAAI,CAACyQ,IAAL,CAAUnU,GAAG,CAAC6S,QAAJ,GAAe,CAAzB,CAAjB;;SACK,IAAIuB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,QAApB,EAA8BE,CAAC,EAA/B,EAAmC;MACjCpU,GAAG,CAAC8R,KAAJ,CAAUsC,CAAV,IACE7D,aAAa,CAACuB,KAAd,CAAoBsC,CAApB,KAA0BvS,CAAC,GAAIA,CAAC,IAAI,CAAV,GAAgBA,CAAC,IAAI,EAArB,GAA4BA,CAAC,IAAI,EAA3D,CADF;;;IAGFoS,MAAM,GAAGrF,QAAQ,CAACmE,GAAT,CAAaC,OAAb,CAAqBiB,MAArB,EAA6BjU,GAA7B,EAAkCiT,UAA3C;;;SAEKgB,MAAM,CAACpP,MAAP,CAAc+J,QAAQ,CAACI,GAAT,CAAaC,SAAb,CAAuBE,MAAvB,CAA8B,IAA9B,EAAoC,EAApC,CAAd,CAAP;;;AAGF,SAASmB,sBAAT,CACET,CADF,EAEEE,OAFF,EAGEG,kBAHF,EAIEE,mBAJF,EAKE;MACIuC,MAAM,GAAGvC,mBAAb;MACIzM,KAAK,GAAGkM,CAAC,IAAI,CAAL,GAAS,EAAT,GAAc,CAA1B;;OACK,IAAIhO,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG8B,KAApB,EAA2B9B,CAAC,EAA5B,EAAgC;IAC9B8Q,MAAM,GAAG/D,QAAQ,CAACC,GAAT,CAAa8D,MAAb,CAAT;;;MAGI3S,GAAG,GAAG2S,MAAM,CAACC,KAAP,EAAZ;EACA5S,GAAG,CAAC6S,QAAJ,GAAe9C,OAAO,GAAG,CAAzB;MACIkE,MAAM,GAAG/D,kBAAb;EACAvM,KAAK,GAAGkM,CAAC,IAAI,CAAL,GAAS,EAAT,GAAc,CAAtB;;OACK,IAAIhO,EAAC,GAAG,CAAb,EAAgBA,EAAC,GAAG8B,KAApB,EAA2B9B,EAAC,EAA5B,EAAgC;QACxBqS,QAAQ,GAAGxQ,IAAI,CAACyQ,IAAL,CAAUnU,GAAG,CAAC6S,QAAJ,GAAe,CAAzB,CAAjB;;SACK,IAAIuB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,QAApB,EAA8BE,CAAC,EAA/B,EAAmC;MACjCpU,GAAG,CAAC8R,KAAJ,CAAUsC,CAAV,IAAezB,MAAM,CAACb,KAAP,CAAasC,CAAb,KAAmBvS,EAAC,GAAIA,EAAC,IAAI,CAAV,GAAgBA,EAAC,IAAI,EAArB,GAA4BA,EAAC,IAAI,EAApD,CAAf;;;IAEFoS,MAAM,GAAGrF,QAAQ,CAACmE,GAAT,CAAaC,OAAb,CAAqBiB,MAArB,EAA6BjU,GAA7B,EAAkCiT,UAA3C;;;SAEKgB,MAAP;;;AAGF,SAASzD,sBAAT,CACEX,CADF,EAEEE,OAFF,EAGEiE,UAHF,EAIE9D,kBAJF,EAKEG,kBALF,EAMEP,WANF,EAOE;MACI9P,GAAG,GAAGkQ,kBAAkB,CACzB0C,KADO,GAEP/N,MAFO,CAEAwL,kBAFA,EAGPxL,MAHO,CAGA+J,QAAQ,CAACI,GAAT,CAAaC,SAAb,CAAuBE,MAAvB,CAA8B,CAACkF,YAAY,CAACvE,WAAD,CAAb,CAA9B,EAA2D,CAA3D,CAHA,EAIPjL,MAJO,CAIA+J,QAAQ,CAACI,GAAT,CAAaC,SAAb,CAAuBE,MAAvB,CAA8B6E,UAA9B,CAJA,CAAV;MAKMrQ,KAAK,GAAGkM,CAAC,IAAI,CAAL,GAAS,EAAT,GAAc,CAA5B;;OACK,IAAIhO,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG8B,KAApB,EAA2B9B,CAAC,EAA5B,EAAgC;IAC9B7B,GAAG,GAAG4O,QAAQ,CAACC,GAAT,CAAa7O,GAAb,CAAN;IACAA,GAAG,CAAC6S,QAAJ,GAAe9C,OAAO,GAAG,CAAzB;;;SAEK/P,GAAP;;;AAGF,SAAS4R,iBAAT,CAA2BJ,qBAA3B,EAAkD1C,uBAAlD,EAA2E;MACnEwF,cAAc,GAAGxF,uBAAuB,CAAC,CAAD,CAA9C;MACMyF,OAAO,GAAGzF,uBAAuB,CAAC,CAAD,CAAvC;SACOF,QAAQ,CAAC4F,MAAT,CAAgBhD,qBAAqB,CAACoB,KAAtB,GAA8B/N,MAA9B,CAAqCyP,cAArC,CAAhB,EACJzP,MADI,CACGyP,cADH,EAEJzP,MAFI,CAEG0P,OAFH,CAAP;;;AAKF,SAASvC,sBAAT,CACER,qBADF,EAEEK,WAFF,EAGEtB,aAHF,EAIE;MACMvQ,GAAG,GAAG4O,QAAQ,CAAC4F,MAAT,CACVhD,qBAAqB,CAACoB,KAAtB,GAA8B/N,MAA9B,CAAqCgN,WAArC,CADU,CAAZ;MAGMjS,OAAO,GAAG;IACduT,IAAI,EAAEvE,QAAQ,CAACuE,IAAT,CAAcC,GADN;IAEdC,OAAO,EAAEzE,QAAQ,CAACxN,GAAT,CAAaqT,SAFR;IAGdvB,EAAE,EAAEtE,QAAQ,CAACI,GAAT,CAAaC,SAAb,CAAuBE,MAAvB,CAA8B,IAA9B,EAAoC,EAApC;GAHN;SAKOP,QAAQ,CAAC2E,GAAT,CAAaP,OAAb,CAAqBzC,aAArB,EAAoCvQ,GAApC,EAAyCJ,OAAzC,EAAkDqT,UAAzD;;;AAGF,SAAShB,kBAAT,CACEP,sBADF,EAEEhB,iBAFF,EAGE5B,uBAHF,EAIE;MACMwF,cAAc,GAAGxF,uBAAuB,CAAC,CAAD,CAA9C;MACMyF,OAAO,GAAGzF,uBAAuB,CAAC,CAAD,CAAvC;SACOF,QAAQ,CAAC4F,MAAT,CACL9C,sBAAsB,CACnBkB,KADH,GAEG/N,MAFH,CAEUyP,cAFV,EAGGzP,MAHH,CAGU6L,iBAHV,CADK,EAMJ7L,MANI,CAMGyP,cANH,EAOJzP,MAPI,CAOG0P,OAPH,CAAP;;;AAUF,SAASnC,uBAAT,CACEV,sBADF,EAEEQ,YAFF,EAGExB,iBAHF,EAIEH,aAJF,EAKE;MACMvQ,GAAG,GAAG4O,QAAQ,CAAC4F,MAAT,CACV9C,sBAAsB,CACnBkB,KADH,GAEG/N,MAFH,CAEUqN,YAFV,EAGGrN,MAHH,CAGU6L,iBAHV,CADU,CAAZ;MAMM9Q,OAAO,GAAG;IACduT,IAAI,EAAEvE,QAAQ,CAACuE,IAAT,CAAcC,GADN;IAEdC,OAAO,EAAEzE,QAAQ,CAACxN,GAAT,CAAaqT,SAFR;IAGdvB,EAAE,EAAEtE,QAAQ,CAACI,GAAT,CAAaC,SAAb,CAAuBE,MAAvB,CAA8B,IAA9B,EAAoC,EAApC;GAHN;SAKOP,QAAQ,CAAC2E,GAAT,CAAaP,OAAb,CAAqBzC,aAArB,EAAoCvQ,GAApC,EAAyCJ,OAAzC,EAAkDqT,UAAzD;;;AAGF,SAAStB,kBAAT,CAA4B7C,uBAA5B,EAAqD;SAC5CA,uBAAuB,CAAC,EAAD,CAA9B;;;AAGF,SAASwD,yBAAT,CACExC,WADF,EAEES,aAFF,EAGEzB,uBAHF,EAIE;MACMmF,MAAM,GAAGrF,QAAQ,CAACI,GAAT,CAAaC,SAAb,CAAuBE,MAAvB,CACb,CAACkF,YAAY,CAACvE,WAAD,CAAb,EAA4B,UAA5B,EAAwC,UAAxC,CADa,EAEb,EAFa,EAGbjL,MAHa,CAGNiK,uBAAuB,CAAC,CAAD,CAHjB,CAAf;MAIMlP,OAAO,GAAG;IACduT,IAAI,EAAEvE,QAAQ,CAACuE,IAAT,CAAcuB,GADN;IAEdrB,OAAO,EAAEzE,QAAQ,CAACxN,GAAT,CAAaqT;GAFxB;SAIO7F,QAAQ,CAAC2E,GAAT,CAAaP,OAAb,CAAqBiB,MAArB,EAA6B1D,aAA7B,EAA4C3Q,OAA5C,EAAqDqT,UAA5D;;;AAGF,SAAS9C,qBAAT,GAA8C;MAAfwE,QAAe,uEAAJ,EAAI;MACtCjU,GAAG,GAAG4B,MAAM,CAACsS,KAAP,CAAa,EAAb,CAAZ;MACMjU,MAAM,GAAGgU,QAAQ,CAAChU,MAAxB;MACIkU,KAAK,GAAG,CAAZ;;SACOA,KAAK,GAAGlU,MAAR,IAAkBkU,KAAK,GAAG,EAAjC,EAAqC;QAC7BC,IAAI,GAAGH,QAAQ,CAACvS,UAAT,CAAoByS,KAApB,CAAb;;QACIC,IAAI,GAAG,IAAX,EAAiB;YACT,IAAIrV,KAAJ,CAAU,mDAAV,CAAN;;;IAEFiB,GAAG,CAACmU,KAAD,CAAH,GAAaC,IAAb;IACAD,KAAK;;;SAEAA,KAAK,GAAG,EAAf,EAAmB;IACjBnU,GAAG,CAACmU,KAAD,CAAH,GAAaE,gBAAgB,CAACF,KAAK,GAAGlU,MAAT,CAA7B;IACAkU,KAAK;;;SAEAjG,QAAQ,CAACI,GAAT,CAAaC,SAAb,CAAuBE,MAAvB,CAA8BzO,GAA9B,CAAP;;;AAGF,SAAS+Q,iBAAT,GAA0C;MAAfkD,QAAe,uEAAJ,EAAI;EACxCA,QAAQ,GAAGK,QAAQ,CAACC,kBAAkB,CAAC/H,QAAQ,CAACyH,QAAD,CAAT,CAAnB,CAAnB;MACMhU,MAAM,GAAG+C,IAAI,CAACoP,GAAL,CAAS,GAAT,EAAc6B,QAAQ,CAAChU,MAAvB,CAAf;MACMD,GAAG,GAAG4B,MAAM,CAACsS,KAAP,CAAajU,MAAb,CAAZ;;OAEK,IAAIkB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGlB,MAApB,EAA4BkB,CAAC,EAA7B,EAAiC;IAC/BnB,GAAG,CAACmB,CAAD,CAAH,GAAS8S,QAAQ,CAACvS,UAAT,CAAoBP,CAApB,CAAT;;;SAGK+M,QAAQ,CAACI,GAAT,CAAaC,SAAb,CAAuBE,MAAvB,CAA8BzO,GAA9B,CAAP;;;AAGF,SAAS2T,YAAT,CAAsBtQ,IAAtB,EAA4B;SAEvB,CAACA,IAAI,GAAG,IAAR,KAAiB,EAAlB,GACC,CAACA,IAAI,GAAG,MAAR,KAAmB,CADpB,GAEEA,IAAI,IAAI,CAAT,GAAc,MAFf,GAGEA,IAAI,IAAI,EAAT,GAAe,IAJlB;;;AAQF,SAAS4K,iBAAT,CAA2BuG,SAA3B,EAAsC;MAC9BC,SAAS,GAAG,EAAlB;;OACK,IAAItT,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGqT,SAAS,CAACrC,QAA9B,EAAwChR,CAAC,EAAzC,EAA6C;IAC3CsT,SAAS,CAACrU,IAAV,CACGoU,SAAS,CAACpD,KAAV,CAAgBpO,IAAI,CAAC6H,KAAL,CAAW1J,CAAC,GAAG,CAAf,CAAhB,KAAuC,KAAK,IAAKA,CAAC,GAAG,CAAd,CAAxC,GAA8D,IADhE;;;SAIKS,MAAM,CAACC,IAAP,CAAY4S,SAAZ,CAAP;;;AAGF,IAAMJ,gBAAgB,GAAG,CACvB,IADuB,EAEvB,IAFuB,EAGvB,IAHuB,EAIvB,IAJuB,EAKvB,IALuB,EAMvB,IANuB,EAOvB,IAPuB,EAQvB,IARuB,EASvB,IATuB,EAUvB,IAVuB,EAWvB,IAXuB,EAYvB,IAZuB,EAavB,IAbuB,EAcvB,IAduB,EAevB,IAfuB,EAgBvB,IAhBuB,EAiBvB,IAjBuB,EAkBvB,IAlBuB,EAmBvB,IAnBuB,EAoBvB,IApBuB,EAqBvB,IArBuB,EAsBvB,IAtBuB,EAuBvB,IAvBuB,EAwBvB,IAxBuB,EAyBvB,IAzBuB,EA0BvB,IA1BuB,EA2BvB,IA3BuB,EA4BvB,IA5BuB,EA6BvB,IA7BuB,EA8BvB,IA9BuB,EA+BvB,IA/BuB,EAgCvB,IAhCuB,CAAzB;;AC1gBA,IAAM;EAAEvR;IAAWzC,SAAnB;;AAEA,MAAMqU,WAAN,CAAkB;EAChBzV,WAAW,CAAC0V,GAAD,EAAM;SACVA,GAAL,GAAWA,GAAX;SACKC,KAAL,GAAa,EAAb;SACKC,QAAL,GAAgB,KAAhB;SACKC,SAAL,GAAiB,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,EAAU,CAAV,EAAa,CAAb,EAAgB,CAAhB,CAAjB;;;EAGFC,IAAI,CAACC,GAAD,EAAMC,KAAN,EAAaC,OAAb,EAAsB;QACpBA,OAAO,IAAI,IAAf,EAAqB;MACnBA,OAAO,GAAG,CAAV;;;IAEFD,KAAK,GAAG,KAAKN,GAAL,CAASQ,eAAT,CAAyBF,KAAzB,CAAR;;QAEI,KAAKL,KAAL,CAAW3U,MAAX,KAAsB,CAA1B,EAA6B;UACvBgV,KAAK,CAAChV,MAAN,KAAiB,CAArB,EAAwB;aACjBmV,WAAL,GAAmB,WAAnB;OADF,MAEO,IAAIH,KAAK,CAAChV,MAAN,KAAiB,CAArB,EAAwB;aACxBmV,WAAL,GAAmB,YAAnB;OADK,MAEA,IAAIH,KAAK,CAAChV,MAAN,KAAiB,CAArB,EAAwB;aACxBmV,WAAL,GAAmB,YAAnB;OADK,MAEA;cACC,IAAIrW,KAAJ,CAAU,qBAAV,CAAN;;KARJ,MAUO,IACJ,KAAKqW,WAAL,KAAqB,WAArB,IAAoCH,KAAK,CAAChV,MAAN,KAAiB,CAAtD,IACC,KAAKmV,WAAL,KAAqB,YAArB,IAAqCH,KAAK,CAAChV,MAAN,KAAiB,CADvD,IAEC,KAAKmV,WAAL,KAAqB,YAArB,IAAqCH,KAAK,CAAChV,MAAN,KAAiB,CAHlD,EAIL;YACM,IAAIlB,KAAJ,CAAU,kDAAV,CAAN;;;IAGFmW,OAAO,GAAGlS,IAAI,CAACqS,GAAL,CAAS,CAAT,EAAYrS,IAAI,CAACoP,GAAL,CAAS,CAAT,EAAY8C,OAAZ,CAAZ,CAAV;SACKN,KAAL,CAAWxU,IAAX,CAAgB,CAAC4U,GAAD,EAAMC,KAAN,EAAaC,OAAb,CAAhB;WACO,IAAP;;;EAGFI,YAAY,CAACC,GAAD,EAAMC,GAAN,EAAWC,GAAX,EAAgBC,GAAhB,EAAqBC,EAArB,EAAyBC,EAAzB,EAA6B;SAClCd,SAAL,GAAiB,CAACS,GAAD,EAAMC,GAAN,EAAWC,GAAX,EAAgBC,GAAhB,EAAqBC,EAArB,EAAyBC,EAAzB,CAAjB;WACO,IAAP;;;EAGFC,KAAK,CAACC,CAAD,EAAI;QACHC,EAAJ;QACMC,WAAW,GAAG,KAAKpB,KAAL,CAAW3U,MAA/B;;QACI+V,WAAW,KAAK,CAApB,EAAuB;;;;SAGlBnB,QAAL,GAAgB,IAAhB;SACKoB,MAAL,GAAcH,CAAd,CAPO;;QAUD3V,IAAI,GAAG,KAAKyU,KAAL,CAAWoB,WAAW,GAAG,CAAzB,CAAb;;QACI7V,IAAI,CAAC,CAAD,CAAJ,GAAU,CAAd,EAAiB;WACVyU,KAAL,CAAWxU,IAAX,CAAgB,CAAC,CAAD,EAAID,IAAI,CAAC,CAAD,CAAR,EAAaA,IAAI,CAAC,CAAD,CAAjB,CAAhB;;;QAGI+V,MAAM,GAAG,EAAf;QACMC,MAAM,GAAG,EAAf;QACMvB,KAAK,GAAG,EAAd;;SAEK,IAAIzT,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG6U,WAAW,GAAG,CAAlC,EAAqC7U,CAAC,EAAtC,EAA0C;MACxCgV,MAAM,CAAC/V,IAAP,CAAY,CAAZ,EAAe,CAAf;;UACIe,CAAC,GAAG,CAAJ,KAAU6U,WAAd,EAA2B;QACzBE,MAAM,CAAC9V,IAAP,CAAY,KAAKwU,KAAL,CAAWzT,CAAC,GAAG,CAAf,EAAkB,CAAlB,CAAZ;;;MAGF4U,EAAE,GAAG,KAAKpB,GAAL,CAASnM,GAAT,CAAa;QAChB4N,YAAY,EAAE,CADE;QAEhBC,MAAM,EAAE,CAAC,CAAD,EAAI,CAAJ,CAFQ;QAGhBjQ,EAAE,EAAE,KAAKwO,KAAL,CAAWzT,CAAC,GAAG,CAAf,EAAkB,CAAlB,CAHY;QAIhBkF,EAAE,EAAE,KAAKuO,KAAL,CAAWzT,CAAC,GAAG,CAAf,EAAkB,CAAlB,CAJY;QAKhBmV,CAAC,EAAE;OALA,CAAL;MAQA1B,KAAK,CAACxU,IAAN,CAAW2V,EAAX;MACAA,EAAE,CAAC3U,GAAH;KAlCK;;;QAsCH4U,WAAW,KAAK,CAApB,EAAuB;MACrBD,EAAE,GAAGnB,KAAK,CAAC,CAAD,CAAV;KADF,MAEO;MACLmB,EAAE,GAAG,KAAKpB,GAAL,CAASnM,GAAT,CAAa;QAChB4N,YAAY,EAAE,CADE;;QAEhBC,MAAM,EAAE,CAAC,CAAD,EAAI,CAAJ,CAFQ;QAGhBE,SAAS,EAAE3B,KAHK;QAIhB4B,MAAM,EAAEN,MAJQ;QAKhBO,MAAM,EAAEN;OALL,CAAL;MAQAJ,EAAE,CAAC3U,GAAH;;;SAGGgC,EAAL,eAAe,EAAE,KAAKuR,GAAL,CAAS+B,UAA1B;QAEMC,MAAM,GAAG,KAAKA,MAAL,CAAYZ,EAAZ,CAAf;IACAY,MAAM,CAACvV,GAAP;QAEMwV,OAAO,GAAG,KAAKjC,GAAL,CAASnM,GAAT,CAAa;MAC3BI,IAAI,EAAE,SADqB;MAE3BiO,WAAW,EAAE,CAFc;MAG3BC,OAAO,EAAEH,MAHkB;MAI3BI,MAAM,EAAE,KAAKd,MAAL,CAAYtT,GAAZ,CAAgBG,MAAhB;KAJM,CAAhB;IAOA8T,OAAO,CAACxV,GAAR;;QAEI,KAAKwT,KAAL,CAAWzH,IAAX,CAAgB4H,IAAI,IAAIA,IAAI,CAAC,CAAD,CAAJ,GAAU,CAAlC,CAAJ,EAA0C;UACpCiC,IAAI,GAAG,KAAKC,eAAL,EAAX;MACAD,IAAI,CAAC5B,WAAL,GAAmB,YAAnB;;WAEK,IAAIL,IAAT,IAAiB,KAAKH,KAAtB,EAA6B;QAC3BoC,IAAI,CAACjC,IAAL,CAAUA,IAAI,CAAC,CAAD,CAAd,EAAmB,CAACA,IAAI,CAAC,CAAD,CAAL,CAAnB;;;MAGFiC,IAAI,GAAGA,IAAI,CAACnB,KAAL,CAAW,KAAKI,MAAhB,CAAP;UAEMiB,QAAQ,GAAG,CAAC,CAAD,EAAI,CAAJ,EAAO,KAAKvC,GAAL,CAASwC,IAAT,CAAc9O,KAArB,EAA4B,KAAKsM,GAAL,CAASwC,IAAT,CAAc7O,MAA1C,CAAjB;UAEM8O,IAAI,GAAG,KAAKzC,GAAL,CAASnM,GAAT,CAAa;QACxBI,IAAI,EAAE,SADkB;QAExByO,OAAO,EAAE,MAFe;QAGxBC,QAAQ,EAAE,CAHc;QAIxBC,IAAI,EAAEL,QAJkB;QAKxBM,KAAK,EAAE;UACL5O,IAAI,EAAE,OADD;UAEL6O,CAAC,EAAE,cAFE;UAGLC,EAAE,EAAE;SARkB;QAUxBxO,SAAS,EAAE;UACTR,OAAO,EAAE,CAAC,KAAD,EAAQ,MAAR,EAAgB,QAAhB,EAA0B,QAA1B,EAAoC,QAApC,CADA;UAETiB,OAAO,EAAE;YACPgO,GAAG,EAAEX;;;OAbE,CAAb;MAkBAI,IAAI,CAACzT,KAAL,CAAW,sBAAX;MACAyT,IAAI,CAAChW,GAAL,WAAY8V,QAAQ,CAACzW,IAAT,CAAc,GAAd,CAAZ;UAEMmX,MAAM,GAAG,KAAKjD,GAAL,CAASnM,GAAT,CAAa;QAC1BI,IAAI,EAAE,WADoB;QAE1BiP,KAAK,EAAE;UACLjP,IAAI,EAAE,MADD;UAEL6O,CAAC,EAAE,YAFE;UAGLK,CAAC,EAAEV;;OALQ,CAAf;MASAQ,MAAM,CAACxW,GAAP;UAEM2W,cAAc,GAAG,KAAKpD,GAAL,CAASnM,GAAT,CAAa;QAClCI,IAAI,EAAE,SAD4B;QAElCiO,WAAW,EAAE,CAFqB;QAGlCmB,SAAS,EAAE,CAHuB;QAIlCC,UAAU,EAAE,CAJsB;QAKlCV,IAAI,EAAEL,QAL4B;QAMlCgB,KAAK,EAAEhB,QAAQ,CAAC,CAAD,CANmB;QAOlCiB,KAAK,EAAEjB,QAAQ,CAAC,CAAD,CAPmB;QAQlChO,SAAS,EAAE;UACTR,OAAO,EAAE,CAAC,KAAD,EAAQ,MAAR,EAAgB,QAAhB,EAA0B,QAA1B,EAAoC,QAApC,CADA;UAETiB,OAAO,EAAE;YACPgO,GAAG,EAAEf;WAHE;UAKTnN,SAAS,EAAE;YACT2O,GAAG,EAAER;;;OAdY,CAAvB;MAmBAG,cAAc,CAACpU,KAAf,CAAqB,8BAArB;MACAoU,cAAc,CAAC3W,GAAf,WAAsB8V,QAAQ,CAACzW,IAAT,CAAc,GAAd,CAAtB;WAEKkU,GAAL,CAASwC,IAAT,CAAczN,QAAd,CAAuB,KAAKtG,EAA5B,IAAkC2U,cAAlC;KAlEF,MAmEO;WACApD,GAAL,CAASwC,IAAT,CAAczN,QAAd,CAAuB,KAAKtG,EAA5B,IAAkCwT,OAAlC;;;WAGKA,OAAP;;;EAGF7J,KAAK,CAACsL,MAAD,EAAS;;QAEN,CAACC,EAAD,EAAKC,EAAL,EAASC,EAAT,EAAaC,EAAb,EAAiBC,EAAjB,EAAqBC,EAArB,IAA2B,KAAKhE,GAAL,CAASiE,IAA1C;QACM,CAACrD,GAAD,EAAMC,GAAN,EAAWC,GAAX,EAAgBC,GAAhB,EAAqBC,EAArB,EAAyBC,EAAzB,IAA+B,KAAKd,SAA1C;QACMgB,CAAC,GAAG,CACRwC,EAAE,GAAG/C,GAAL,GAAWiD,EAAE,GAAGhD,GADR,EAER+C,EAAE,GAAGhD,GAAL,GAAWkD,EAAE,GAAGjD,GAFR,EAGR8C,EAAE,GAAG7C,GAAL,GAAW+C,EAAE,GAAG9C,GAHR,EAIR6C,EAAE,GAAG9C,GAAL,GAAWgD,EAAE,GAAG/C,GAJR,EAKR4C,EAAE,GAAG3C,EAAL,GAAU6C,EAAE,GAAG5C,EAAf,GAAoB8C,EALZ,EAMRH,EAAE,GAAG5C,EAAL,GAAU8C,EAAE,GAAG7C,EAAf,GAAoB+C,EANZ,CAAV;;QASI,CAAC,KAAK9D,QAAN,IAAkBiB,CAAC,CAACrV,IAAF,CAAO,GAAP,MAAgB,KAAKwV,MAAL,CAAYxV,IAAZ,CAAiB,GAAjB,CAAtC,EAA6D;WACtDoV,KAAL,CAAWC,CAAX;;;SAEGnB,GAAL,CAASkE,cAAT,CAAwB,SAAxB,EAAmCR,MAAnC;;QACMS,EAAE,GAAGT,MAAM,GAAG,KAAH,GAAW,KAA5B;WACO,KAAK1D,GAAL,CAASoE,UAAT,YAAwB,KAAK3V,EAA7B,cAAmC0V,EAAnC,EAAP;;;;;AAIJ,MAAME,iBAAN,SAAgCtE,WAAhC,CAA4C;EAC1CzV,WAAW,CAAC0V,GAAD,EAAMsE,EAAN,EAAUC,EAAV,EAAcC,EAAd,EAAkBC,EAAlB,EAAsB;UACzBzE,GAAN;SACKsE,EAAL,GAAUA,EAAV;SACKC,EAAL,GAAUA,EAAV;SACKC,EAAL,GAAUA,EAAV;SACKC,EAAL,GAAUA,EAAV;;;EAGFzC,MAAM,CAACZ,EAAD,EAAK;WACF,KAAKpB,GAAL,CAASnM,GAAT,CAAa;MAClB6Q,WAAW,EAAE,CADK;MAElBxP,UAAU,EAAE,KAAKuL,WAFC;MAGlBkE,MAAM,EAAE,CAAC,KAAKL,EAAN,EAAU,KAAKC,EAAf,EAAmB,KAAKC,EAAxB,EAA4B,KAAKC,EAAjC,CAHU;MAIlBG,QAAQ,EAAExD,EAJQ;MAKlByD,MAAM,EAAE,CAAC,IAAD,EAAO,IAAP;KALH,CAAP;;;EASFvC,eAAe,GAAG;WACT,IAAI+B,iBAAJ,CAAsB,KAAKrE,GAA3B,EAAgC,KAAKsE,EAArC,EAAyC,KAAKC,EAA9C,EAAkD,KAAKC,EAAvD,EAA2D,KAAKC,EAAhE,CAAP;;;;;AAIJ,MAAMK,iBAAN,SAAgC/E,WAAhC,CAA4C;EAC1CzV,WAAW,CAAC0V,GAAD,EAAMsE,EAAN,EAAUC,EAAV,EAAcQ,EAAd,EAAkBP,EAAlB,EAAsBC,EAAtB,EAA0BO,EAA1B,EAA8B;UACjChF,GAAN;SACKA,GAAL,GAAWA,GAAX;SACKsE,EAAL,GAAUA,EAAV;SACKC,EAAL,GAAUA,EAAV;SACKQ,EAAL,GAAUA,EAAV;SACKP,EAAL,GAAUA,EAAV;SACKC,EAAL,GAAUA,EAAV;SACKO,EAAL,GAAUA,EAAV;;;EAGFhD,MAAM,CAACZ,EAAD,EAAK;WACF,KAAKpB,GAAL,CAASnM,GAAT,CAAa;MAClB6Q,WAAW,EAAE,CADK;MAElBxP,UAAU,EAAE,KAAKuL,WAFC;MAGlBkE,MAAM,EAAE,CAAC,KAAKL,EAAN,EAAU,KAAKC,EAAf,EAAmB,KAAKQ,EAAxB,EAA4B,KAAKP,EAAjC,EAAqC,KAAKC,EAA1C,EAA8C,KAAKO,EAAnD,CAHU;MAIlBJ,QAAQ,EAAExD,EAJQ;MAKlByD,MAAM,EAAE,CAAC,IAAD,EAAO,IAAP;KALH,CAAP;;;EASFvC,eAAe,GAAG;WACT,IAAIwC,iBAAJ,CACL,KAAK9E,GADA,EAEL,KAAKsE,EAFA,EAGL,KAAKC,EAHA,EAIL,KAAKQ,EAJA,EAKL,KAAKP,EALA,EAML,KAAKC,EANA,EAOL,KAAKO,EAPA,CAAP;;;;;AAYJ,eAAe;EAAEjF,WAAF;EAAesE,iBAAf;EAAkCS;CAAjD;;AC3QA;;;AAIA,IAAMG,qBAAqB,GAAG,CAAC,YAAD,EAAe,WAAf,CAA9B;;AAEA,MAAMC,gBAAN,CAAuB;EACrB5a,WAAW,CAAC0V,GAAD,EAAMmF,IAAN,EAAYC,KAAZ,EAAmBC,KAAnB,EAA0BC,MAA1B,EAAkC;SACtCtF,GAAL,GAAWA,GAAX;SACKmF,IAAL,GAAYA,IAAZ;SACKC,KAAL,GAAaA,KAAb;SACKC,KAAL,GAAaA,KAAb;SACKC,MAAL,GAAcA,MAAd;;;EAGFC,aAAa,GAAG;;;QAGRzR,SAAS,GAAG,KAAKkM,GAAL,CAASnM,GAAT,EAAlB;IACAC,SAAS,CAACrH,GAAV,GAJc;;;QAOR,CAACkX,EAAD,EAAKC,EAAL,EAASC,EAAT,EAAaC,EAAb,EAAiBC,EAAjB,EAAqBC,EAArB,IAA2B,KAAKhE,GAAL,CAASiE,IAA1C;QACM,CAACrD,GAAD,EAAMC,GAAN,EAAWC,GAAX,EAAgBC,GAAhB,EAAqBC,EAArB,EAAyBC,EAAzB,IAA+B,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,EAAU,CAAV,EAAa,CAAb,EAAgB,CAAhB,CAArC;QACME,CAAC,GAAG,CACRwC,EAAE,GAAG/C,GAAL,GAAWiD,EAAE,GAAGhD,GADR,EAER+C,EAAE,GAAGhD,GAAL,GAAWkD,EAAE,GAAGjD,GAFR,EAGR8C,EAAE,GAAG7C,GAAL,GAAW+C,EAAE,GAAG9C,GAHR,EAIR6C,EAAE,GAAG9C,GAAL,GAAWgD,EAAE,GAAG/C,GAJR,EAKR4C,EAAE,GAAG3C,EAAL,GAAU6C,EAAE,GAAG5C,EAAf,GAAoB8C,EALZ,EAMRH,EAAE,GAAG5C,EAAL,GAAU8C,EAAE,GAAG7C,EAAf,GAAoB+C,EANZ,CAAV;QAQM/B,OAAO,GAAG,KAAKjC,GAAL,CAASnM,GAAT,CAAa;MAC3BI,IAAI,EAAE,SADqB;MAE3BiO,WAAW,EAAE,CAFc;;MAG3BmB,SAAS,EAAE,CAHgB;;MAI3BC,UAAU,EAAE,CAJe;;MAK3BV,IAAI,EAAE,KAAKuC,IALgB;MAM3B5B,KAAK,EAAE,KAAK6B,KANe;MAO3B5B,KAAK,EAAE,KAAK6B,KAPe;MAQ3BjD,MAAM,EAAEjB,CAAC,CAACnT,GAAF,CAAMuM,CAAC,IAAI,CAACA,CAAC,CAACiL,OAAF,CAAU,CAAV,CAAZ,CARmB;MAS3BjR,SAAS,EAAET;KATG,CAAhB;IAWAmO,OAAO,CAACxV,GAAR,CAAY,KAAK6Y,MAAjB;WACOrD,OAAP;;;EAGFwD,uBAAuB,GAAG;;;IAGxBR,qBAAqB,CAACS,OAAtB,CAA8BC,MAAM,IAAI;UAChCC,IAAI,GAAG,KAAKC,sBAAL,CAA4BF,MAA5B,CAAb;UAEI,KAAK3F,GAAL,CAASwC,IAAT,CAAcvN,WAAd,CAA0B2Q,IAA1B,CAAJ,EAAqC;UAC/BE,EAAE,GAAG,KAAK9F,GAAL,CAASnM,GAAT,CAAa,CAAC,SAAD,EAAY8R,MAAZ,CAAb,CAAX;MACAG,EAAE,CAACrZ,GAAH;WACKuT,GAAL,CAASwC,IAAT,CAAcvN,WAAd,CAA0B2Q,IAA1B,IAAkCE,EAAlC;KANF;;;EAUFD,sBAAsB,CAACE,oBAAD,EAAuB;wBAC9BA,oBAAb;;;EAGF7E,KAAK,GAAG;QACF,CAAC,KAAKzS,EAAV,EAAc;WACPuR,GAAL,CAASgG,aAAT,GAAyB,KAAKhG,GAAL,CAASgG,aAAT,GAAyB,CAAlD;WACKvX,EAAL,GAAU,MAAM,KAAKuR,GAAL,CAASgG,aAAzB;WACK/D,OAAL,GAAe,KAAKsD,aAAL,EAAf;KAJI;;;QAQF,CAAC,KAAKvF,GAAL,CAASwC,IAAT,CAAczN,QAAd,CAAuB,KAAKtG,EAA5B,CAAL,EAAsC;WAC/BuR,GAAL,CAASwC,IAAT,CAAczN,QAAd,CAAuB,KAAKtG,EAA5B,IAAkC,KAAKwT,OAAvC;;;;EAIJ7J,KAAK,CAACsL,MAAD,EAASuC,YAAT,EAAuB;;SAErBR,uBAAL;SACKvE,KAAL;;QAEMgF,eAAe,GAAG,KAAKlG,GAAL,CAASQ,eAAT,CAAyByF,YAAzB,CAAxB;;QACI,CAACC,eAAL,EACE,MAAM9b,KAAK,0CAAmC6b,YAAnC,OAAX,CAPwB;;QAUpBL,IAAI,GAAG,KAAKC,sBAAL,CACX,KAAK7F,GAAL,CAASmG,cAAT,CAAwBD,eAAxB,CADW,CAAb;;SAGKlG,GAAL,CAASkE,cAAT,CAAwB0B,IAAxB,EAA8BlC,MAA9B,EAb0B;;;QAgBpBS,EAAE,GAAGT,MAAM,GAAG,KAAH,GAAW,KAA5B;WACO,KAAK1D,GAAL,CAASoE,UAAT,WACF8B,eAAe,CAACpa,IAAhB,CAAqB,GAArB,CADE,eAC4B,KAAK2C,EADjC,cACuC0V,EADvC,EAAP;;;;;AAMJ,cAAe;EAAEe;CAAjB;;ACjGA,IAAM;eAAEnF,aAAF;qBAAesE,mBAAf;qBAAkCS;IAAsBsB,QAA9D;AACA,IAAM;oBAAElB;IAAqBjD,OAA7B;AAEA,iBAAe;EACboE,SAAS,GAAG;;SAELC,gBAAL,GAAwB,EAAxB;SACKC,aAAL,GAAqB,CAArB;SACKP,aAAL,GAAqB,CAArB;WACQ,KAAKjE,UAAL,GAAkB,CAA1B;GANW;;EASbvB,eAAe,CAACF,KAAD,EAAQ;QACjB,OAAOA,KAAP,KAAiB,QAArB,EAA+B;UACzBA,KAAK,CAACkG,MAAN,CAAa,CAAb,MAAoB,GAAxB,EAA6B;YACvBlG,KAAK,CAAChV,MAAN,KAAiB,CAArB,EAAwB;UACtBgV,KAAK,GAAGA,KAAK,CAAClT,OAAN,CACN,kCADM,EAEN,eAFM,CAAR;;;YAKIqZ,GAAG,GAAGC,QAAQ,CAACpG,KAAK,CAACpU,KAAN,CAAY,CAAZ,CAAD,EAAiB,EAAjB,CAApB;QACAoU,KAAK,GAAG,CAACmG,GAAG,IAAI,EAAR,EAAaA,GAAG,IAAI,CAAR,GAAa,IAAzB,EAA+BA,GAAG,GAAG,IAArC,CAAR;OARF,MASO,IAAIE,WAAW,CAACrG,KAAD,CAAf,EAAwB;QAC7BA,KAAK,GAAGqG,WAAW,CAACrG,KAAD,CAAnB;;;;QAIArU,KAAK,CAAC6B,OAAN,CAAcwS,KAAd,CAAJ,EAA0B;;UAEpBA,KAAK,CAAChV,MAAN,KAAiB,CAArB,EAAwB;QACtBgV,KAAK,GAAGA,KAAK,CAACtS,GAAN,CAAU4Y,IAAI,IAAIA,IAAI,GAAG,GAAzB,CAAR,CADsB;OAAxB,MAGO,IAAItG,KAAK,CAAChV,MAAN,KAAiB,CAArB,EAAwB;QAC7BgV,KAAK,GAAGA,KAAK,CAACtS,GAAN,CAAU4Y,IAAI,IAAIA,IAAI,GAAG,GAAzB,CAAR;;;aAEKtG,KAAP;;;WAGK,IAAP;GApCW;;EAuCbuG,SAAS,CAACvG,KAAD,EAAQoD,MAAR,EAAgB;QACnBpD,KAAK,YAAYP,aAArB,EAAkC;MAChCO,KAAK,CAAClI,KAAN,CAAYsL,MAAZ;aACO,IAAP,CAFgC;KAAlC,MAIO,IAAIzX,KAAK,CAAC6B,OAAN,CAAcwS,KAAd,KAAwBA,KAAK,CAAC,CAAD,CAAL,YAAoB4E,kBAAhD,EAAkE;MACvE5E,KAAK,CAAC,CAAD,CAAL,CAASlI,KAAT,CAAesL,MAAf,EAAuBpD,KAAK,CAAC,CAAD,CAA5B;aACO,IAAP;KAPqB;;;WAUhB,KAAKwG,aAAL,CAAmBxG,KAAnB,EAA0BoD,MAA1B,CAAP;GAjDW;;EAoDboD,aAAa,CAACxG,KAAD,EAAQoD,MAAR,EAAgB;IAC3BpD,KAAK,GAAG,KAAKE,eAAL,CAAqBF,KAArB,CAAR;;QACI,CAACA,KAAL,EAAY;aACH,KAAP;;;QAGI6D,EAAE,GAAGT,MAAM,GAAG,KAAH,GAAW,KAA5B;;QACMqD,KAAK,GAAG,KAAKZ,cAAL,CAAoB7F,KAApB,CAAd;;SACK4D,cAAL,CAAoB6C,KAApB,EAA2BrD,MAA3B;;IAEApD,KAAK,GAAGA,KAAK,CAACxU,IAAN,CAAW,GAAX,CAAR;SACKsY,UAAL,WAAmB9D,KAAnB,cAA4B6D,EAA5B;WAEO,IAAP;GAjEW;;EAoEbD,cAAc,CAAC6C,KAAD,EAAQrD,MAAR,EAAgB;QACtBS,EAAE,GAAGT,MAAM,GAAG,IAAH,GAAU,IAA3B;WACO,KAAKU,UAAL,YAAoB2C,KAApB,cAA6B5C,EAA7B,EAAP;GAtEW;;EAyEbgC,cAAc,CAAC7F,KAAD,EAAQ;WACbA,KAAK,CAAChV,MAAN,KAAiB,CAAjB,GAAqB,YAArB,GAAoC,WAA3C;GA1EW;;EA6Eb0b,SAAS,CAAC1G,KAAD,EAAQC,OAAR,EAAiB;QAClB0G,GAAG,GAAG,KAAKJ,SAAL,CAAevG,KAAf,EAAsB,KAAtB,CAAZ;;QACI2G,GAAJ,EAAS;WACFC,WAAL,CAAiB3G,OAAjB;KAHsB;;;;SAQnB4G,UAAL,GAAkB,CAAC7G,KAAD,EAAQC,OAAR,CAAlB;WACO,IAAP;GAtFW;;EAyFb6G,WAAW,CAAC9G,KAAD,EAAQC,OAAR,EAAiB;QACpB0G,GAAG,GAAG,KAAKJ,SAAL,CAAevG,KAAf,EAAsB,IAAtB,CAAZ;;QACI2G,GAAJ,EAAS;WACFI,aAAL,CAAmB9G,OAAnB;;;WAEK,IAAP;GA9FW;;EAiGbA,OAAO,CAACA,OAAD,EAAU;SACV+G,UAAL,CAAgB/G,OAAhB,EAAyBA,OAAzB;;WACO,IAAP;GAnGW;;EAsGb2G,WAAW,CAAC3G,OAAD,EAAU;SACd+G,UAAL,CAAgB/G,OAAhB,EAAyB,IAAzB;;WACO,IAAP;GAxGW;;EA2Gb8G,aAAa,CAAC9G,OAAD,EAAU;SAChB+G,UAAL,CAAgB,IAAhB,EAAsB/G,OAAtB;;WACO,IAAP;GA7GW;;EAgHb+G,UAAU,CAACJ,WAAD,EAAcG,aAAd,EAA6B;QACjCrT,UAAJ,EAAgBuT,IAAhB;;QACIL,WAAW,IAAI,IAAf,IAAuBG,aAAa,IAAI,IAA5C,EAAkD;;;;QAI9CH,WAAW,IAAI,IAAnB,EAAyB;MACvBA,WAAW,GAAG7Y,IAAI,CAACqS,GAAL,CAAS,CAAT,EAAYrS,IAAI,CAACoP,GAAL,CAAS,CAAT,EAAYyJ,WAAZ,CAAZ,CAAd;;;QAEEG,aAAa,IAAI,IAArB,EAA2B;MACzBA,aAAa,GAAGhZ,IAAI,CAACqS,GAAL,CAAS,CAAT,EAAYrS,IAAI,CAACoP,GAAL,CAAS,CAAT,EAAY4J,aAAZ,CAAZ,CAAhB;;;QAEI1c,GAAG,aAAMuc,WAAN,cAAqBG,aAArB,CAAT;;QAEI,KAAKf,gBAAL,CAAsB3b,GAAtB,CAAJ,EAAgC;OAC7BqJ,UAAD,EAAauT,IAAb,IAAqB,KAAKjB,gBAAL,CAAsB3b,GAAtB,CAArB;KADF,MAEO;MACLqJ,UAAU,GAAG;QAAEC,IAAI,EAAE;OAArB;;UAEIiT,WAAW,IAAI,IAAnB,EAAyB;QACvBlT,UAAU,CAACwT,EAAX,GAAgBN,WAAhB;;;UAEEG,aAAa,IAAI,IAArB,EAA2B;QACzBrT,UAAU,CAACyT,EAAX,GAAgBJ,aAAhB;;;MAGFrT,UAAU,GAAG,KAAKH,GAAL,CAASG,UAAT,CAAb;MACAA,UAAU,CAACvH,GAAX;UACMgC,EAAE,GAAG,EAAE,KAAK8X,aAAlB;MACAgB,IAAI,eAAQ9Y,EAAR,CAAJ;WACK6X,gBAAL,CAAsB3b,GAAtB,IAA6B,CAACqJ,UAAD,EAAauT,IAAb,CAA7B;;;SAGG/E,IAAL,CAAU3N,WAAV,CAAsB0S,IAAtB,IAA8BvT,UAA9B;WACO,KAAKoQ,UAAL,YAAoBmD,IAApB,SAAP;GAlJW;;EAqJbG,cAAc,CAACpD,EAAD,EAAKC,EAAL,EAASC,EAAT,EAAaC,EAAb,EAAiB;WACtB,IAAIJ,mBAAJ,CAAsB,IAAtB,EAA4BC,EAA5B,EAAgCC,EAAhC,EAAoCC,EAApC,EAAwCC,EAAxC,CAAP;GAtJW;;EAyJbkD,cAAc,CAACrD,EAAD,EAAKC,EAAL,EAASQ,EAAT,EAAaP,EAAb,EAAiBC,EAAjB,EAAqBO,EAArB,EAAyB;WAC9B,IAAIF,mBAAJ,CAAsB,IAAtB,EAA4BR,EAA5B,EAAgCC,EAAhC,EAAoCQ,EAApC,EAAwCP,EAAxC,EAA4CC,EAA5C,EAAgDO,EAAhD,CAAP;GA1JW;;EA6Jb/C,OAAO,CAAC2F,IAAD,EAAOxC,KAAP,EAAcC,KAAd,EAAqBC,MAArB,EAA6B;WAC3B,IAAIJ,kBAAJ,CAAqB,IAArB,EAA2B0C,IAA3B,EAAiCxC,KAAjC,EAAwCC,KAAxC,EAA+CC,MAA/C,CAAP;;;CA9JJ;AAkKA,IAAIqB,WAAW,GAAG;EAChBkB,SAAS,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CADK;EAEhBC,YAAY,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAFE;EAGhBC,IAAI,EAAE,CAAC,CAAD,EAAI,GAAJ,EAAS,GAAT,CAHU;EAIhBC,UAAU,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAJI;EAKhBC,KAAK,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CALS;EAMhBC,KAAK,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CANS;EAOhBC,MAAM,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAPQ;EAQhBC,KAAK,EAAE,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,CARS;EAShBC,cAAc,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CATA;EAUhBC,IAAI,EAAE,CAAC,CAAD,EAAI,CAAJ,EAAO,GAAP,CAVU;EAWhBC,UAAU,EAAE,CAAC,GAAD,EAAM,EAAN,EAAU,GAAV,CAXI;EAYhBC,KAAK,EAAE,CAAC,GAAD,EAAM,EAAN,EAAU,EAAV,CAZS;EAahBC,SAAS,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAbK;EAchBC,SAAS,EAAE,CAAC,EAAD,EAAK,GAAL,EAAU,GAAV,CAdK;EAehBC,UAAU,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,CAAX,CAfI;EAgBhBC,SAAS,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,EAAX,CAhBK;EAiBhBC,KAAK,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,EAAX,CAjBS;EAkBhBC,cAAc,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAlBA;EAmBhBC,QAAQ,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAnBM;EAoBhBC,OAAO,EAAE,CAAC,GAAD,EAAM,EAAN,EAAU,EAAV,CApBO;EAqBhBC,IAAI,EAAE,CAAC,CAAD,EAAI,GAAJ,EAAS,GAAT,CArBU;EAsBhBC,QAAQ,EAAE,CAAC,CAAD,EAAI,CAAJ,EAAO,GAAP,CAtBM;EAuBhBC,QAAQ,EAAE,CAAC,CAAD,EAAI,GAAJ,EAAS,GAAT,CAvBM;EAwBhBC,aAAa,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,EAAX,CAxBC;EAyBhBC,QAAQ,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAzBM;EA0BhBC,SAAS,EAAE,CAAC,CAAD,EAAI,GAAJ,EAAS,CAAT,CA1BK;EA2BhBC,QAAQ,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA3BM;EA4BhBC,SAAS,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA5BK;EA6BhBC,WAAW,EAAE,CAAC,GAAD,EAAM,CAAN,EAAS,GAAT,CA7BG;EA8BhBC,cAAc,EAAE,CAAC,EAAD,EAAK,GAAL,EAAU,EAAV,CA9BA;EA+BhBC,UAAU,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,CAAX,CA/BI;EAgChBC,UAAU,EAAE,CAAC,GAAD,EAAM,EAAN,EAAU,GAAV,CAhCI;EAiChBC,OAAO,EAAE,CAAC,GAAD,EAAM,CAAN,EAAS,CAAT,CAjCO;EAkChBC,UAAU,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAlCI;EAmChBC,YAAY,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAnCE;EAoChBC,aAAa,EAAE,CAAC,EAAD,EAAK,EAAL,EAAS,GAAT,CApCC;EAqChBC,aAAa,EAAE,CAAC,EAAD,EAAK,EAAL,EAAS,EAAT,CArCC;EAsChBC,aAAa,EAAE,CAAC,EAAD,EAAK,EAAL,EAAS,EAAT,CAtCC;EAuChBC,aAAa,EAAE,CAAC,CAAD,EAAI,GAAJ,EAAS,GAAT,CAvCC;EAwChBC,UAAU,EAAE,CAAC,GAAD,EAAM,CAAN,EAAS,GAAT,CAxCI;EAyChBC,QAAQ,EAAE,CAAC,GAAD,EAAM,EAAN,EAAU,GAAV,CAzCM;EA0ChBC,WAAW,EAAE,CAAC,CAAD,EAAI,GAAJ,EAAS,GAAT,CA1CG;EA2ChBC,OAAO,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA3CO;EA4ChBC,OAAO,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA5CO;EA6ChBC,UAAU,EAAE,CAAC,EAAD,EAAK,GAAL,EAAU,GAAV,CA7CI;EA8ChBC,SAAS,EAAE,CAAC,GAAD,EAAM,EAAN,EAAU,EAAV,CA9CK;EA+ChBC,WAAW,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA/CG;EAgDhBC,WAAW,EAAE,CAAC,EAAD,EAAK,GAAL,EAAU,EAAV,CAhDG;EAiDhBC,OAAO,EAAE,CAAC,GAAD,EAAM,CAAN,EAAS,GAAT,CAjDO;EAkDhBC,SAAS,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAlDK;EAmDhBC,UAAU,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAnDI;EAoDhBC,IAAI,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,CAAX,CApDU;EAqDhBC,SAAS,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,EAAX,CArDK;EAsDhBC,IAAI,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAtDU;EAuDhBC,IAAI,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAvDU;EAwDhBC,KAAK,EAAE,CAAC,CAAD,EAAI,GAAJ,EAAS,CAAT,CAxDS;EAyDhBC,WAAW,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,EAAX,CAzDG;EA0DhBC,QAAQ,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA1DM;EA2DhBC,OAAO,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA3DO;EA4DhBC,SAAS,EAAE,CAAC,GAAD,EAAM,EAAN,EAAU,EAAV,CA5DK;EA6DhBC,MAAM,EAAE,CAAC,EAAD,EAAK,CAAL,EAAQ,GAAR,CA7DQ;EA8DhBC,KAAK,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA9DS;EA+DhBC,KAAK,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA/DS;EAgEhBC,QAAQ,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAhEM;EAiEhBC,aAAa,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAjEC;EAkEhBC,SAAS,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,CAAX,CAlEK;EAmEhBC,YAAY,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAnEE;EAoEhBC,SAAS,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CApEK;EAqEhBC,UAAU,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CArEI;EAsEhBC,SAAS,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAtEK;EAuEhBC,oBAAoB,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAvEN;EAwEhBC,SAAS,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAxEK;EAyEhBC,UAAU,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAzEI;EA0EhBC,SAAS,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA1EK;EA2EhBC,SAAS,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA3EK;EA4EhBC,WAAW,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA5EG;EA6EhBC,aAAa,EAAE,CAAC,EAAD,EAAK,GAAL,EAAU,GAAV,CA7EC;EA8EhBC,YAAY,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA9EE;EA+EhBC,cAAc,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA/EA;EAgFhBC,cAAc,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAhFA;EAiFhBC,cAAc,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAjFA;EAkFhBC,WAAW,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAlFG;EAmFhBC,IAAI,EAAE,CAAC,CAAD,EAAI,GAAJ,EAAS,CAAT,CAnFU;EAoFhBC,SAAS,EAAE,CAAC,EAAD,EAAK,GAAL,EAAU,EAAV,CApFK;EAqFhBC,KAAK,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CArFS;EAsFhBC,OAAO,EAAE,CAAC,GAAD,EAAM,CAAN,EAAS,GAAT,CAtFO;EAuFhBC,MAAM,EAAE,CAAC,GAAD,EAAM,CAAN,EAAS,CAAT,CAvFQ;EAwFhBC,gBAAgB,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAxFF;EAyFhBC,UAAU,EAAE,CAAC,CAAD,EAAI,CAAJ,EAAO,GAAP,CAzFI;EA0FhBC,YAAY,EAAE,CAAC,GAAD,EAAM,EAAN,EAAU,GAAV,CA1FE;EA2FhBC,YAAY,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA3FE;EA4FhBC,cAAc,EAAE,CAAC,EAAD,EAAK,GAAL,EAAU,GAAV,CA5FA;EA6FhBC,eAAe,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA7FD;EA8FhBC,iBAAiB,EAAE,CAAC,CAAD,EAAI,GAAJ,EAAS,GAAT,CA9FH;EA+FhBC,eAAe,EAAE,CAAC,EAAD,EAAK,GAAL,EAAU,GAAV,CA/FD;EAgGhBC,eAAe,EAAE,CAAC,GAAD,EAAM,EAAN,EAAU,GAAV,CAhGD;EAiGhBC,YAAY,EAAE,CAAC,EAAD,EAAK,EAAL,EAAS,GAAT,CAjGE;EAkGhBC,SAAS,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAlGK;EAmGhBC,SAAS,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAnGK;EAoGhBC,QAAQ,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CApGM;EAqGhBC,WAAW,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CArGG;EAsGhBC,IAAI,EAAE,CAAC,CAAD,EAAI,CAAJ,EAAO,GAAP,CAtGU;EAuGhBC,OAAO,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAvGO;EAwGhBC,KAAK,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,CAAX,CAxGS;EAyGhBC,SAAS,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,EAAX,CAzGK;EA0GhBC,MAAM,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,CAAX,CA1GQ;EA2GhBC,SAAS,EAAE,CAAC,GAAD,EAAM,EAAN,EAAU,CAAV,CA3GK;EA4GhBC,MAAM,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA5GQ;EA6GhBC,aAAa,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA7GC;EA8GhBC,SAAS,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA9GK;EA+GhBC,aAAa,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA/GC;EAgHhBC,aAAa,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAhHC;EAiHhBC,UAAU,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAjHI;EAkHhBC,SAAS,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAlHK;EAmHhBC,IAAI,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,EAAX,CAnHU;EAoHhBC,IAAI,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CApHU;EAqHhBC,IAAI,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CArHU;EAsHhBC,UAAU,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAtHI;EAuHhBC,MAAM,EAAE,CAAC,GAAD,EAAM,CAAN,EAAS,GAAT,CAvHQ;EAwHhBC,GAAG,EAAE,CAAC,GAAD,EAAM,CAAN,EAAS,CAAT,CAxHW;EAyHhBC,SAAS,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAzHK;EA0HhBC,SAAS,EAAE,CAAC,EAAD,EAAK,GAAL,EAAU,GAAV,CA1HK;EA2HhBC,WAAW,EAAE,CAAC,GAAD,EAAM,EAAN,EAAU,EAAV,CA3HG;EA4HhBC,MAAM,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA5HQ;EA6HhBC,UAAU,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,EAAX,CA7HI;EA8HhBC,QAAQ,EAAE,CAAC,EAAD,EAAK,GAAL,EAAU,EAAV,CA9HM;EA+HhBC,QAAQ,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA/HM;EAgIhBC,MAAM,EAAE,CAAC,GAAD,EAAM,EAAN,EAAU,EAAV,CAhIQ;EAiIhBC,MAAM,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAjIQ;EAkIhBC,OAAO,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAlIO;EAmIhBC,SAAS,EAAE,CAAC,GAAD,EAAM,EAAN,EAAU,GAAV,CAnIK;EAoIhBC,SAAS,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CApIK;EAqIhBC,SAAS,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CArIK;EAsIhBC,IAAI,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAtIU;EAuIhBC,WAAW,EAAE,CAAC,CAAD,EAAI,GAAJ,EAAS,GAAT,CAvIG;EAwIhBC,SAAS,EAAE,CAAC,EAAD,EAAK,GAAL,EAAU,GAAV,CAxIK;EAyIhBC,GAAG,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAzIW;EA0IhBC,IAAI,EAAE,CAAC,CAAD,EAAI,GAAJ,EAAS,GAAT,CA1IU;EA2IhBC,OAAO,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA3IO;EA4IhBC,MAAM,EAAE,CAAC,GAAD,EAAM,EAAN,EAAU,EAAV,CA5IQ;EA6IhBC,SAAS,EAAE,CAAC,EAAD,EAAK,GAAL,EAAU,GAAV,CA7IK;EA8IhBC,MAAM,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA9IQ;EA+IhBC,KAAK,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA/IS;EAgJhBC,KAAK,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAhJS;EAiJhBC,UAAU,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAjJI;EAkJhBC,MAAM,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,CAAX,CAlJQ;EAmJhBC,WAAW,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,EAAX;CAnJf;;ACxKA,IAAIC,EAAJ,EAAQC,EAAR,EAAYC,EAAZ,EAAgBC,EAAhB,EAAoBC,EAApB,EAAwBC,EAAxB;AAEAL,EAAE,GAAGC,EAAE,GAAGC,EAAE,GAAGC,EAAE,GAAGC,EAAE,GAAGC,EAAE,GAAG,CAA9B;AAEA,IAAMC,UAAU,GAAG;EACjBC,CAAC,EAAE,CADc;EAEjBrmB,CAAC,EAAE,CAFc;EAGjBsmB,CAAC,EAAE,CAHc;EAIjBnkB,CAAC,EAAE,CAJc;EAKjBokB,CAAC,EAAE,CALc;EAMjBC,CAAC,EAAE,CANc;EAOjBC,CAAC,EAAE,CAPc;EAQjBplB,CAAC,EAAE,CARc;EASjBqlB,CAAC,EAAE,CATc;EAUjBzQ,CAAC,EAAE,CAVc;EAWjB0Q,CAAC,EAAE,CAXc;EAYjBC,CAAC,EAAE,CAZc;EAajBhP,CAAC,EAAE,CAbc;EAcjBiP,CAAC,EAAE,CAdc;EAejBC,CAAC,EAAE,CAfc;EAgBjBC,CAAC,EAAE,CAhBc;EAiBjBzW,CAAC,EAAE,CAjBc;EAkBjBjB,CAAC,EAAE,CAlBc;EAmBjB2X,CAAC,EAAE,CAnBc;EAoBjBC,CAAC,EAAE;CApBL;;AAuBA,IAAMC,KAAK,GAAG,SAARA,KAAQ,CAASC,IAAT,EAAe;MACvBC,GAAJ;MACMC,GAAG,GAAG,EAAZ;MACIC,IAAI,GAAG,EAAX;MACIC,MAAM,GAAG,EAAb;MACIC,YAAY,GAAG,KAAnB;MACIC,MAAM,GAAG,CAAb;;OAEK,IAAItlB,CAAT,IAAcglB,IAAd,EAAoB;QACdf,UAAU,CAACjkB,CAAD,CAAV,IAAiB,IAArB,EAA2B;MACzBslB,MAAM,GAAGrB,UAAU,CAACjkB,CAAD,CAAnB;;UACIilB,GAAJ,EAAS;;YAEHG,MAAM,CAACnnB,MAAP,GAAgB,CAApB,EAAuB;UACrBknB,IAAI,CAACA,IAAI,CAAClnB,MAAN,CAAJ,GAAoB,CAACmnB,MAArB;;;QAEFF,GAAG,CAACA,GAAG,CAACjnB,MAAL,CAAH,GAAkB;UAAEgnB,GAAF;UAAOE;SAAzB;QAEAA,IAAI,GAAG,EAAP;QACAC,MAAM,GAAG,EAAT;QACAC,YAAY,GAAG,KAAf;;;MAGFJ,GAAG,GAAGjlB,CAAN;KAdF,MAeO,IACL,CAAC,GAAD,EAAM,GAAN,EAAWulB,QAAX,CAAoBvlB,CAApB,KACCA,CAAC,KAAK,GAAN,IAAaolB,MAAM,CAACnnB,MAAP,GAAgB,CAA7B,IAAkCmnB,MAAM,CAACA,MAAM,CAACnnB,MAAP,GAAgB,CAAjB,CAAN,KAA8B,GADjE,IAEC+B,CAAC,KAAK,GAAN,IAAaqlB,YAHT,EAIL;UACID,MAAM,CAACnnB,MAAP,KAAkB,CAAtB,EAAyB;;;;UAIrBknB,IAAI,CAAClnB,MAAL,KAAgBqnB,MAApB,EAA4B;;QAE1BJ,GAAG,CAACA,GAAG,CAACjnB,MAAL,CAAH,GAAkB;UAAEgnB,GAAF;UAAOE;SAAzB;QACAA,IAAI,GAAG,CAAC,CAACC,MAAF,CAAP,CAH0B;;YAMtBH,GAAG,KAAK,GAAZ,EAAiB;UACfA,GAAG,GAAG,GAAN;;;YAEEA,GAAG,KAAK,GAAZ,EAAiB;UACfA,GAAG,GAAG,GAAN;;OAVJ,MAYO;QACLE,IAAI,CAACA,IAAI,CAAClnB,MAAN,CAAJ,GAAoB,CAACmnB,MAArB;;;MAGFC,YAAY,GAAGrlB,CAAC,KAAK,GAArB,CArBA;;MAwBAolB,MAAM,GAAG,CAAC,GAAD,EAAM,GAAN,EAAWG,QAAX,CAAoBvlB,CAApB,IAAyBA,CAAzB,GAA6B,EAAtC;KA5BK,MA6BA;MACLolB,MAAM,IAAIplB,CAAV;;UACIA,CAAC,KAAK,GAAV,EAAe;QACbqlB,YAAY,GAAG,IAAf;;;GAxDqB;;;MA8DvBD,MAAM,CAACnnB,MAAP,GAAgB,CAApB,EAAuB;QACjBknB,IAAI,CAAClnB,MAAL,KAAgBqnB,MAApB,EAA4B;;MAE1BJ,GAAG,CAACA,GAAG,CAACjnB,MAAL,CAAH,GAAkB;QAAEgnB,GAAF;QAAOE;OAAzB;MACAA,IAAI,GAAG,CAAC,CAACC,MAAF,CAAP,CAH0B;;UAMtBH,GAAG,KAAK,GAAZ,EAAiB;QACfA,GAAG,GAAG,GAAN;;;UAEEA,GAAG,KAAK,GAAZ,EAAiB;QACfA,GAAG,GAAG,GAAN;;KAVJ,MAYO;MACLE,IAAI,CAACA,IAAI,CAAClnB,MAAN,CAAJ,GAAoB,CAACmnB,MAArB;;;;EAIJF,GAAG,CAACA,GAAG,CAACjnB,MAAL,CAAH,GAAkB;IAAEgnB,GAAF;IAAOE;GAAzB;SAEOD,GAAP;CAlFF;;AAqFA,IAAMna,KAAK,GAAG,SAARA,KAAQ,CAASya,QAAT,EAAmB7S,GAAnB,EAAwB;;EAEpCgR,EAAE,GAAGC,EAAE,GAAGC,EAAE,GAAGC,EAAE,GAAGC,EAAE,GAAGC,EAAE,GAAG,CAA9B,CAFoC;;OAK/B,IAAI7kB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGqmB,QAAQ,CAACvnB,MAA7B,EAAqCkB,CAAC,EAAtC,EAA0C;QAClCa,CAAC,GAAGwlB,QAAQ,CAACrmB,CAAD,CAAlB;;QACI,OAAOsmB,OAAO,CAACzlB,CAAC,CAACilB,GAAH,CAAd,KAA0B,UAA9B,EAA0C;MACxCQ,OAAO,CAACzlB,CAAC,CAACilB,GAAH,CAAP,CAAetS,GAAf,EAAoB3S,CAAC,CAACmlB,IAAtB;;;CARN;;AAaA,IAAMM,OAAO,GAAG;EACdlB,CAAC,CAAC5R,GAAD,EAAM9U,CAAN,EAAS;IACR8lB,EAAE,GAAG9lB,CAAC,CAAC,CAAD,CAAN;IACA+lB,EAAE,GAAG/lB,CAAC,CAAC,CAAD,CAAN;IACAgmB,EAAE,GAAGC,EAAE,GAAG,IAAV;IACAC,EAAE,GAAGJ,EAAL;IACAK,EAAE,GAAGJ,EAAL;WACOjR,GAAG,CAAC+S,MAAJ,CAAW/B,EAAX,EAAeC,EAAf,CAAP;GAPY;;EAUd9P,CAAC,CAACnB,GAAD,EAAM9U,CAAN,EAAS;IACR8lB,EAAE,IAAI9lB,CAAC,CAAC,CAAD,CAAP;IACA+lB,EAAE,IAAI/lB,CAAC,CAAC,CAAD,CAAP;IACAgmB,EAAE,GAAGC,EAAE,GAAG,IAAV;IACAC,EAAE,GAAGJ,EAAL;IACAK,EAAE,GAAGJ,EAAL;WACOjR,GAAG,CAAC+S,MAAJ,CAAW/B,EAAX,EAAeC,EAAf,CAAP;GAhBY;;EAmBdO,CAAC,CAACxR,GAAD,EAAM9U,CAAN,EAAS;IACR8lB,EAAE,GAAG9lB,CAAC,CAAC,CAAD,CAAN;IACA+lB,EAAE,GAAG/lB,CAAC,CAAC,CAAD,CAAN;IACAgmB,EAAE,GAAGhmB,CAAC,CAAC,CAAD,CAAN;IACAimB,EAAE,GAAGjmB,CAAC,CAAC,CAAD,CAAN;WACO8U,GAAG,CAACgT,aAAJ,CAAkB,GAAG9nB,CAArB,CAAP;GAxBY;;EA2BdmC,CAAC,CAAC2S,GAAD,EAAM9U,CAAN,EAAS;IACR8U,GAAG,CAACgT,aAAJ,CACE9nB,CAAC,CAAC,CAAD,CAAD,GAAO8lB,EADT,EAEE9lB,CAAC,CAAC,CAAD,CAAD,GAAO+lB,EAFT,EAGE/lB,CAAC,CAAC,CAAD,CAAD,GAAO8lB,EAHT,EAIE9lB,CAAC,CAAC,CAAD,CAAD,GAAO+lB,EAJT,EAKE/lB,CAAC,CAAC,CAAD,CAAD,GAAO8lB,EALT,EAME9lB,CAAC,CAAC,CAAD,CAAD,GAAO+lB,EANT;IAQAC,EAAE,GAAGF,EAAE,GAAG9lB,CAAC,CAAC,CAAD,CAAX;IACAimB,EAAE,GAAGF,EAAE,GAAG/lB,CAAC,CAAC,CAAD,CAAX;IACA8lB,EAAE,IAAI9lB,CAAC,CAAC,CAAD,CAAP;WACQ+lB,EAAE,IAAI/lB,CAAC,CAAC,CAAD,CAAf;GAvCY;;EA0Cd4X,CAAC,CAAC9C,GAAD,EAAM9U,CAAN,EAAS;QACJgmB,EAAE,KAAK,IAAX,EAAiB;MACfA,EAAE,GAAGF,EAAL;MACAG,EAAE,GAAGF,EAAL;;;IAGFjR,GAAG,CAACgT,aAAJ,CAAkBhC,EAAE,IAAIE,EAAE,GAAGF,EAAT,CAApB,EAAkCC,EAAE,IAAIE,EAAE,GAAGF,EAAT,CAApC,EAAkD/lB,CAAC,CAAC,CAAD,CAAnD,EAAwDA,CAAC,CAAC,CAAD,CAAzD,EAA8DA,CAAC,CAAC,CAAD,CAA/D,EAAoEA,CAAC,CAAC,CAAD,CAArE;IACAgmB,EAAE,GAAGhmB,CAAC,CAAC,CAAD,CAAN;IACAimB,EAAE,GAAGjmB,CAAC,CAAC,CAAD,CAAN;IACA8lB,EAAE,GAAG9lB,CAAC,CAAC,CAAD,CAAN;WACQ+lB,EAAE,GAAG/lB,CAAC,CAAC,CAAD,CAAd;GApDY;;EAuDd6mB,CAAC,CAAC/R,GAAD,EAAM9U,CAAN,EAAS;QACJgmB,EAAE,KAAK,IAAX,EAAiB;MACfA,EAAE,GAAGF,EAAL;MACAG,EAAE,GAAGF,EAAL;;;IAGFjR,GAAG,CAACgT,aAAJ,CACEhC,EAAE,IAAIE,EAAE,GAAGF,EAAT,CADJ,EAEEC,EAAE,IAAIE,EAAE,GAAGF,EAAT,CAFJ,EAGED,EAAE,GAAG9lB,CAAC,CAAC,CAAD,CAHR,EAIE+lB,EAAE,GAAG/lB,CAAC,CAAC,CAAD,CAJR,EAKE8lB,EAAE,GAAG9lB,CAAC,CAAC,CAAD,CALR,EAME+lB,EAAE,GAAG/lB,CAAC,CAAC,CAAD,CANR;IAQAgmB,EAAE,GAAGF,EAAE,GAAG9lB,CAAC,CAAC,CAAD,CAAX;IACAimB,EAAE,GAAGF,EAAE,GAAG/lB,CAAC,CAAC,CAAD,CAAX;IACA8lB,EAAE,IAAI9lB,CAAC,CAAC,CAAD,CAAP;WACQ+lB,EAAE,IAAI/lB,CAAC,CAAC,CAAD,CAAf;GAxEY;;EA2Ed2mB,CAAC,CAAC7R,GAAD,EAAM9U,CAAN,EAAS;IACRgmB,EAAE,GAAGhmB,CAAC,CAAC,CAAD,CAAN;IACAimB,EAAE,GAAGjmB,CAAC,CAAC,CAAD,CAAN;IACA8lB,EAAE,GAAG9lB,CAAC,CAAC,CAAD,CAAN;IACA+lB,EAAE,GAAG/lB,CAAC,CAAC,CAAD,CAAN;WACO8U,GAAG,CAACiT,gBAAJ,CAAqB/nB,CAAC,CAAC,CAAD,CAAtB,EAA2BA,CAAC,CAAC,CAAD,CAA5B,EAAiC8lB,EAAjC,EAAqCC,EAArC,CAAP;GAhFY;;EAmFda,CAAC,CAAC9R,GAAD,EAAM9U,CAAN,EAAS;IACR8U,GAAG,CAACiT,gBAAJ,CAAqB/nB,CAAC,CAAC,CAAD,CAAD,GAAO8lB,EAA5B,EAAgC9lB,CAAC,CAAC,CAAD,CAAD,GAAO+lB,EAAvC,EAA2C/lB,CAAC,CAAC,CAAD,CAAD,GAAO8lB,EAAlD,EAAsD9lB,CAAC,CAAC,CAAD,CAAD,GAAO+lB,EAA7D;IACAC,EAAE,GAAGF,EAAE,GAAG9lB,CAAC,CAAC,CAAD,CAAX;IACAimB,EAAE,GAAGF,EAAE,GAAG/lB,CAAC,CAAC,CAAD,CAAX;IACA8lB,EAAE,IAAI9lB,CAAC,CAAC,CAAD,CAAP;WACQ+lB,EAAE,IAAI/lB,CAAC,CAAC,CAAD,CAAf;GAxFY;;EA2Fd8mB,CAAC,CAAChS,GAAD,EAAM9U,CAAN,EAAS;QACJgmB,EAAE,KAAK,IAAX,EAAiB;MACfA,EAAE,GAAGF,EAAL;MACAG,EAAE,GAAGF,EAAL;KAFF,MAGO;MACLC,EAAE,GAAGF,EAAE,IAAIE,EAAE,GAAGF,EAAT,CAAP;MACAG,EAAE,GAAGF,EAAE,IAAIE,EAAE,GAAGF,EAAT,CAAP;;;IAGFjR,GAAG,CAACiT,gBAAJ,CAAqB/B,EAArB,EAAyBC,EAAzB,EAA6BjmB,CAAC,CAAC,CAAD,CAA9B,EAAmCA,CAAC,CAAC,CAAD,CAApC;IACAgmB,EAAE,GAAGF,EAAE,IAAIE,EAAE,GAAGF,EAAT,CAAP;IACAG,EAAE,GAAGF,EAAE,IAAIE,EAAE,GAAGF,EAAT,CAAP;IACAD,EAAE,GAAG9lB,CAAC,CAAC,CAAD,CAAN;WACQ+lB,EAAE,GAAG/lB,CAAC,CAAC,CAAD,CAAd;GAxGY;;EA2Gd+mB,CAAC,CAACjS,GAAD,EAAM9U,CAAN,EAAS;QACJgmB,EAAE,KAAK,IAAX,EAAiB;MACfA,EAAE,GAAGF,EAAL;MACAG,EAAE,GAAGF,EAAL;KAFF,MAGO;MACLC,EAAE,GAAGF,EAAE,IAAIE,EAAE,GAAGF,EAAT,CAAP;MACAG,EAAE,GAAGF,EAAE,IAAIE,EAAE,GAAGF,EAAT,CAAP;;;IAGFjR,GAAG,CAACiT,gBAAJ,CAAqB/B,EAArB,EAAyBC,EAAzB,EAA6BH,EAAE,GAAG9lB,CAAC,CAAC,CAAD,CAAnC,EAAwC+lB,EAAE,GAAG/lB,CAAC,CAAC,CAAD,CAA9C;IACA8lB,EAAE,IAAI9lB,CAAC,CAAC,CAAD,CAAP;WACQ+lB,EAAE,IAAI/lB,CAAC,CAAC,CAAD,CAAf;GAtHY;;EAyHdqmB,CAAC,CAACvR,GAAD,EAAM9U,CAAN,EAAS;IACRgoB,QAAQ,CAAClT,GAAD,EAAMgR,EAAN,EAAUC,EAAV,EAAc/lB,CAAd,CAAR;IACA8lB,EAAE,GAAG9lB,CAAC,CAAC,CAAD,CAAN;WACQ+lB,EAAE,GAAG/lB,CAAC,CAAC,CAAD,CAAd;GA5HY;;EA+HdA,CAAC,CAAC8U,GAAD,EAAM9U,CAAN,EAAS;IACRA,CAAC,CAAC,CAAD,CAAD,IAAQ8lB,EAAR;IACA9lB,CAAC,CAAC,CAAD,CAAD,IAAQ+lB,EAAR;IACAiC,QAAQ,CAAClT,GAAD,EAAMgR,EAAN,EAAUC,EAAV,EAAc/lB,CAAd,CAAR;IACA8lB,EAAE,GAAG9lB,CAAC,CAAC,CAAD,CAAN;WACQ+lB,EAAE,GAAG/lB,CAAC,CAAC,CAAD,CAAd;GApIY;;EAuIdymB,CAAC,CAAC3R,GAAD,EAAM9U,CAAN,EAAS;IACR8lB,EAAE,GAAG9lB,CAAC,CAAC,CAAD,CAAN;IACA+lB,EAAE,GAAG/lB,CAAC,CAAC,CAAD,CAAN;IACAgmB,EAAE,GAAGC,EAAE,GAAG,IAAV;WACOnR,GAAG,CAACmT,MAAJ,CAAWnC,EAAX,EAAeC,EAAf,CAAP;GA3IY;;EA8Id1kB,CAAC,CAACyT,GAAD,EAAM9U,CAAN,EAAS;IACR8lB,EAAE,IAAI9lB,CAAC,CAAC,CAAD,CAAP;IACA+lB,EAAE,IAAI/lB,CAAC,CAAC,CAAD,CAAP;IACAgmB,EAAE,GAAGC,EAAE,GAAG,IAAV;WACOnR,GAAG,CAACmT,MAAJ,CAAWnC,EAAX,EAAeC,EAAf,CAAP;GAlJY;;EAqJdQ,CAAC,CAACzR,GAAD,EAAM9U,CAAN,EAAS;IACR8lB,EAAE,GAAG9lB,CAAC,CAAC,CAAD,CAAN;IACAgmB,EAAE,GAAGC,EAAE,GAAG,IAAV;WACOnR,GAAG,CAACmT,MAAJ,CAAWnC,EAAX,EAAeC,EAAf,CAAP;GAxJY;;EA2JdS,CAAC,CAAC1R,GAAD,EAAM9U,CAAN,EAAS;IACR8lB,EAAE,IAAI9lB,CAAC,CAAC,CAAD,CAAP;IACAgmB,EAAE,GAAGC,EAAE,GAAG,IAAV;WACOnR,GAAG,CAACmT,MAAJ,CAAWnC,EAAX,EAAeC,EAAf,CAAP;GA9JY;;EAiKdzV,CAAC,CAACwE,GAAD,EAAM9U,CAAN,EAAS;IACR+lB,EAAE,GAAG/lB,CAAC,CAAC,CAAD,CAAN;IACAgmB,EAAE,GAAGC,EAAE,GAAG,IAAV;WACOnR,GAAG,CAACmT,MAAJ,CAAWnC,EAAX,EAAeC,EAAf,CAAP;GApKY;;EAuKd1W,CAAC,CAACyF,GAAD,EAAM9U,CAAN,EAAS;IACR+lB,EAAE,IAAI/lB,CAAC,CAAC,CAAD,CAAP;IACAgmB,EAAE,GAAGC,EAAE,GAAG,IAAV;WACOnR,GAAG,CAACmT,MAAJ,CAAWnC,EAAX,EAAeC,EAAf,CAAP;GA1KY;;EA6KdiB,CAAC,CAAClS,GAAD,EAAM;IACLA,GAAG,CAACoT,SAAJ;IACApC,EAAE,GAAGI,EAAL;WACQH,EAAE,GAAGI,EAAb;GAhLY;;EAmLdc,CAAC,CAACnS,GAAD,EAAM;IACLA,GAAG,CAACoT,SAAJ;IACApC,EAAE,GAAGI,EAAL;WACQH,EAAE,GAAGI,EAAb;;;CAtLJ;;AA0LA,IAAM6B,QAAQ,GAAG,SAAXA,QAAW,CAASlT,GAAT,EAAczI,CAAd,EAAiB8b,CAAjB,EAAoBC,MAApB,EAA4B;MACrC,CAACC,EAAD,EAAKC,EAAL,EAASC,GAAT,EAAcC,KAAd,EAAqBC,KAArB,EAA4BC,EAA5B,EAAgCC,EAAhC,IAAsCP,MAA5C;MACMQ,IAAI,GAAGC,aAAa,CAACH,EAAD,EAAKC,EAAL,EAASN,EAAT,EAAaC,EAAb,EAAiBE,KAAjB,EAAwBC,KAAxB,EAA+BF,GAA/B,EAAoClc,CAApC,EAAuC8b,CAAvC,CAA1B;;OAEK,IAAIW,GAAT,IAAgBF,IAAhB,EAAsB;QACdG,GAAG,GAAGC,eAAe,CAAC,GAAGF,GAAJ,CAA3B;IACAhU,GAAG,CAACgT,aAAJ,CAAkB,GAAGiB,GAArB;;CANJ;;;AAWA,IAAMF,aAAa,GAAG,SAAhBA,aAAgB,CAASxc,CAAT,EAAY8b,CAAZ,EAAeE,EAAf,EAAmBC,EAAnB,EAAuBE,KAAvB,EAA8BC,KAA9B,EAAqCQ,OAArC,EAA8CC,EAA9C,EAAkDC,EAAlD,EAAsD;MACpEC,EAAE,GAAGH,OAAO,IAAI9lB,IAAI,CAACkmB,EAAL,GAAU,GAAd,CAAlB;MACMC,MAAM,GAAGnmB,IAAI,CAAComB,GAAL,CAASH,EAAT,CAAf;MACMI,MAAM,GAAGrmB,IAAI,CAACsmB,GAAL,CAASL,EAAT,CAAf;EACAf,EAAE,GAAGllB,IAAI,CAACumB,GAAL,CAASrB,EAAT,CAAL;EACAC,EAAE,GAAGnlB,IAAI,CAACumB,GAAL,CAASpB,EAAT,CAAL;EACAtC,EAAE,GAAGwD,MAAM,IAAIN,EAAE,GAAG7c,CAAT,CAAN,GAAoB,GAApB,GAA0Bid,MAAM,IAAIH,EAAE,GAAGhB,CAAT,CAAN,GAAoB,GAAnD;EACAlC,EAAE,GAAGuD,MAAM,IAAIL,EAAE,GAAGhB,CAAT,CAAN,GAAoB,GAApB,GAA0BmB,MAAM,IAAIJ,EAAE,GAAG7c,CAAT,CAAN,GAAoB,GAAnD;MACIsd,EAAE,GAAI3D,EAAE,GAAGA,EAAN,IAAaqC,EAAE,GAAGA,EAAlB,IAAyBpC,EAAE,GAAGA,EAAN,IAAaqC,EAAE,GAAGA,EAAlB,CAAjC;;MACIqB,EAAE,GAAG,CAAT,EAAY;IACVA,EAAE,GAAGxmB,IAAI,CAACymB,IAAL,CAAUD,EAAV,CAAL;IACAtB,EAAE,IAAIsB,EAAN;IACArB,EAAE,IAAIqB,EAAN;;;MAGIE,GAAG,GAAGL,MAAM,GAAGnB,EAArB;MACMyB,GAAG,GAAGR,MAAM,GAAGjB,EAArB;MACM0B,GAAG,GAAG,CAACT,MAAD,GAAUhB,EAAtB;MACM0B,GAAG,GAAGR,MAAM,GAAGlB,EAArB;MACM2B,EAAE,GAAGJ,GAAG,GAAGX,EAAN,GAAWY,GAAG,GAAGX,EAA5B;MACMe,EAAE,GAAGH,GAAG,GAAGb,EAAN,GAAWc,GAAG,GAAGb,EAA5B;MACM/P,EAAE,GAAGyQ,GAAG,GAAGxd,CAAN,GAAUyd,GAAG,GAAG3B,CAA3B;MACM9O,EAAE,GAAG0Q,GAAG,GAAG1d,CAAN,GAAU2d,GAAG,GAAG7B,CAA3B;MAEMgC,CAAC,GAAG,CAAC/Q,EAAE,GAAG6Q,EAAN,KAAa7Q,EAAE,GAAG6Q,EAAlB,IAAwB,CAAC5Q,EAAE,GAAG6Q,EAAN,KAAa7Q,EAAE,GAAG6Q,EAAlB,CAAlC;MACIE,UAAU,GAAG,IAAID,CAAJ,GAAQ,IAAzB;;MACIC,UAAU,GAAG,CAAjB,EAAoB;IAClBA,UAAU,GAAG,CAAb;;;MAEEC,OAAO,GAAGlnB,IAAI,CAACymB,IAAL,CAAUQ,UAAV,CAAd;;MACI3B,KAAK,KAAKD,KAAd,EAAqB;IACnB6B,OAAO,GAAG,CAACA,OAAX;;;MAGIC,EAAE,GAAG,OAAOL,EAAE,GAAG7Q,EAAZ,IAAkBiR,OAAO,IAAIhR,EAAE,GAAG6Q,EAAT,CAApC;MACMK,EAAE,GAAG,OAAOL,EAAE,GAAG7Q,EAAZ,IAAkBgR,OAAO,IAAIjR,EAAE,GAAG6Q,EAAT,CAApC;MAEMO,GAAG,GAAGrnB,IAAI,CAACsnB,KAAL,CAAWP,EAAE,GAAGK,EAAhB,EAAoBN,EAAE,GAAGK,EAAzB,CAAZ;MACMI,GAAG,GAAGvnB,IAAI,CAACsnB,KAAL,CAAWpR,EAAE,GAAGkR,EAAhB,EAAoBnR,EAAE,GAAGkR,EAAzB,CAAZ;MAEIK,MAAM,GAAGD,GAAG,GAAGF,GAAnB;;MACIG,MAAM,GAAG,CAAT,IAAclC,KAAK,KAAK,CAA5B,EAA+B;IAC7BkC,MAAM,IAAI,IAAIxnB,IAAI,CAACkmB,EAAnB;GADF,MAEO,IAAIsB,MAAM,GAAG,CAAT,IAAclC,KAAK,KAAK,CAA5B,EAA+B;IACpCkC,MAAM,IAAI,IAAIxnB,IAAI,CAACkmB,EAAnB;;;MAGIuB,QAAQ,GAAGznB,IAAI,CAACyQ,IAAL,CAAUzQ,IAAI,CAACumB,GAAL,CAASiB,MAAM,IAAIxnB,IAAI,CAACkmB,EAAL,GAAU,GAAV,GAAgB,KAApB,CAAf,CAAV,CAAjB;MACMwB,MAAM,GAAG,EAAf;;OAEK,IAAIvpB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGspB,QAApB,EAA8BtpB,CAAC,EAA/B,EAAmC;QAC3BwpB,GAAG,GAAGN,GAAG,GAAIlpB,CAAC,GAAGqpB,MAAL,GAAeC,QAAjC;QACMG,GAAG,GAAGP,GAAG,GAAI,CAAClpB,CAAC,GAAG,CAAL,IAAUqpB,MAAX,GAAqBC,QAAvC;IACAC,MAAM,CAACvpB,CAAD,CAAN,GAAY,CAACgpB,EAAD,EAAKC,EAAL,EAASO,GAAT,EAAcC,GAAd,EAAmB1C,EAAnB,EAAuBC,EAAvB,EAA2BgB,MAA3B,EAAmCE,MAAnC,CAAZ;;;SAGKqB,MAAP;CAxDF;;AA2DA,IAAM7B,eAAe,GAAG,SAAlBA,eAAkB,CAASlD,EAAT,EAAaC,EAAb,EAAiByE,GAAjB,EAAsBE,GAAtB,EAA2BrC,EAA3B,EAA+BC,EAA/B,EAAmCgB,MAAnC,EAA2CE,MAA3C,EAAmD;MACnEK,GAAG,GAAGL,MAAM,GAAGnB,EAArB;MACMyB,GAAG,GAAG,CAACR,MAAD,GAAUhB,EAAtB;MACMyB,GAAG,GAAGT,MAAM,GAAGjB,EAArB;MACM2B,GAAG,GAAGR,MAAM,GAAGlB,EAArB;MAEM0C,OAAO,GAAG,OAAON,GAAG,GAAGF,GAAb,CAAhB;MACMzD,CAAC,GACH,IAAI,CAAL,GAAU5jB,IAAI,CAAComB,GAAL,CAASyB,OAAO,GAAG,GAAnB,CAAV,GAAoC7nB,IAAI,CAAComB,GAAL,CAASyB,OAAO,GAAG,GAAnB,CAArC,GACA7nB,IAAI,CAAComB,GAAL,CAASyB,OAAT,CAFF;MAGM5R,EAAE,GAAG0M,EAAE,GAAG3iB,IAAI,CAACsmB,GAAL,CAASe,GAAT,CAAL,GAAqBzD,CAAC,GAAG5jB,IAAI,CAAComB,GAAL,CAASiB,GAAT,CAApC;MACMnR,EAAE,GAAG0M,EAAE,GAAG5iB,IAAI,CAAComB,GAAL,CAASiB,GAAT,CAAL,GAAqBzD,CAAC,GAAG5jB,IAAI,CAACsmB,GAAL,CAASe,GAAT,CAApC;MACMS,EAAE,GAAGnF,EAAE,GAAG3iB,IAAI,CAACsmB,GAAL,CAASiB,GAAT,CAAhB;MACMQ,EAAE,GAAGnF,EAAE,GAAG5iB,IAAI,CAAComB,GAAL,CAASmB,GAAT,CAAhB;MACMpR,EAAE,GAAG2R,EAAE,GAAGlE,CAAC,GAAG5jB,IAAI,CAAComB,GAAL,CAASmB,GAAT,CAApB;MACMnR,EAAE,GAAG2R,EAAE,GAAGnE,CAAC,GAAG5jB,IAAI,CAACsmB,GAAL,CAASiB,GAAT,CAApB;SAEO,CACLb,GAAG,GAAGzQ,EAAN,GAAW0Q,GAAG,GAAGzQ,EADZ,EAEL0Q,GAAG,GAAG3Q,EAAN,GAAW4Q,GAAG,GAAG3Q,EAFZ,EAGLwQ,GAAG,GAAGvQ,EAAN,GAAWwQ,GAAG,GAAGvQ,EAHZ,EAILwQ,GAAG,GAAGzQ,EAAN,GAAW0Q,GAAG,GAAGzQ,EAJZ,EAKLsQ,GAAG,GAAGoB,EAAN,GAAWnB,GAAG,GAAGoB,EALZ,EAMLnB,GAAG,GAAGkB,EAAN,GAAWjB,GAAG,GAAGkB,EANZ,CAAP;CAjBF;;AA2BA,MAAMC,OAAN,CAAc;SACLje,KAAP,CAAa4H,GAAb,EAAkBqS,IAAlB,EAAwB;QAChBQ,QAAQ,GAAGT,KAAK,CAACC,IAAD,CAAtB;IACAja,KAAK,CAACya,QAAD,EAAW7S,GAAX,CAAL;;;;;ACxZJ,IAAM;UAAE7R;IAAWzC,SAAnB;;;AAIA,IAAM4qB,KAAK,GAAG,OAAO,CAACjoB,IAAI,CAACymB,IAAL,CAAU,CAAV,IAAe,GAAhB,IAAuB,GAA9B,CAAd;AACA,kBAAe;EACbyB,UAAU,GAAG;SACNtS,IAAL,GAAY,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,EAAU,CAAV,EAAa,CAAb,EAAgB,CAAhB,CAAZ,CADW;;WAEH,KAAKuS,SAAL,GAAiB,EAAzB;GAHW;;EAMbC,IAAI,GAAG;SACAD,SAAL,CAAe/qB,IAAf,CAAoB,KAAKwY,IAAL,CAAU/X,KAAV,EAApB,EADK;;;WAGE,KAAKkY,UAAL,CAAgB,GAAhB,CAAP;GATW;;EAYbsS,OAAO,GAAG;SACHzS,IAAL,GAAY,KAAKuS,SAAL,CAAeG,GAAf,MAAwB,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,EAAU,CAAV,EAAa,CAAb,EAAgB,CAAhB,CAApC;WACO,KAAKvS,UAAL,CAAgB,GAAhB,CAAP;GAdW;;EAiBbgP,SAAS,GAAG;WACH,KAAKhP,UAAL,CAAgB,GAAhB,CAAP;GAlBW;;EAqBbwS,SAAS,CAACC,CAAD,EAAI;WACJ,KAAKzS,UAAL,WAAmBjW,QAAM,CAAC0oB,CAAD,CAAzB,QAAP;GAtBW;;EAyBbC,WAAW,EAAE;IACXC,IAAI,EAAE,CADK;IAEXC,KAAK,EAAE,CAFI;IAGXC,MAAM,EAAE;GA5BG;;EA+BbC,OAAO,CAAC7pB,CAAD,EAAI;QACL,OAAOA,CAAP,KAAa,QAAjB,EAA2B;MACzBA,CAAC,GAAG,KAAKypB,WAAL,CAAiBzpB,CAAC,CAACoG,WAAF,EAAjB,CAAJ;;;WAEK,KAAK2Q,UAAL,WAAmB/W,CAAnB,QAAP;GAnCW;;EAsCb8pB,YAAY,EAAE;IACZC,KAAK,EAAE,CADK;IAEZJ,KAAK,EAAE,CAFK;IAGZK,KAAK,EAAE;GAzCI;;EA4CbC,QAAQ,CAACvY,CAAD,EAAI;QACN,OAAOA,CAAP,KAAa,QAAjB,EAA2B;MACzBA,CAAC,GAAG,KAAKoY,YAAL,CAAkBpY,CAAC,CAACtL,WAAF,EAAlB,CAAJ;;;WAEK,KAAK2Q,UAAL,WAAmBrF,CAAnB,QAAP;GAhDW;;EAmDbwY,UAAU,CAACpW,CAAD,EAAI;WACL,KAAKiD,UAAL,WAAmBjW,QAAM,CAACgT,CAAD,CAAzB,QAAP;GApDW;;EAuDbqW,IAAI,CAAClsB,MAAD,EAAuB;QAAdf,OAAc,uEAAJ,EAAI;QACnBktB,cAAc,GAAGnsB,MAAvB;;QACI,CAACW,KAAK,CAAC6B,OAAN,CAAcxC,MAAd,CAAL,EAA4B;MAC1BA,MAAM,GAAG,CAACA,MAAD,EAASf,OAAO,CAACwc,KAAR,IAAiBzb,MAA1B,CAAT;;;QAGIosB,KAAK,GAAGpsB,MAAM,CAACqsB,KAAP,CAAapgB,CAAC,IAAIqgB,MAAM,CAACC,QAAP,CAAgBtgB,CAAhB,KAAsBA,CAAC,GAAG,CAA5C,CAAd;;QACI,CAACmgB,KAAL,EAAY;YACJ,IAAIttB,KAAJ,gBACI0tB,IAAI,CAACC,SAAL,CAAeN,cAAf,CADJ,eACuCK,IAAI,CAACC,SAAL,CACzCxtB,OADyC,CADvC,8DAAN;;;IAOFe,MAAM,GAAGA,MAAM,CAAC0C,GAAP,CAAWG,QAAX,EAAmBrC,IAAnB,CAAwB,GAAxB,CAAT;WACO,KAAKsY,UAAL,YAAoB9Y,MAApB,eAA+B6C,QAAM,CAAC5D,OAAO,CAACytB,KAAR,IAAiB,CAAlB,CAArC,QAAP;GAvEW;;EA0EbC,MAAM,GAAG;WACA,KAAK7T,UAAL,CAAgB,QAAhB,CAAP;GA3EW;;EA8Eb2O,MAAM,CAACxb,CAAD,EAAI8b,CAAJ,EAAO;WACJ,KAAKjP,UAAL,WAAmBjW,QAAM,CAACoJ,CAAD,CAAzB,cAAgCpJ,QAAM,CAACklB,CAAD,CAAtC,QAAP;GA/EW;;EAkFbF,MAAM,CAAC5b,CAAD,EAAI8b,CAAJ,EAAO;WACJ,KAAKjP,UAAL,WAAmBjW,QAAM,CAACoJ,CAAD,CAAzB,cAAgCpJ,QAAM,CAACklB,CAAD,CAAtC,QAAP;GAnFW;;EAsFbL,aAAa,CAACkF,IAAD,EAAOC,IAAP,EAAaC,IAAb,EAAmBC,IAAnB,EAAyB9gB,CAAzB,EAA4B8b,CAA5B,EAA+B;WACnC,KAAKjP,UAAL,WACFjW,QAAM,CAAC+pB,IAAD,CADJ,cACc/pB,QAAM,CAACgqB,IAAD,CADpB,cAC8BhqB,QAAM,CAACiqB,IAAD,CADpC,cAC8CjqB,QAAM,CAACkqB,IAAD,CADpD,cAC8DlqB,QAAM,CACvEoJ,CADuE,CADpE,cAGApJ,QAAM,CAACklB,CAAD,CAHN,QAAP;GAvFW;;EA8FbJ,gBAAgB,CAACqF,GAAD,EAAMC,GAAN,EAAWhhB,CAAX,EAAc8b,CAAd,EAAiB;WACxB,KAAKjP,UAAL,WACFjW,QAAM,CAACmqB,GAAD,CADJ,cACanqB,QAAM,CAACoqB,GAAD,CADnB,cAC4BpqB,QAAM,CAACoJ,CAAD,CADlC,cACyCpJ,QAAM,CAACklB,CAAD,CAD/C,QAAP;GA/FW;;EAoGbmF,IAAI,CAACjhB,CAAD,EAAI8b,CAAJ,EAAOwD,CAAP,EAAUnF,CAAV,EAAa;WACR,KAAKtN,UAAL,WACFjW,QAAM,CAACoJ,CAAD,CADJ,cACWpJ,QAAM,CAACklB,CAAD,CADjB,cACwBllB,QAAM,CAAC0oB,CAAD,CAD9B,cACqC1oB,QAAM,CAACujB,CAAD,CAD3C,SAAP;GArGW;;EA0Gb+G,WAAW,CAAClhB,CAAD,EAAI8b,CAAJ,EAAOwD,CAAP,EAAUnF,CAAV,EAAalX,CAAb,EAAgB;QACrBA,CAAC,IAAI,IAAT,EAAe;MACbA,CAAC,GAAG,CAAJ;;;IAEFA,CAAC,GAAGnM,IAAI,CAACoP,GAAL,CAASjD,CAAT,EAAY,MAAMqc,CAAlB,EAAqB,MAAMnF,CAA3B,CAAJ,CAJyB;;QAOnBrkB,CAAC,GAAGmN,CAAC,IAAI,MAAM8b,KAAV,CAAX;SAEKvD,MAAL,CAAYxb,CAAC,GAAGiD,CAAhB,EAAmB6Y,CAAnB;SACKF,MAAL,CAAY5b,CAAC,GAAGsf,CAAJ,GAAQrc,CAApB,EAAuB6Y,CAAvB;SACKL,aAAL,CAAmBzb,CAAC,GAAGsf,CAAJ,GAAQxpB,CAA3B,EAA8BgmB,CAA9B,EAAiC9b,CAAC,GAAGsf,CAArC,EAAwCxD,CAAC,GAAGhmB,CAA5C,EAA+CkK,CAAC,GAAGsf,CAAnD,EAAsDxD,CAAC,GAAG7Y,CAA1D;SACK2Y,MAAL,CAAY5b,CAAC,GAAGsf,CAAhB,EAAmBxD,CAAC,GAAG3B,CAAJ,GAAQlX,CAA3B;SACKwY,aAAL,CAAmBzb,CAAC,GAAGsf,CAAvB,EAA0BxD,CAAC,GAAG3B,CAAJ,GAAQrkB,CAAlC,EAAqCkK,CAAC,GAAGsf,CAAJ,GAAQxpB,CAA7C,EAAgDgmB,CAAC,GAAG3B,CAApD,EAAuDna,CAAC,GAAGsf,CAAJ,GAAQrc,CAA/D,EAAkE6Y,CAAC,GAAG3B,CAAtE;SACKyB,MAAL,CAAY5b,CAAC,GAAGiD,CAAhB,EAAmB6Y,CAAC,GAAG3B,CAAvB;SACKsB,aAAL,CAAmBzb,CAAC,GAAGlK,CAAvB,EAA0BgmB,CAAC,GAAG3B,CAA9B,EAAiCna,CAAjC,EAAoC8b,CAAC,GAAG3B,CAAJ,GAAQrkB,CAA5C,EAA+CkK,CAA/C,EAAkD8b,CAAC,GAAG3B,CAAJ,GAAQlX,CAA1D;SACK2Y,MAAL,CAAY5b,CAAZ,EAAe8b,CAAC,GAAG7Y,CAAnB;SACKwY,aAAL,CAAmBzb,CAAnB,EAAsB8b,CAAC,GAAGhmB,CAA1B,EAA6BkK,CAAC,GAAGlK,CAAjC,EAAoCgmB,CAApC,EAAuC9b,CAAC,GAAGiD,CAA3C,EAA8C6Y,CAA9C;WACO,KAAKD,SAAL,EAAP;GA5HW;;EA+HbsF,OAAO,CAACnhB,CAAD,EAAI8b,CAAJ,EAAOtO,EAAP,EAAWC,EAAX,EAAe;;QAEhBA,EAAE,IAAI,IAAV,EAAgB;MACdA,EAAE,GAAGD,EAAL;;;IAEFxN,CAAC,IAAIwN,EAAL;IACAsO,CAAC,IAAIrO,EAAL;QACMoP,EAAE,GAAGrP,EAAE,GAAGuR,KAAhB;QACMjC,EAAE,GAAGrP,EAAE,GAAGsR,KAAhB;QACMqC,EAAE,GAAGphB,CAAC,GAAGwN,EAAE,GAAG,CAApB;QACM6T,EAAE,GAAGvF,CAAC,GAAGrO,EAAE,GAAG,CAApB;QACM6T,EAAE,GAAGthB,CAAC,GAAGwN,EAAf;QACM+T,EAAE,GAAGzF,CAAC,GAAGrO,EAAf;SAEK+N,MAAL,CAAYxb,CAAZ,EAAeuhB,EAAf;SACK9F,aAAL,CAAmBzb,CAAnB,EAAsBuhB,EAAE,GAAGzE,EAA3B,EAA+BwE,EAAE,GAAGzE,EAApC,EAAwCf,CAAxC,EAA2CwF,EAA3C,EAA+CxF,CAA/C;SACKL,aAAL,CAAmB6F,EAAE,GAAGzE,EAAxB,EAA4Bf,CAA5B,EAA+BsF,EAA/B,EAAmCG,EAAE,GAAGzE,EAAxC,EAA4CsE,EAA5C,EAAgDG,EAAhD;SACK9F,aAAL,CAAmB2F,EAAnB,EAAuBG,EAAE,GAAGzE,EAA5B,EAAgCwE,EAAE,GAAGzE,EAArC,EAAyCwE,EAAzC,EAA6CC,EAA7C,EAAiDD,EAAjD;SACK5F,aAAL,CAAmB6F,EAAE,GAAGzE,EAAxB,EAA4BwE,EAA5B,EAAgCrhB,CAAhC,EAAmCuhB,EAAE,GAAGzE,EAAxC,EAA4C9c,CAA5C,EAA+CuhB,EAA/C;WACO,KAAK1F,SAAL,EAAP;GAlJW;;EAqJb2F,MAAM,CAACxhB,CAAD,EAAI8b,CAAJ,EAAO2F,MAAP,EAAe;WACZ,KAAKN,OAAL,CAAanhB,CAAb,EAAgB8b,CAAhB,EAAmB2F,MAAnB,CAAP;GAtJW;;EAyJbC,GAAG,CAAC1hB,CAAD,EAAI8b,CAAJ,EAAO2F,MAAP,EAAeE,UAAf,EAA2BC,QAA3B,EAAqCC,aAArC,EAAoD;QACjDA,aAAa,IAAI,IAArB,EAA2B;MACzBA,aAAa,GAAG,KAAhB;;;QAEIC,MAAM,GAAG,MAAMhrB,IAAI,CAACkmB,EAA1B;QACM+E,OAAO,GAAG,MAAMjrB,IAAI,CAACkmB,EAA3B;QAEIgF,QAAQ,GAAGJ,QAAQ,GAAGD,UAA1B;;QAEI7qB,IAAI,CAACumB,GAAL,CAAS2E,QAAT,IAAqBF,MAAzB,EAAiC;;MAE/BE,QAAQ,GAAGF,MAAX;KAFF,MAGO,IAAIE,QAAQ,KAAK,CAAb,IAAkBH,aAAa,KAAKG,QAAQ,GAAG,CAAnD,EAAsD;;UAErDC,GAAG,GAAGJ,aAAa,GAAG,CAAC,CAAJ,GAAQ,CAAjC;MACAG,QAAQ,GAAGC,GAAG,GAAGH,MAAN,GAAeE,QAA1B;;;QAGIE,OAAO,GAAGprB,IAAI,CAACyQ,IAAL,CAAUzQ,IAAI,CAACumB,GAAL,CAAS2E,QAAT,IAAqBD,OAA/B,CAAhB;QACMI,MAAM,GAAGH,QAAQ,GAAGE,OAA1B;QACME,SAAS,GAAID,MAAM,GAAGJ,OAAV,GAAqBhD,KAArB,GAA6B0C,MAA/C;QACIY,MAAM,GAAGV,UAAb,CArBqD;;QAwBjDW,OAAO,GAAG,CAACxrB,IAAI,CAAComB,GAAL,CAASmF,MAAT,CAAD,GAAoBD,SAAlC;QACIG,OAAO,GAAGzrB,IAAI,CAACsmB,GAAL,CAASiF,MAAT,IAAmBD,SAAjC,CAzBqD;;QA4BjDI,EAAE,GAAGxiB,CAAC,GAAGlJ,IAAI,CAACsmB,GAAL,CAASiF,MAAT,IAAmBZ,MAAhC;QACIgB,EAAE,GAAG3G,CAAC,GAAGhlB,IAAI,CAAComB,GAAL,CAASmF,MAAT,IAAmBZ,MAAhC,CA7BqD;;SAgChDjG,MAAL,CAAYgH,EAAZ,EAAgBC,EAAhB;;SAEK,IAAIC,MAAM,GAAG,CAAlB,EAAqBA,MAAM,GAAGR,OAA9B,EAAuCQ,MAAM,EAA7C,EAAiD;;UAEzC/B,IAAI,GAAG6B,EAAE,GAAGF,OAAlB;UACM1B,IAAI,GAAG6B,EAAE,GAAGF,OAAlB,CAH+C;;MAM/CF,MAAM,IAAIF,MAAV,CAN+C;;MAS/CK,EAAE,GAAGxiB,CAAC,GAAGlJ,IAAI,CAACsmB,GAAL,CAASiF,MAAT,IAAmBZ,MAA5B;MACAgB,EAAE,GAAG3G,CAAC,GAAGhlB,IAAI,CAAComB,GAAL,CAASmF,MAAT,IAAmBZ,MAA5B,CAV+C;;MAa/Ca,OAAO,GAAG,CAACxrB,IAAI,CAAComB,GAAL,CAASmF,MAAT,CAAD,GAAoBD,SAA9B;MACAG,OAAO,GAAGzrB,IAAI,CAACsmB,GAAL,CAASiF,MAAT,IAAmBD,SAA7B,CAd+C;;UAiBzCvB,IAAI,GAAG2B,EAAE,GAAGF,OAAlB;UACMxB,IAAI,GAAG2B,EAAE,GAAGF,OAAlB,CAlB+C;;WAqB1C9G,aAAL,CAAmBkF,IAAnB,EAAyBC,IAAzB,EAA+BC,IAA/B,EAAqCC,IAArC,EAA2C0B,EAA3C,EAA+CC,EAA/C;;;WAGK,IAAP;GAnNW;;EAsNbE,OAAO,GAAY;sCAARC,MAAQ;MAARA,MAAQ;;;SACZpH,MAAL,CAAY,IAAIoH,MAAM,CAACC,KAAP,MAAkB,EAAtB,CAAZ;;SACK,IAAIC,KAAT,IAAkBF,MAAlB,EAA0B;WACnBhH,MAAL,CAAY,IAAIkH,KAAK,IAAI,EAAb,CAAZ;;;WAEK,KAAKjH,SAAL,EAAP;GA3NW;;EA8Nbf,IAAI,CAACA,IAAD,EAAO;IACTgE,OAAO,CAACje,KAAR,CAAc,IAAd,EAAoBia,IAApB;WACO,IAAP;GAhOW;;EAmObiI,YAAY,CAACC,IAAD,EAAO;QACb,YAAYC,IAAZ,CAAiBD,IAAjB,CAAJ,EAA4B;aACnB,GAAP;;;WAGK,EAAP;GAxOW;;EA2ObE,IAAI,CAACna,KAAD,EAAQia,IAAR,EAAc;QACZ,0BAA0BC,IAA1B,CAA+Bla,KAA/B,CAAJ,EAA2C;MACzCia,IAAI,GAAGja,KAAP;MACAA,KAAK,GAAG,IAAR;;;QAGEA,KAAJ,EAAW;WACJ0G,SAAL,CAAe1G,KAAf;;;WAEK,KAAK8D,UAAL,YAAoB,KAAKkW,YAAL,CAAkBC,IAAlB,CAApB,EAAP;GApPW;;EAuPb7W,MAAM,CAACpD,KAAD,EAAQ;QACRA,KAAJ,EAAW;WACJ8G,WAAL,CAAiB9G,KAAjB;;;WAEK,KAAK8D,UAAL,CAAgB,GAAhB,CAAP;GA3PW;;EA8PbsW,aAAa,CAAC1T,SAAD,EAAYI,WAAZ,EAAyBmT,IAAzB,EAA+B;QACtCnT,WAAW,IAAI,IAAnB,EAAyB;MACvBA,WAAW,GAAGJ,SAAd;;;QAEI2T,UAAU,GAAG,yBAAnB;;QACIA,UAAU,CAACH,IAAX,CAAgBxT,SAAhB,CAAJ,EAAgC;MAC9BuT,IAAI,GAAGvT,SAAP;MACAA,SAAS,GAAG,IAAZ;;;QAGE2T,UAAU,CAACH,IAAX,CAAgBpT,WAAhB,CAAJ,EAAkC;MAChCmT,IAAI,GAAGnT,WAAP;MACAA,WAAW,GAAGJ,SAAd;;;QAGEA,SAAJ,EAAe;WACRA,SAAL,CAAeA,SAAf;WACKI,WAAL,CAAiBA,WAAjB;;;WAGK,KAAKhD,UAAL,YAAoB,KAAKkW,YAAL,CAAkBC,IAAlB,CAApB,EAAP;GAlRW;;EAqRbK,IAAI,CAACL,IAAD,EAAO;WACF,KAAKnW,UAAL,YAAoB,KAAKkW,YAAL,CAAkBC,IAAlB,CAApB,QAAP;GAtRW;;EAyRbpa,SAAS,CAACS,GAAD,EAAMC,GAAN,EAAWC,GAAX,EAAgBC,GAAhB,EAAqBC,EAArB,EAAyBC,EAAzB,EAA6B;;QAEhCL,GAAG,KAAK,CAAR,IAAaC,GAAG,KAAK,CAArB,IAA0BC,GAAG,KAAK,CAAlC,IAAuCC,GAAG,KAAK,CAA/C,IAAoDC,EAAE,KAAK,CAA3D,IAAgEC,EAAE,KAAK,CAA3E,EAA8E;;aAErE,IAAP;;;QAEIE,CAAC,GAAG,KAAK8C,IAAf;QACM,CAACN,EAAD,EAAKC,EAAL,EAASC,EAAT,EAAaC,EAAb,EAAiBC,EAAjB,EAAqBC,EAArB,IAA2B7C,CAAjC;IACAA,CAAC,CAAC,CAAD,CAAD,GAAOwC,EAAE,GAAG/C,GAAL,GAAWiD,EAAE,GAAGhD,GAAvB;IACAM,CAAC,CAAC,CAAD,CAAD,GAAOyC,EAAE,GAAGhD,GAAL,GAAWkD,EAAE,GAAGjD,GAAvB;IACAM,CAAC,CAAC,CAAD,CAAD,GAAOwC,EAAE,GAAG7C,GAAL,GAAW+C,EAAE,GAAG9C,GAAvB;IACAI,CAAC,CAAC,CAAD,CAAD,GAAOyC,EAAE,GAAG9C,GAAL,GAAWgD,EAAE,GAAG/C,GAAvB;IACAI,CAAC,CAAC,CAAD,CAAD,GAAOwC,EAAE,GAAG3C,EAAL,GAAU6C,EAAE,GAAG5C,EAAf,GAAoB8C,EAA3B;IACA5C,CAAC,CAAC,CAAD,CAAD,GAAOyC,EAAE,GAAG5C,EAAL,GAAU8C,EAAE,GAAG7C,EAAf,GAAoB+C,EAA3B;QAEM6W,MAAM,GAAG,CAACja,GAAD,EAAMC,GAAN,EAAWC,GAAX,EAAgBC,GAAhB,EAAqBC,EAArB,EAAyBC,EAAzB,EAA6BjT,GAA7B,CAAiCuM,CAAC,IAAIpM,QAAM,CAACoM,CAAD,CAA5C,EAAiDzO,IAAjD,CAAsD,GAAtD,CAAf;WACO,KAAKsY,UAAL,WAAmByW,MAAnB,SAAP;GAzSW;;EA4SbC,SAAS,CAACvjB,CAAD,EAAI8b,CAAJ,EAAO;WACP,KAAKlT,SAAL,CAAe,CAAf,EAAkB,CAAlB,EAAqB,CAArB,EAAwB,CAAxB,EAA2B5I,CAA3B,EAA8B8b,CAA9B,CAAP;GA7SW;;EAgTb0H,MAAM,CAACC,KAAD,EAAsB;QAAdzwB,OAAc,uEAAJ,EAAI;QACtB8oB,CAAJ;QACM4H,GAAG,GAAID,KAAK,GAAG3sB,IAAI,CAACkmB,EAAd,GAAoB,GAAhC;QACMI,GAAG,GAAGtmB,IAAI,CAACsmB,GAAL,CAASsG,GAAT,CAAZ;QACMxG,GAAG,GAAGpmB,IAAI,CAAComB,GAAL,CAASwG,GAAT,CAAZ;QACI1jB,CAAC,GAAI8b,CAAC,GAAG,CAAb;;QAEI9oB,OAAO,CAAC2wB,MAAR,IAAkB,IAAtB,EAA4B;OACzB3jB,CAAD,EAAI8b,CAAJ,IAAS9oB,OAAO,CAAC2wB,MAAjB;UACM5W,EAAE,GAAG/M,CAAC,GAAGod,GAAJ,GAAUtB,CAAC,GAAGoB,GAAzB;UACMlQ,EAAE,GAAGhN,CAAC,GAAGkd,GAAJ,GAAUpB,CAAC,GAAGsB,GAAzB;MACApd,CAAC,IAAI+M,EAAL;MACA+O,CAAC,IAAI9O,EAAL;;;WAGK,KAAKpE,SAAL,CAAewU,GAAf,EAAoBF,GAApB,EAAyB,CAACA,GAA1B,EAA+BE,GAA/B,EAAoCpd,CAApC,EAAuC8b,CAAvC,CAAP;GA/TW;;EAkUb8H,KAAK,CAACC,OAAD,EAAUC,OAAV,EAAiC;QAAd9wB,OAAc,uEAAJ,EAAI;QAChC8oB,CAAJ;;QACIgI,OAAO,IAAI,IAAf,EAAqB;MACnBA,OAAO,GAAGD,OAAV;;;QAEE,OAAOC,OAAP,KAAmB,QAAvB,EAAiC;MAC/B9wB,OAAO,GAAG8wB,OAAV;MACAA,OAAO,GAAGD,OAAV;;;QAGE7jB,CAAC,GAAI8b,CAAC,GAAG,CAAb;;QACI9oB,OAAO,CAAC2wB,MAAR,IAAkB,IAAtB,EAA4B;OACzB3jB,CAAD,EAAI8b,CAAJ,IAAS9oB,OAAO,CAAC2wB,MAAjB;MACA3jB,CAAC,IAAI6jB,OAAO,GAAG7jB,CAAf;MACA8b,CAAC,IAAIgI,OAAO,GAAGhI,CAAf;;;WAGK,KAAKlT,SAAL,CAAeib,OAAf,EAAwB,CAAxB,EAA2B,CAA3B,EAA8BC,OAA9B,EAAuC9jB,CAAvC,EAA0C8b,CAA1C,CAAP;;;CAnVJ;;ACNA,IAAMiI,YAAY,GAAG;OACd,GADc;QAEb,GAFa;QAGb,GAHa;QAIb,GAJa;QAKb,GALa;QAMb,GANa;QAOb,GAPa;QAQb,GARa;QASb,GATa;QAUb,GAVa;QAWb,GAXa;QAYb,GAZa;QAab,GAba;QAcb,GAda;QAeb,GAfa;QAgBb,GAhBa;QAiBb,GAjBa;OAkBd,GAlBc;QAmBb,GAnBa;OAoBd,GApBc;OAqBd,GArBc;OAsBd,GAtBc;OAuBd,GAvBc;OAwBd,GAxBc;OAyBd,GAzBc;OA0Bd,GA1Bc;OA2Bd;CA3BP;AA8BA,IAAMC,UAAU,GAAG,q2GAwEjBC,KAxEiB,CAwEX,KAxEW,CAAnB;;AA0EA,MAAMC,OAAN,CAAc;SACLC,IAAP,CAAYC,QAAZ,EAAsB;WACb,IAAIF,OAAJ,CAAYG,EAAE,CAACC,YAAH,CAAgBF,QAAhB,EAA0B,MAA1B,CAAZ,CAAP;;;EAGFrxB,WAAW,CAACwxB,QAAD,EAAW;SACfA,QAAL,GAAgBA,QAAhB;SACKC,UAAL,GAAkB,EAAlB;SACKC,WAAL,GAAmB,EAAnB;SACKC,aAAL,GAAqB,EAArB;SACKC,SAAL,GAAiB,EAAjB;SAEK9J,KAAL,GAPoB;;SASf+J,UAAL,GAAkB,IAAIlwB,KAAJ,CAAU,GAAV,CAAlB;;SACK,IAAImwB,IAAI,GAAG,CAAhB,EAAmBA,IAAI,IAAI,GAA3B,EAAgCA,IAAI,EAApC,EAAwC;WACjCD,UAAL,CAAgBC,IAAhB,IAAwB,KAAKJ,WAAL,CAAiBT,UAAU,CAACa,IAAD,CAA3B,CAAxB;;;SAGGxU,IAAL,GAAY,KAAKmU,UAAL,CAAgB,UAAhB,EAA4BP,KAA5B,CAAkC,KAAlC,EAAyCxtB,GAAzC,CAA6CC,CAAC,IAAI,CAACA,CAAnD,CAAZ;SACKouB,QAAL,GAAgB,EAAE,KAAKN,UAAL,CAAgB,UAAhB,KAA+B,CAAjC,CAAhB;SACKO,SAAL,GAAiB,EAAE,KAAKP,UAAL,CAAgB,WAAhB,KAAgC,CAAlC,CAAjB;SACKQ,OAAL,GAAe,EAAE,KAAKR,UAAL,CAAgB,SAAhB,KAA8B,CAAhC,CAAf;SACKS,SAAL,GAAiB,EAAE,KAAKT,UAAL,CAAgB,WAAhB,KAAgC,CAAlC,CAAjB;SACKU,OAAL,GACE,KAAK7U,IAAL,CAAU,CAAV,IAAe,KAAKA,IAAL,CAAU,CAAV,CAAf,IAA+B,KAAKyU,QAAL,GAAgB,KAAKC,SAApD,CADF;;;EAIFlK,KAAK,GAAG;QACFsK,OAAO,GAAG,EAAd;;SACK,IAAIC,IAAT,IAAiB,KAAKb,QAAL,CAAcN,KAAd,CAAoB,IAApB,CAAjB,EAA4C;UACtCoB,KAAJ;UACI1xB,CAAJ;;UACK0xB,KAAK,GAAGD,IAAI,CAACC,KAAL,CAAW,aAAX,CAAb,EAAyC;QACvCF,OAAO,GAAGE,KAAK,CAAC,CAAD,CAAf;;OADF,MAGO,IAAKA,KAAK,GAAGD,IAAI,CAACC,KAAL,CAAW,WAAX,CAAb,EAAuC;QAC5CF,OAAO,GAAG,EAAV;;;;cAIMA,OAAR;aACO,aAAL;UACEE,KAAK,GAAGD,IAAI,CAACC,KAAL,CAAW,eAAX,CAAR;cACIjyB,GAAG,GAAGiyB,KAAK,CAAC,CAAD,CAAf;cACI/mB,KAAK,GAAG+mB,KAAK,CAAC,CAAD,CAAjB;;cAEK1xB,CAAC,GAAG,KAAK6wB,UAAL,CAAgBpxB,GAAhB,CAAT,EAAgC;gBAC1B,CAACsB,KAAK,CAAC6B,OAAN,CAAc5C,CAAd,CAAL,EAAuB;cACrBA,CAAC,GAAG,KAAK6wB,UAAL,CAAgBpxB,GAAhB,IAAuB,CAACO,CAAD,CAA3B;;;YAEFA,CAAC,CAACO,IAAF,CAAOoK,KAAP;WAJF,MAKO;iBACAkmB,UAAL,CAAgBpxB,GAAhB,IAAuBkL,KAAvB;;;;;aAIC,aAAL;cACM,CAAC,SAAS2kB,IAAT,CAAcmC,IAAd,CAAL,EAA0B;;;;cAGtBpV,IAAI,GAAGoV,IAAI,CAACC,KAAL,CAAW,oBAAX,EAAiC,CAAjC,CAAX;eACKZ,WAAL,CAAiBzU,IAAjB,IAAyB,CAACoV,IAAI,CAACC,KAAL,CAAW,kBAAX,EAA+B,CAA/B,CAA1B;;;aAGG,WAAL;UACEA,KAAK,GAAGD,IAAI,CAACC,KAAL,CAAW,sCAAX,CAAR;;cACIA,KAAJ,EAAW;iBACJV,SAAL,CAAeU,KAAK,CAAC,CAAD,CAAL,GAAW,IAAX,GAAkBA,KAAK,CAAC,CAAD,CAAtC,IAA6ClW,QAAQ,CAACkW,KAAK,CAAC,CAAD,CAAN,CAArD;;;;;;;;EAOVC,UAAU,CAACC,IAAD,EAAO;QACTC,GAAG,GAAG,EAAZ;;SACK,IAAIvwB,CAAC,GAAG,CAAR,EAAWwwB,GAAG,GAAGF,IAAI,CAACxxB,MAA3B,EAAmCkB,CAAC,GAAGwwB,GAAvC,EAA4CxwB,CAAC,EAA7C,EAAiD;UAC3C4vB,IAAI,GAAGU,IAAI,CAAC/vB,UAAL,CAAgBP,CAAhB,CAAX;MACA4vB,IAAI,GAAGd,YAAY,CAACc,IAAD,CAAZ,IAAsBA,IAA7B;MACAW,GAAG,CAACtxB,IAAJ,CAAS2wB,IAAI,CAACjyB,QAAL,CAAc,EAAd,CAAT;;;WAGK4yB,GAAP;;;EAGFE,eAAe,CAACpwB,MAAD,EAAS;QAChBqwB,MAAM,GAAG,EAAf;;SAEK,IAAI1wB,CAAC,GAAG,CAAR,EAAWwwB,GAAG,GAAGnwB,MAAM,CAACvB,MAA7B,EAAqCkB,CAAC,GAAGwwB,GAAzC,EAA8CxwB,CAAC,EAA/C,EAAmD;UAC3C2wB,QAAQ,GAAGtwB,MAAM,CAACE,UAAP,CAAkBP,CAAlB,CAAjB;MACA0wB,MAAM,CAACzxB,IAAP,CAAY,KAAK2xB,gBAAL,CAAsBD,QAAtB,CAAZ;;;WAGKD,MAAP;;;EAGFE,gBAAgB,CAAC9mB,SAAD,EAAY;WACnBilB,UAAU,CAACD,YAAY,CAAChlB,SAAD,CAAZ,IAA2BA,SAA5B,CAAV,IAAoD,SAA3D;;;EAGF+mB,YAAY,CAACC,KAAD,EAAQ;WACX,KAAKtB,WAAL,CAAiBsB,KAAjB,KAA2B,CAAlC;;;EAGFC,WAAW,CAACxtB,IAAD,EAAOE,KAAP,EAAc;WAChB,KAAKisB,SAAL,CAAensB,IAAI,GAAG,IAAP,GAAcE,KAA7B,KAAuC,CAA9C;;;EAGFutB,iBAAiB,CAACN,MAAD,EAAS;QAClBO,QAAQ,GAAG,EAAjB;;SAEK,IAAIje,KAAK,GAAG,CAAjB,EAAoBA,KAAK,GAAG0d,MAAM,CAAC5xB,MAAnC,EAA2CkU,KAAK,EAAhD,EAAoD;UAC5CzP,IAAI,GAAGmtB,MAAM,CAAC1d,KAAD,CAAnB;UACMvP,KAAK,GAAGitB,MAAM,CAAC1d,KAAK,GAAG,CAAT,CAApB;MACAie,QAAQ,CAAChyB,IAAT,CAAc,KAAK4xB,YAAL,CAAkBttB,IAAlB,IAA0B,KAAKwtB,WAAL,CAAiBxtB,IAAjB,EAAuBE,KAAvB,CAAxC;;;WAGKwtB,QAAP;;;;;AChOJ,MAAMC,OAAN,CAAc;EACZpzB,WAAW,GAAG;;EAEdkX,MAAM,GAAG;UACD,IAAIpX,KAAJ,CAAU,mCAAV,CAAN;;;EAGFuzB,aAAa,GAAG;UACR,IAAIvzB,KAAJ,CAAU,mCAAV,CAAN;;;EAGFyJ,GAAG,GAAG;WACG,KAAKG,UAAL,IAAmB,IAAnB,GACH,KAAKA,UADF,GAEF,KAAKA,UAAL,GAAkB,KAAKxF,QAAL,CAAcqF,GAAd,EAFvB;;;EAKF1E,QAAQ,GAAG;QACL,KAAK+Q,QAAL,IAAiB,KAAKlM,UAAL,IAAmB,IAAxC,EAA8C;;;;SAIzCkN,KAAL;WACQ,KAAKhB,QAAL,GAAgB,IAAxB;;;EAGFgB,KAAK,GAAG;UACA,IAAI9W,KAAJ,CAAU,mCAAV,CAAN;;;EAGFwzB,UAAU,CAACxqB,IAAD,EAAOyqB,UAAP,EAAmB;QACvBA,UAAU,IAAI,IAAlB,EAAwB;MACtBA,UAAU,GAAG,KAAb;;;QAEIC,GAAG,GAAGD,UAAU,GAAG,KAAKpB,OAAR,GAAkB,CAAxC;WACQ,CAAC,KAAKJ,QAAL,GAAgByB,GAAhB,GAAsB,KAAKxB,SAA5B,IAAyC,IAA1C,GAAkDlpB,IAAzD;;;;;AC9BJ,IAAM2qB,cAAc,GAAG;EACrBC,OAAO,GAAG;WACDpC,EAAE,CAACC,YAAH,CAAgBoC,SAAS,GAAG,mBAA5B,EAAiD,MAAjD,CAAP;GAFmB;;mBAIJ;WACRrC,EAAE,CAACC,YAAH,CAAgBoC,SAAS,GAAG,wBAA5B,EAAsD,MAAtD,CAAP;GALmB;;sBAOD;WACXrC,EAAE,CAACC,YAAH,CAAgBoC,SAAS,GAAG,2BAA5B,EAAyD,MAAzD,CAAP;GARmB;;0BAUG;WACfrC,EAAE,CAACC,YAAH,CAAgBoC,SAAS,GAAG,+BAA5B,EAA6D,MAA7D,CAAP;GAXmB;;EAarBC,SAAS,GAAG;WACHtC,EAAE,CAACC,YAAH,CAAgBoC,SAAS,GAAG,qBAA5B,EAAmD,MAAnD,CAAP;GAdmB;;qBAgBF;WACVrC,EAAE,CAACC,YAAH,CAAgBoC,SAAS,GAAG,0BAA5B,EAAwD,MAAxD,CAAP;GAjBmB;;wBAmBC;WACbrC,EAAE,CAACC,YAAH,CAAgBoC,SAAS,GAAG,6BAA5B,EAA2D,MAA3D,CAAP;GApBmB;;4BAsBK;WACjBrC,EAAE,CAACC,YAAH,CACLoC,SAAS,GAAG,iCADP,EAEL,MAFK,CAAP;GAvBmB;;kBA4BL;WACPrC,EAAE,CAACC,YAAH,CAAgBoC,SAAS,GAAG,uBAA5B,EAAqD,MAArD,CAAP;GA7BmB;;iBA+BN;WACNrC,EAAE,CAACC,YAAH,CAAgBoC,SAAS,GAAG,sBAA5B,EAAoD,MAApD,CAAP;GAhCmB;;mBAkCJ;WACRrC,EAAE,CAACC,YAAH,CAAgBoC,SAAS,GAAG,wBAA5B,EAAsD,MAAtD,CAAP;GAnCmB;;uBAqCA;WACZrC,EAAE,CAACC,YAAH,CAAgBoC,SAAS,GAAG,4BAA5B,EAA0D,MAA1D,CAAP;GAtCmB;;EAwCrBE,MAAM,GAAG;WACAvC,EAAE,CAACC,YAAH,CAAgBoC,SAAS,GAAG,kBAA5B,EAAgD,MAAhD,CAAP;GAzCmB;;EA2CrBG,YAAY,GAAG;WACNxC,EAAE,CAACC,YAAH,CAAgBoC,SAAS,GAAG,wBAA5B,EAAsD,MAAtD,CAAP;;;CA5CJ;;AAgDA,MAAMI,YAAN,SAA2BX,OAA3B,CAAmC;EACjCpzB,WAAW,CAACkE,QAAD,EAAW+Y,IAAX,EAAiB9Y,EAAjB,EAAqB;;SAEzBD,QAAL,GAAgBA,QAAhB;SACK+Y,IAAL,GAAYA,IAAZ;SACK9Y,EAAL,GAAUA,EAAV;SACK6vB,IAAL,GAAY,IAAI7C,OAAJ,CAAYsC,cAAc,CAAC,KAAKxW,IAAN,CAAd,EAAZ,CAAZ;KACC;MACC8U,QAAQ,EAAE,KAAKA,QADhB;MAECC,SAAS,EAAE,KAAKA,SAFjB;MAGC1U,IAAI,EAAE,KAAKA,IAHZ;MAIC6U,OAAO,EAAE,KAAKA,OAJf;MAKCF,OAAO,EAAE,KAAKA,OALf;MAMCC,SAAS,EAAE,KAAKA;QACd,KAAK8B,IAPT;;;EAUFpd,KAAK,GAAG;SACDlN,UAAL,CAAgBtF,IAAhB,GAAuB;MACrBuF,IAAI,EAAE,MADe;MAErBsqB,QAAQ,EAAE,KAAKhX,IAFM;MAGrB7E,OAAO,EAAE,OAHY;MAIrB8b,QAAQ,EAAE;KAJZ;WAOO,KAAKxqB,UAAL,CAAgBvH,GAAhB,EAAP;;;EAGF+U,MAAM,CAACsb,IAAD,EAAO;QACL2B,OAAO,GAAG,KAAKH,IAAL,CAAUzB,UAAV,CAAqBC,IAArB,CAAhB;QACMI,MAAM,GAAG,KAAKoB,IAAL,CAAUrB,eAAV,WAA6BH,IAA7B,EAAf;QACMW,QAAQ,GAAG,KAAKa,IAAL,CAAUd,iBAAV,CAA4BN,MAA5B,CAAjB;QACMwB,SAAS,GAAG,EAAlB;;SACK,IAAIlyB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG0wB,MAAM,CAAC5xB,MAA3B,EAAmCkB,CAAC,EAApC,EAAwC;UAChC8wB,KAAK,GAAGJ,MAAM,CAAC1wB,CAAD,CAApB;MACAkyB,SAAS,CAACjzB,IAAV,CAAe;QACbkzB,QAAQ,EAAElB,QAAQ,CAACjxB,CAAD,CADL;QAEboyB,QAAQ,EAAE,CAFG;QAGbC,OAAO,EAAE,CAHI;QAIbC,OAAO,EAAE,CAJI;QAKbC,YAAY,EAAE,KAAKT,IAAL,CAAUjB,YAAV,CAAuBC,KAAvB;OALhB;;;WASK,CAACmB,OAAD,EAAUC,SAAV,CAAP;;;EAGFf,aAAa,CAAC9wB,MAAD,EAASuG,IAAT,EAAe;QACpB8pB,MAAM,GAAG,KAAKoB,IAAL,CAAUrB,eAAV,WAA6BpwB,MAA7B,EAAf;QACM4wB,QAAQ,GAAG,KAAKa,IAAL,CAAUd,iBAAV,CAA4BN,MAA5B,CAAjB;QAEIxpB,KAAK,GAAG,CAAZ;;SACK,IAAIsrB,OAAT,IAAoBvB,QAApB,EAA8B;MAC5B/pB,KAAK,IAAIsrB,OAAT;;;QAGI7D,KAAK,GAAG/nB,IAAI,GAAG,IAArB;WACOM,KAAK,GAAGynB,KAAf;;;SAGK8D,cAAP,CAAsB1X,IAAtB,EAA4B;WACnBA,IAAI,IAAIwW,cAAf;;;;;AChHJ,IAAMmB,KAAK,GAAG,SAARA,KAAQ,CAASC,GAAT,EAAc;SACnB,cAAOA,GAAG,CAACh1B,QAAJ,CAAa,EAAb,CAAP,EAA0B+B,KAA1B,CAAgC,CAAC,CAAjC,CAAP;CADF;;AAIA,MAAMkzB,YAAN,SAA2B1B,OAA3B,CAAmC;EACjCpzB,WAAW,CAACkE,QAAD,EAAW8vB,IAAX,EAAiB7vB,EAAjB,EAAqB;;SAEzBD,QAAL,GAAgBA,QAAhB;SACK8vB,IAAL,GAAYA,IAAZ;SACK7vB,EAAL,GAAUA,EAAV;SACK4wB,MAAL,GAAc,KAAKf,IAAL,CAAUgB,YAAV,EAAd;SACKC,OAAL,GAAe,CAAC,CAAC,CAAD,CAAD,CAAf;SACKC,MAAL,GAAc,CAAC,KAAKlB,IAAL,CAAUmB,QAAV,CAAmB,CAAnB,EAAsBV,YAAvB,CAAd;SAEKxX,IAAL,GAAY,KAAK+W,IAAL,CAAUoB,cAAtB;SACKvE,KAAL,GAAa,OAAO,KAAKmD,IAAL,CAAUqB,UAA9B;SACKtD,QAAL,GAAgB,KAAKiC,IAAL,CAAUsB,MAAV,GAAmB,KAAKzE,KAAxC;SACKmB,SAAL,GAAiB,KAAKgC,IAAL,CAAUuB,OAAV,GAAoB,KAAK1E,KAA1C;SACKoB,OAAL,GAAe,KAAK+B,IAAL,CAAU/B,OAAV,GAAoB,KAAKpB,KAAxC;SACKqB,SAAL,GAAiB,KAAK8B,IAAL,CAAU9B,SAAV,GAAsB,KAAKrB,KAA5C;SACKsB,OAAL,GAAe,KAAK6B,IAAL,CAAU7B,OAAV,GAAoB,KAAKtB,KAAxC;SACKvT,IAAL,GAAY,KAAK0W,IAAL,CAAU1W,IAAtB;;QAEIpZ,QAAQ,CAACjE,OAAT,CAAiBu1B,eAAjB,KAAqC,KAAzC,EAAgD;WACzCC,WAAL,GAAmBh1B,MAAM,CAAC+O,MAAP,CAAc,IAAd,CAAnB;;;;EAIJkmB,SAAS,CAAClD,IAAD,EAAOmD,QAAP,EAAiB;QAClBC,GAAG,GAAG,KAAK5B,IAAL,CAAUjrB,MAAV,CAAiBypB,IAAjB,EAAuBmD,QAAvB,CAAZ,CADwB;;SAInB,IAAIzzB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG0zB,GAAG,CAACxB,SAAJ,CAAcpzB,MAAlC,EAA0CkB,CAAC,EAA3C,EAA+C;UACvC2zB,QAAQ,GAAGD,GAAG,CAACxB,SAAJ,CAAclyB,CAAd,CAAjB;;WACK,IAAI7B,GAAT,IAAgBw1B,QAAhB,EAA0B;QACxBA,QAAQ,CAACx1B,GAAD,CAAR,IAAiB,KAAKwwB,KAAtB;;;MAGFgF,QAAQ,CAACpB,YAAT,GAAwBmB,GAAG,CAAChD,MAAJ,CAAW1wB,CAAX,EAAcuyB,YAAd,GAA6B,KAAK5D,KAA1D;;;WAGK+E,GAAP;;;EAGFE,YAAY,CAACtD,IAAD,EAAO;QACb,CAAC,KAAKiD,WAAV,EAAuB;aACd,KAAKC,SAAL,CAAelD,IAAf,CAAP;;;QAEEuD,MAAJ;;QACKA,MAAM,GAAG,KAAKN,WAAL,CAAiBjD,IAAjB,CAAd,EAAuC;aAC9BuD,MAAP;;;QAGIH,GAAG,GAAG,KAAKF,SAAL,CAAelD,IAAf,CAAZ;SACKiD,WAAL,CAAiBjD,IAAjB,IAAyBoD,GAAzB;WACOA,GAAP;;;EAGF7sB,MAAM,CAACypB,IAAD,EAAOmD,QAAP,EAAiBK,SAAjB,EAA4B;;QAE5BL,QAAJ,EAAc;aACL,KAAKD,SAAL,CAAelD,IAAf,EAAqBmD,QAArB,CAAP;;;QAGE/C,MAAM,GAAGoD,SAAS,GAAG,IAAH,GAAU,EAAhC;QACI5B,SAAS,GAAG4B,SAAS,GAAG,IAAH,GAAU,EAAnC;QACIvB,YAAY,GAAG,CAAnB,CARgC;;;QAY5BvzB,IAAI,GAAG,CAAX;QACIgU,KAAK,GAAG,CAAZ;;WACOA,KAAK,IAAIsd,IAAI,CAACxxB,MAArB,EAA6B;UACvBi1B,MAAJ;;UAEG/gB,KAAK,KAAKsd,IAAI,CAACxxB,MAAf,IAAyBE,IAAI,GAAGgU,KAAjC,KACE+gB,MAAM,GAAGzD,IAAI,CAACtW,MAAL,CAAYhH,KAAZ,CAAV,EAA+B,CAAC,GAAD,EAAM,IAAN,EAAYoT,QAAZ,CAAqB2N,MAArB,CADhC,CADF,EAGE;YACML,GAAG,GAAG,KAAKE,YAAL,CAAkBtD,IAAI,CAAC5wB,KAAL,CAAWV,IAAX,EAAiB,EAAEgU,KAAnB,CAAlB,CAAZ;;YACI,CAAC8gB,SAAL,EAAgB;UACdpD,MAAM,GAAGA,MAAM,CAAC1tB,MAAP,CAAc0wB,GAAG,CAAChD,MAAlB,CAAT;UACAwB,SAAS,GAAGA,SAAS,CAAClvB,MAAV,CAAiB0wB,GAAG,CAACxB,SAArB,CAAZ;;;QAGFK,YAAY,IAAImB,GAAG,CAACnB,YAApB;QACAvzB,IAAI,GAAGgU,KAAP;OAXF,MAYO;QACLA,KAAK;;;;WAIF;MAAE0d,MAAF;MAAUwB,SAAV;MAAqBK;KAA5B;;;EAGFvd,MAAM,CAACsb,IAAD,EAAOmD,QAAP,EAAiB;QACf;MAAE/C,MAAF;MAAUwB;QAAc,KAAKrrB,MAAL,CAAYypB,IAAZ,EAAkBmD,QAAlB,CAA9B;QAEMlD,GAAG,GAAG,EAAZ;;SACK,IAAIvwB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG0wB,MAAM,CAAC5xB,MAA3B,EAAmCkB,CAAC,EAApC,EAAwC;UAChC8wB,KAAK,GAAGJ,MAAM,CAAC1wB,CAAD,CAApB;UACMg0B,GAAG,GAAG,KAAKnB,MAAL,CAAYoB,YAAZ,CAAyBnD,KAAK,CAAC7uB,EAA/B,CAAZ;MACAsuB,GAAG,CAACtxB,IAAJ,CAAS,cAAO+0B,GAAG,CAACr2B,QAAJ,CAAa,EAAb,CAAP,EAA0B+B,KAA1B,CAAgC,CAAC,CAAjC,CAAT;;UAEI,KAAKszB,MAAL,CAAYgB,GAAZ,KAAoB,IAAxB,EAA8B;aACvBhB,MAAL,CAAYgB,GAAZ,IAAmBlD,KAAK,CAACyB,YAAN,GAAqB,KAAK5D,KAA7C;;;UAEE,KAAKoE,OAAL,CAAaiB,GAAb,KAAqB,IAAzB,EAA+B;aACxBjB,OAAL,CAAaiB,GAAb,IAAoBlD,KAAK,CAACoD,UAA1B;;;;WAIG,CAAC3D,GAAD,EAAM2B,SAAN,CAAP;;;EAGFf,aAAa,CAAC9wB,MAAD,EAASuG,IAAT,EAAe6sB,QAAf,EAAyB;QAC9BvsB,KAAK,GAAG,KAAKL,MAAL,CAAYxG,MAAZ,EAAoBozB,QAApB,EAA8B,IAA9B,EAAoClB,YAAlD;QACM5D,KAAK,GAAG/nB,IAAI,GAAG,IAArB;WACOM,KAAK,GAAGynB,KAAf;;;EAGFja,KAAK,GAAG;QACAyf,KAAK,GAAG,KAAKtB,MAAL,CAAYuB,GAAZ,IAAmB,IAAjC;QACMC,QAAQ,GAAG,KAAKryB,QAAL,CAAcqF,GAAd,EAAjB;;QAEI8sB,KAAJ,EAAW;MACTE,QAAQ,CAACnyB,IAAT,CAAcgU,OAAd,GAAwB,eAAxB;;;SAGG2c,MAAL,CACGyB,YADH,GAEGC,EAFH,CAEM,MAFN,EAEcryB,IAAI,IAAImyB,QAAQ,CAAC7xB,KAAT,CAAeN,IAAf,CAFtB,EAGGqyB,EAHH,CAGM,KAHN,EAGa,MAAMF,QAAQ,CAACp0B,GAAT,EAHnB;QAKMu0B,WAAW,GACf,CAAC,CAAC,KAAK1C,IAAL,CAAU,MAAV,KAAqB,IAArB,GACE,KAAKA,IAAL,CAAU,MAAV,EAAkB2C,YADpB,GAEEC,SAFH,KAEiB,CAFlB,KAEwB,CAH1B;QAIIC,KAAK,GAAG,CAAZ;;QACI,KAAK7C,IAAL,CAAU8C,IAAV,CAAeC,YAAnB,EAAiC;MAC/BF,KAAK,IAAI,KAAK,CAAd;;;QAEE,KAAKH,WAAL,IAAoBA,WAAW,IAAI,CAAvC,EAA0C;MACxCG,KAAK,IAAI,KAAK,CAAd;;;IAEFA,KAAK,IAAI,KAAK,CAAd,CAxBM;;QAyBFH,WAAW,KAAK,EAApB,EAAwB;MACtBG,KAAK,IAAI,KAAK,CAAd;;;QAEE,KAAK7C,IAAL,CAAUgD,IAAV,CAAeC,QAAf,CAAwBC,MAA5B,EAAoC;MAClCL,KAAK,IAAI,KAAK,CAAd;KA7BI;;;QAiCAM,GAAG,GAAG,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,EAAU,CAAV,EAAa,CAAb,EAAgB,CAAhB,EACTzzB,GADS,CACLxB,CAAC,IAAII,MAAM,CAAC80B,YAAP,CAAoB,CAAC,KAAKjzB,EAAL,CAAQ1B,UAAR,CAAmBP,CAAnB,KAAyB,EAA1B,IAAgC,EAApD,CADA,EAETV,IAFS,CAEJ,EAFI,CAAZ;QAGMyb,IAAI,GAAGka,GAAG,GAAG,GAAN,GAAY,KAAKnD,IAAL,CAAUoB,cAAnC;QAEM;MAAE9X;QAAS,KAAK0W,IAAtB;QACMqD,UAAU,GAAG,KAAKnzB,QAAL,CAAcqF,GAAd,CAAkB;MACnCI,IAAI,EAAE,gBAD6B;MAEnC2tB,QAAQ,EAAEra,IAFyB;MAGnCsa,KAAK,EAAEV,KAH4B;MAInCW,QAAQ,EAAE,CACRla,IAAI,CAACma,IAAL,GAAY,KAAK5G,KADT,EAERvT,IAAI,CAACoa,IAAL,GAAY,KAAK7G,KAFT,EAGRvT,IAAI,CAACqa,IAAL,GAAY,KAAK9G,KAHT,EAIRvT,IAAI,CAACpS,IAAL,GAAY,KAAK2lB,KAJT,CAJyB;MAUnC+G,WAAW,EAAE,KAAK5D,IAAL,CAAU6D,WAVY;MAWnCC,MAAM,EAAE,KAAK/F,QAXsB;MAYnCgG,OAAO,EAAE,KAAK/F,SAZqB;MAanCgG,SAAS,EAAE,CAAC,KAAKhE,IAAL,CAAU9B,SAAV,IAAuB,KAAK8B,IAAL,CAAUsB,MAAlC,IAA4C,KAAKzE,KAbzB;MAcnCoH,OAAO,EAAE,CAAC,KAAKjE,IAAL,CAAU/B,OAAV,IAAqB,CAAtB,IAA2B,KAAKpB,KAdN;MAenCqH,KAAK,EAAE;KAfU,CAAnB,CAvCM;;QAyDF7B,KAAJ,EAAW;MACTgB,UAAU,CAACjzB,IAAX,CAAgB+zB,SAAhB,GAA4B5B,QAA5B;KADF,MAEO;MACLc,UAAU,CAACjzB,IAAX,CAAgBg0B,SAAhB,GAA4B7B,QAA5B;;;QAGE,KAAKryB,QAAL,CAAc6wB,MAAlB,EAA0B;UAClBsD,MAAM,GAAG11B,MAAM,CAACC,IAAP,CAAY,YAAZ,EAA0B,KAA1B,CAAf;UACM01B,SAAS,GAAG,KAAKp0B,QAAL,CAAcqF,GAAd,EAAlB;MACA+uB,SAAS,CAAC5zB,KAAV,CAAgB2zB,MAAhB;MACAC,SAAS,CAACn2B,GAAV;MAEAk1B,UAAU,CAACjzB,IAAX,CAAgBi0B,MAAhB,GAAyBC,SAAzB;;;IAGFjB,UAAU,CAACl1B,GAAX;QAEMo2B,kBAAkB,GAAG;MACzB5uB,IAAI,EAAE,MADmB;MAEzByO,OAAO,EAAE,cAFgB;MAGzB6b,QAAQ,EAAEhX,IAHe;MAIzBub,aAAa,EAAE;QACbC,QAAQ,EAAE,IAAIn2B,MAAJ,CAAW,OAAX,CADG;QAEbo2B,QAAQ,EAAE,IAAIp2B,MAAJ,CAAW,UAAX,CAFG;QAGbq2B,UAAU,EAAE;OAPW;MASzBC,cAAc,EAAEvB,UATS;MAUzBwB,CAAC,EAAE,CAAC,CAAD,EAAI,KAAK3D,MAAT;KAVL;;QAaI,CAACmB,KAAL,EAAY;MACVkC,kBAAkB,CAACngB,OAAnB,GAA6B,cAA7B;MACAmgB,kBAAkB,CAACO,WAAnB,GAAiC,UAAjC;;;QAGIC,cAAc,GAAG,KAAK70B,QAAL,CAAcqF,GAAd,CAAkBgvB,kBAAlB,CAAvB;IAEAQ,cAAc,CAAC52B,GAAf;SAEKuH,UAAL,CAAgBtF,IAAhB,GAAuB;MACrBuF,IAAI,EAAE,MADe;MAErByO,OAAO,EAAE,OAFY;MAGrB6b,QAAQ,EAAEhX,IAHW;MAIrBiX,QAAQ,EAAE,YAJW;MAKrB8E,eAAe,EAAE,CAACD,cAAD,CALI;MAMrBE,SAAS,EAAE,KAAKC,aAAL;KANb;WASO,KAAKxvB,UAAL,CAAgBvH,GAAhB,EAAP;GA7N+B;;;;;EAmOjC+2B,aAAa,GAAG;QACRC,IAAI,GAAG,KAAKj1B,QAAL,CAAcqF,GAAd,EAAb;QAEM6vB,OAAO,GAAG,EAAhB;;SACK,IAAIhD,UAAT,IAAuB,KAAKnB,OAA5B,EAAqC;UAC7Bd,OAAO,GAAG,EAAhB,CADmC;;WAI9B,IAAI5oB,KAAT,IAAkB6qB,UAAlB,EAA8B;YACxB7qB,KAAK,GAAG,MAAZ,EAAoB;UAClBA,KAAK,IAAI,OAAT;UACA4oB,OAAO,CAAChzB,IAAR,CAAayzB,KAAK,CAAGrpB,KAAK,KAAK,EAAX,GAAiB,KAAlB,GAA2B,MAA5B,CAAlB;UACAA,KAAK,GAAG,SAAUA,KAAK,GAAG,KAA1B;;;QAGF4oB,OAAO,CAAChzB,IAAR,CAAayzB,KAAK,CAACrpB,KAAD,CAAlB;;;MAGF6tB,OAAO,CAACj4B,IAAR,YAAiBgzB,OAAO,CAAC3yB,IAAR,CAAa,GAAb,CAAjB;;;QAGI63B,SAAS,GAAG,GAAlB;QACMC,MAAM,GAAGv1B,IAAI,CAACyQ,IAAL,CAAU4kB,OAAO,CAACp4B,MAAR,GAAiBq4B,SAA3B,CAAf;QACME,MAAM,GAAG,EAAf;;SACK,IAAIr3B,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGo3B,MAApB,EAA4Bp3B,CAAC,EAA7B,EAAiC;UACzBs3B,KAAK,GAAGt3B,CAAC,GAAGm3B,SAAlB;UACMl3B,GAAG,GAAG4B,IAAI,CAACoP,GAAL,CAAS,CAACjR,CAAC,GAAG,CAAL,IAAUm3B,SAAnB,EAA8BD,OAAO,CAACp4B,MAAtC,CAAZ;MACAu4B,MAAM,CAACp4B,IAAP,YAAgByzB,KAAK,CAAC4E,KAAD,CAArB,gBAAkC5E,KAAK,CAACzyB,GAAG,GAAG,CAAP,CAAvC,gBAAsDi3B,OAAO,CAACx3B,KAAR,CAAc43B,KAAd,EAAqBr3B,GAArB,EAA0BX,IAA1B,CAA+B,GAA/B,CAAtD;;;IAGF23B,IAAI,CAACh3B,GAAL,2RAeFo3B,MAAM,CAAC/3B,IAAP,CAAY,IAAZ,CAfE;WAuBO23B,IAAP;;;;;ACzRJ,MAAMM,cAAN,CAAqB;SACZrI,IAAP,CAAYltB,QAAZ,EAAsBw1B,GAAtB,EAA2BC,MAA3B,EAAmCx1B,EAAnC,EAAuC;QACjC6vB,IAAJ;;QACI,OAAO0F,GAAP,KAAe,QAAnB,EAA6B;UACvB3F,YAAY,CAACY,cAAb,CAA4B+E,GAA5B,CAAJ,EAAsC;eAC7B,IAAI3F,YAAJ,CAAiB7vB,QAAjB,EAA2Bw1B,GAA3B,EAAgCv1B,EAAhC,CAAP;;;MAGFu1B,GAAG,GAAGpI,EAAE,CAACC,YAAH,CAAgBmI,GAAhB,CAAN;;;QAEE/2B,MAAM,CAACK,QAAP,CAAgB02B,GAAhB,CAAJ,EAA0B;MACxB1F,IAAI,GAAG4F,OAAO,CAACpqB,MAAR,CAAekqB,GAAf,EAAoBC,MAApB,CAAP;KADF,MAEO,IAAID,GAAG,YAAYG,UAAnB,EAA+B;MACpC7F,IAAI,GAAG4F,OAAO,CAACpqB,MAAR,CAAe7M,MAAM,CAACC,IAAP,CAAY82B,GAAZ,CAAf,EAAiCC,MAAjC,CAAP;KADK,MAEA,IAAID,GAAG,YAAYI,WAAnB,EAAgC;MACrC9F,IAAI,GAAG4F,OAAO,CAACpqB,MAAR,CAAe7M,MAAM,CAACC,IAAP,CAAY,IAAIi3B,UAAJ,CAAeH,GAAf,CAAZ,CAAf,EAAiDC,MAAjD,CAAP;;;QAGE3F,IAAI,IAAI,IAAZ,EAAkB;YACV,IAAIl0B,KAAJ,CAAU,mDAAV,CAAN;;;WAGK,IAAIg1B,YAAJ,CAAiB5wB,QAAjB,EAA2B8vB,IAA3B,EAAiC7vB,EAAjC,CAAP;;;;;ACzBJ,iBAAe;EACb41B,SAAS,GAA4B;QAA3BC,WAA2B,uEAAb,WAAa;;SAE9BC,aAAL,GAAqB,EAArB;SACKC,UAAL,GAAkB,CAAlB,CAHmC;;SAM9BC,SAAL,GAAiB,EAAjB;SACKC,KAAL,GAAa,IAAb;SAEKC,gBAAL,GAAwB,EAAxB,CATmC;;QAY/BL,WAAJ,EAAiB;WACVhG,IAAL,CAAUgG,WAAV;;GAdS;;EAkBbhG,IAAI,CAAC0F,GAAD,EAAMC,MAAN,EAAc7wB,IAAd,EAAoB;QAClBwxB,QAAJ,EAActG,IAAd;;QACI,OAAO2F,MAAP,KAAkB,QAAtB,EAAgC;MAC9B7wB,IAAI,GAAG6wB,MAAP;MACAA,MAAM,GAAG,IAAT;KAJoB;;;QAQlB,OAAOD,GAAP,KAAe,QAAf,IAA2B,KAAKW,gBAAL,CAAsBX,GAAtB,CAA/B,EAA2D;MACzDY,QAAQ,GAAGZ,GAAX;OACC;QAAEA,GAAF;QAAOC;UAAW,KAAKU,gBAAL,CAAsBX,GAAtB,CAAnB;KAFF,MAGO;MACLY,QAAQ,GAAGX,MAAM,IAAID,GAArB;;UACI,OAAOY,QAAP,KAAoB,QAAxB,EAAkC;QAChCA,QAAQ,GAAG,IAAX;;;;QAIAxxB,IAAI,IAAI,IAAZ,EAAkB;WACXyxB,QAAL,CAAczxB,IAAd;KAnBoB;;;QAuBjBkrB,IAAI,GAAG,KAAKiG,aAAL,CAAmBK,QAAnB,CAAZ,EAA2C;WACpCF,KAAL,GAAapG,IAAb;aACO,IAAP;KAzBoB;;;QA6BhB7vB,EAAE,cAAO,EAAE,KAAK+1B,UAAd,CAAR;SACKE,KAAL,GAAaX,cAAc,CAACrI,IAAf,CAAoB,IAApB,EAA0BsI,GAA1B,EAA+BC,MAA/B,EAAuCx1B,EAAvC,CAAb,CA9BsB;;;QAkCjB6vB,IAAI,GAAG,KAAKiG,aAAL,CAAmB,KAAKG,KAAL,CAAWnd,IAA9B,CAAZ,EAAkD;WAC3Cmd,KAAL,GAAapG,IAAb;aACO,IAAP;KApCoB;;;QAwClBsG,QAAJ,EAAc;WACPL,aAAL,CAAmBK,QAAnB,IAA+B,KAAKF,KAApC;;;QAGE,KAAKA,KAAL,CAAWnd,IAAf,EAAqB;WACdgd,aAAL,CAAmB,KAAKG,KAAL,CAAWnd,IAA9B,IAAsC,KAAKmd,KAA3C;;;WAGK,IAAP;GAlEW;;EAqEbG,QAAQ,CAACJ,SAAD,EAAY;SACbA,SAAL,GAAiBA,SAAjB;WACO,IAAP;GAvEW;;EA0EbK,iBAAiB,CAACjH,UAAD,EAAa;QACxBA,UAAU,IAAI,IAAlB,EAAwB;MACtBA,UAAU,GAAG,KAAb;;;WAEK,KAAK6G,KAAL,CAAW9G,UAAX,CAAsB,KAAK6G,SAA3B,EAAsC5G,UAAtC,CAAP;GA9EW;;EAiFbkH,YAAY,CAACxd,IAAD,EAAOyc,GAAP,EAAYC,MAAZ,EAAoB;SACzBU,gBAAL,CAAsBpd,IAAtB,IAA8B;MAC5Byc,GAD4B;MAE5BC;KAFF;WAKO,IAAP;;;CAvFJ;;ACCA,IAAMe,WAAW,GAAG,QAApB;AACA,IAAMC,MAAM,GAAG,GAAf;;AAEA,MAAMC,WAAN,SAA0BC,YAA1B,CAAuC;EACrC76B,WAAW,CAACkE,QAAD,EAAWjE,OAAX,EAAoB;;SAExBiE,QAAL,GAAgBA,QAAhB;SACK42B,MAAL,GAAc76B,OAAO,CAAC66B,MAAR,IAAkB,CAAhC;SACKC,gBAAL,GAAwB96B,OAAO,CAAC86B,gBAAR,IAA4B,CAApD;SACKC,WAAL,GAAmB/6B,OAAO,CAAC+6B,WAAR,KAAwB,CAA3C;SACKC,OAAL,GAAeh7B,OAAO,CAACg7B,OAAR,IAAmB,CAAlC;SACKC,SAAL,GAAiBj7B,OAAO,CAACi7B,SAAR,IAAqB,IAArB,GAA4Bj7B,OAAO,CAACi7B,SAApC,GAAgD,EAAjE,CAP6B;;SAQxB5O,SAAL,GACE,CAACrsB,OAAO,CAACmJ,KAAR,GAAgB,KAAK8xB,SAAL,IAAkB,KAAKD,OAAL,GAAe,CAAjC,CAAjB,IAAwD,KAAKA,OAD/D;SAEKE,SAAL,GAAiB,KAAK7O,SAAtB;SACK8O,MAAL,GAAc,KAAKl3B,QAAL,CAAc+I,CAA5B;SACKouB,MAAL,GAAc,KAAKn3B,QAAL,CAAc6kB,CAA5B;SACKuS,MAAL,GAAc,CAAd;SACKC,QAAL,GAAgBt7B,OAAO,CAACs7B,QAAxB;SACKC,UAAL,GAAkB,CAAlB;SACK7F,QAAL,GAAgB11B,OAAO,CAAC01B,QAAxB,CAhB6B;;QAmBzB11B,OAAO,CAACoJ,MAAR,IAAkB,IAAtB,EAA4B;WACrBA,MAAL,GAAcpJ,OAAO,CAACoJ,MAAtB;WACK6B,IAAL,GAAY,KAAKmwB,MAAL,GAAcp7B,OAAO,CAACoJ,MAAlC;KAFF,MAGO;WACA6B,IAAL,GAAY,KAAKhH,QAAL,CAAcgU,IAAd,CAAmBhN,IAAnB,EAAZ;KAvB2B;;;SA2BxBurB,EAAL,CAAQ,WAAR,EAAqBx2B,OAAO,IAAI;;;;UAIxB66B,MAAM,GAAG,KAAKU,UAAL,IAAmB,KAAKV,MAAvC;WACK52B,QAAL,CAAc+I,CAAd,IAAmB6tB,MAAnB;WACKxO,SAAL,IAAkBwO,MAAlB;aAEO,KAAKW,IAAL,CAAU,MAAV,EAAkB,MAAM;aACxBv3B,QAAL,CAAc+I,CAAd,IAAmB6tB,MAAnB;aACKxO,SAAL,IAAkBwO,MAAlB;;YACI76B,OAAO,CAACy7B,SAAR,IAAqB,CAAC,KAAKF,UAA/B,EAA2C;eACpCA,UAAL,GAAkB,KAAKV,MAAvB;;;YAEE,CAAC76B,OAAO,CAACy7B,SAAb,EAAwB;iBACd,KAAKF,UAAL,GAAkB,CAA1B;;OAPG,CAAP;KARF,EA3B6B;;SAgDxB/E,EAAL,CAAQ,UAAR,EAAoBx2B,OAAO,IAAI;UACvB;QAAE07B;UAAU17B,OAAlB;;UACI07B,KAAK,KAAK,SAAd,EAAyB;QACvB17B,OAAO,CAAC07B,KAAR,GAAgB,MAAhB;;;WAEGC,QAAL,GAAgB,IAAhB;aAEO,KAAKH,IAAL,CAAU,MAAV,EAAkB,MAAM;aACxBv3B,QAAL,CAAc6kB,CAAd,IAAmB9oB,OAAO,CAAC47B,YAAR,IAAwB,CAA3C;QACA57B,OAAO,CAAC07B,KAAR,GAAgBA,KAAhB;eACQ,KAAKC,QAAL,GAAgB,KAAxB;OAHK,CAAP;KAPF;;;EAeFE,SAAS,CAACC,IAAD,EAAO;WAEZ,KAAK73B,QAAL,CAAcmvB,aAAd,CAA4B0I,IAA5B,EAAkC,IAAlC,IACA,KAAKhB,gBADL,GAEA,KAAKC,WAHP;;;EAOFgB,MAAM,CAACD,IAAD,EAAOxP,CAAP,EAAU;QACVwP,IAAI,CAACA,IAAI,CAAC/6B,MAAL,GAAc,CAAf,CAAJ,IAAyB05B,WAA7B,EAA0C;aACjCnO,CAAC,IAAI,KAAK4O,SAAjB;;;WAEK5O,CAAC,GAAG,KAAKuP,SAAL,CAAenB,MAAf,CAAJ,IAA8B,KAAKQ,SAA1C;;;EAGFc,QAAQ,CAACzJ,IAAD,EAAO1b,EAAP,EAAW;;QAEbolB,EAAJ;QACMC,OAAO,GAAG,IAAIC,WAAJ,CAAgB5J,IAAhB,CAAhB;QACItxB,IAAI,GAAG,IAAX;QACMm7B,UAAU,GAAG57B,MAAM,CAAC+O,MAAP,CAAc,IAAd,CAAnB;;WAEQ0sB,EAAE,GAAGC,OAAO,CAACG,SAAR,EAAb,EAAmC;UAC7BC,cAAJ;UACIR,IAAI,GAAGvJ,IAAI,CAAC5wB,KAAL,CACT,CAACV,IAAI,IAAI,IAAR,GAAeA,IAAI,CAAC20B,QAApB,GAA+Be,SAAhC,KAA8C,CADrC,EAETsF,EAAE,CAACrG,QAFM,CAAX;UAIItJ,CAAC,GACH8P,UAAU,CAACN,IAAD,CAAV,IAAoB,IAApB,GACIM,UAAU,CAACN,IAAD,CADd,GAEKM,UAAU,CAACN,IAAD,CAAV,GAAmB,KAAKD,SAAL,CAAeC,IAAf,CAH1B,CANiC;;;UAa7BxP,CAAC,GAAG,KAAKD,SAAL,GAAiB,KAAKkP,UAA9B,EAA0C;;YAEpCgB,GAAG,GAAGt7B,IAAV;YACMu7B,GAAG,GAAG,EAAZ;;eAEOV,IAAI,CAAC/6B,MAAZ,EAAoB;;cAEdiB,CAAJ,EAAOy6B,SAAP;;cACInQ,CAAC,GAAG,KAAK4O,SAAb,EAAwB;;;YAGtBl5B,CAAC,GAAG8B,IAAI,CAACyQ,IAAL,CAAU,KAAK2mB,SAAL,IAAkB5O,CAAC,GAAGwP,IAAI,CAAC/6B,MAA3B,CAAV,CAAJ;YACAurB,CAAC,GAAG,KAAKuP,SAAL,CAAeC,IAAI,CAACn6B,KAAL,CAAW,CAAX,EAAcK,CAAd,CAAf,CAAJ;YACAy6B,SAAS,GAAGnQ,CAAC,IAAI,KAAK4O,SAAV,IAAuBl5B,CAAC,GAAG85B,IAAI,CAAC/6B,MAA5C;WALF,MAMO;YACLiB,CAAC,GAAG85B,IAAI,CAAC/6B,MAAT;;;cAEE27B,UAAU,GAAGpQ,CAAC,GAAG,KAAK4O,SAAT,IAAsBl5B,CAAC,GAAG,CAA3C,CAZkB;;iBAcX06B,UAAU,IAAID,SAArB,EAAgC;gBAC1BC,UAAJ,EAAgB;cACdpQ,CAAC,GAAG,KAAKuP,SAAL,CAAeC,IAAI,CAACn6B,KAAL,CAAW,CAAX,EAAc,EAAEK,CAAhB,CAAf,CAAJ;cACA06B,UAAU,GAAGpQ,CAAC,GAAG,KAAK4O,SAAT,IAAsBl5B,CAAC,GAAG,CAAvC;aAFF,MAGO;cACLsqB,CAAC,GAAG,KAAKuP,SAAL,CAAeC,IAAI,CAACn6B,KAAL,CAAW,CAAX,EAAc,EAAEK,CAAhB,CAAf,CAAJ;cACA06B,UAAU,GAAGpQ,CAAC,GAAG,KAAK4O,SAAT,IAAsBl5B,CAAC,GAAG,CAAvC;cACAy6B,SAAS,GAAGnQ,CAAC,IAAI,KAAK4O,SAAV,IAAuBl5B,CAAC,GAAG85B,IAAI,CAAC/6B,MAA5C;;WArBc;;;cA0BdiB,CAAC,KAAK,CAAN,IAAW,KAAKk5B,SAAL,KAAmB,KAAK7O,SAAvC,EAAkD;YAChDrqB,CAAC,GAAG,CAAJ;WA3BgB;;;UA+BlBw6B,GAAG,CAACG,QAAJ,GAAeV,EAAE,CAACU,QAAH,IAAe36B,CAAC,GAAG85B,IAAI,CAAC/6B,MAAvC;UACAu7B,cAAc,GAAGzlB,EAAE,CAACilB,IAAI,CAACn6B,KAAL,CAAW,CAAX,EAAcK,CAAd,CAAD,EAAmBsqB,CAAnB,EAAsBkQ,GAAtB,EAA2BD,GAA3B,CAAnB;UACAA,GAAG,GAAG;YAAEI,QAAQ,EAAE;WAAlB,CAjCkB;;UAoClBb,IAAI,GAAGA,IAAI,CAACn6B,KAAL,CAAWK,CAAX,CAAP;UACAsqB,CAAC,GAAG,KAAKuP,SAAL,CAAeC,IAAf,CAAJ;;cAEIQ,cAAc,KAAK,KAAvB,EAA8B;;;;OA5ClC,MAgDO;;QAELA,cAAc,GAAGzlB,EAAE,CAACilB,IAAD,EAAOxP,CAAP,EAAU2P,EAAV,EAAch7B,IAAd,CAAnB;;;UAGEq7B,cAAc,KAAK,KAAvB,EAA8B;;;;MAG9Br7B,IAAI,GAAGg7B,EAAP;;;;EAIJW,IAAI,CAACrK,IAAD,EAAOvyB,OAAP,EAAgB;;QAEdA,OAAO,CAAC66B,MAAR,IAAkB,IAAtB,EAA4B;WACrBA,MAAL,GAAc76B,OAAO,CAAC66B,MAAtB;;;QAEE76B,OAAO,CAAC86B,gBAAR,IAA4B,IAAhC,EAAsC;WAC/BA,gBAAL,GAAwB96B,OAAO,CAAC86B,gBAAhC;;;QAEE96B,OAAO,CAAC+6B,WAAR,IAAuB,IAA3B,EAAiC;WAC1BA,WAAL,GAAmB/6B,OAAO,CAAC+6B,WAA3B;;;QAEE/6B,OAAO,CAACs7B,QAAR,IAAoB,IAAxB,EAA8B;WACvBA,QAAL,GAAgBt7B,OAAO,CAACs7B,QAAxB;KAZgB;;;;;QAkBZuB,KAAK,GAAG,KAAK54B,QAAL,CAAc6kB,CAAd,GAAkB,KAAK7kB,QAAL,CAAcs2B,iBAAd,CAAgC,IAAhC,CAAhC;;QACI,KAAKt2B,QAAL,CAAc6kB,CAAd,GAAkB,KAAK7d,IAAvB,IAA+B4xB,KAAK,GAAG,KAAK5xB,IAAhD,EAAsD;WAC/C6xB,WAAL;;;QAGEt4B,MAAM,GAAG,EAAb;QACIu4B,SAAS,GAAG,CAAhB;QACIC,EAAE,GAAG,CAAT;QACIC,EAAE,GAAG,CAAT;QAEI;MAAEnU;QAAM,KAAK7kB,QAAjB,CA5BkB;;QA6BZi5B,QAAQ,GAAG,MAAM;MACrBl9B,OAAO,CAAC+8B,SAAR,GAAoBA,SAAS,GAAG,KAAKhC,WAAL,IAAoBiC,EAAE,GAAG,CAAzB,CAAhC;MACAh9B,OAAO,CAACm9B,SAAR,GAAoBH,EAApB;MACAh9B,OAAO,CAACqsB,SAAR,GAAoB,KAAKA,SAAzB;OACC;QAAEvD;UAAM,KAAK7kB,QAAd;WACKm5B,IAAL,CAAU,MAAV,EAAkB54B,MAAlB,EAA0BxE,OAA1B,EAAmC,IAAnC;aACOi9B,EAAE,EAAT;KANF;;SASKG,IAAL,CAAU,cAAV,EAA0Bp9B,OAA1B,EAAmC,IAAnC;SAEKg8B,QAAL,CAAczJ,IAAd,EAAoB,CAACuJ,IAAD,EAAOxP,CAAP,EAAU2P,EAAV,EAAch7B,IAAd,KAAuB;UACrCA,IAAI,IAAI,IAAR,IAAgBA,IAAI,CAAC07B,QAAzB,EAAmC;aAC5BS,IAAL,CAAU,WAAV,EAAuBp9B,OAAvB,EAAgC,IAAhC;aACKk7B,SAAL,GAAiB,KAAK7O,SAAtB;;;UAGE,KAAK0P,MAAL,CAAYD,IAAZ,EAAkBxP,CAAlB,CAAJ,EAA0B;QACxB9nB,MAAM,IAAIs3B,IAAV;QACAiB,SAAS,IAAIzQ,CAAb;QACA0Q,EAAE;;;UAGAf,EAAE,CAACU,QAAH,IAAe,CAAC,KAAKZ,MAAL,CAAYD,IAAZ,EAAkBxP,CAAlB,CAApB,EAA0C;;;YAGlC+Q,EAAE,GAAG,KAAKp5B,QAAL,CAAcs2B,iBAAd,CAAgC,IAAhC,CAAX;;YAEE,KAAKnxB,MAAL,IAAe,IAAf,IACA,KAAKkyB,QADL,IAEA,KAAKr3B,QAAL,CAAc6kB,CAAd,GAAkBuU,EAAE,GAAG,CAAvB,GAA2B,KAAKpyB,IAFhC,IAGA,KAAKowB,MAAL,IAAe,KAAKL,OAJtB,EAKE;cACI,KAAKM,QAAL,KAAkB,IAAtB,EAA4B;iBACrBA,QAAL,GAAgB,GAAhB;WAFF;;;UAIA92B,MAAM,GAAGA,MAAM,CAAC3B,OAAP,CAAe,MAAf,EAAuB,EAAvB,CAAT;UACAk6B,SAAS,GAAG,KAAKlB,SAAL,CAAer3B,MAAM,GAAG,KAAK82B,QAA7B,CAAZ,CALA;;;iBASO92B,MAAM,IAAIu4B,SAAS,GAAG,KAAK1Q,SAAlC,EAA6C;YAC3C7nB,MAAM,GAAGA,MAAM,CAAC7C,KAAP,CAAa,CAAb,EAAgB,CAAC,CAAjB,EAAoBkB,OAApB,CAA4B,MAA5B,EAAoC,EAApC,CAAT;YACAk6B,SAAS,GAAG,KAAKlB,SAAL,CAAer3B,MAAM,GAAG,KAAK82B,QAA7B,CAAZ;WAXF;;;cAcIyB,SAAS,IAAI,KAAK1Q,SAAtB,EAAiC;YAC/B7nB,MAAM,GAAGA,MAAM,GAAG,KAAK82B,QAAvB;;;UAGFyB,SAAS,GAAG,KAAKlB,SAAL,CAAer3B,MAAf,CAAZ;;;YAGEy3B,EAAE,CAACU,QAAP,EAAiB;cACXrQ,CAAC,GAAG,KAAK4O,SAAb,EAAwB;YACtBgC,QAAQ;YACR14B,MAAM,GAAGs3B,IAAT;YACAiB,SAAS,GAAGzQ,CAAZ;YACA0Q,EAAE,GAAG,CAAL;;;eAGGI,IAAL,CAAU,UAAV,EAAsBp9B,OAAtB,EAA+B,IAA/B;SAtCsC;;;YA0CpCwE,MAAM,CAACA,MAAM,CAACzD,MAAP,GAAgB,CAAjB,CAAN,IAA6B05B,WAAjC,EAA8C;UAC5Cj2B,MAAM,GAAGA,MAAM,CAAC7C,KAAP,CAAa,CAAb,EAAgB,CAAC,CAAjB,IAAsB+4B,MAA/B;eACKQ,SAAL,IAAkB,KAAKW,SAAL,CAAenB,MAAf,CAAlB;;;QAGFwC,QAAQ,GA/CgC;;;YAmDpC,KAAKj5B,QAAL,CAAc6kB,CAAd,GAAkBuU,EAAlB,GAAuB,KAAKpyB,IAAhC,EAAsC;cAC9BqxB,cAAc,GAAG,KAAKQ,WAAL,EAAvB,CADoC;;cAIhC,CAACR,cAAL,EAAqB;YACnBU,EAAE,GAAG,CAAL;YACAx4B,MAAM,GAAG,EAAT;mBACO,KAAP;;SA1DoC;;;YA+DpCy3B,EAAE,CAACU,QAAP,EAAiB;eACVzB,SAAL,GAAiB,KAAK7O,SAAtB;UACA7nB,MAAM,GAAG,EAAT;UACAu4B,SAAS,GAAG,CAAZ;iBACQC,EAAE,GAAG,CAAb;SAJF,MAKO;;eAEA9B,SAAL,GAAiB,KAAK7O,SAAL,GAAiBC,CAAlC;UACA9nB,MAAM,GAAGs3B,IAAT;UACAiB,SAAS,GAAGzQ,CAAZ;iBACQ0Q,EAAE,GAAG,CAAb;;OAzEJ,MA2EO;eACG,KAAK9B,SAAL,IAAkB5O,CAA1B;;KAxFJ;;QA4FI0Q,EAAE,GAAG,CAAT,EAAY;WACLI,IAAL,CAAU,UAAV,EAAsBp9B,OAAtB,EAA+B,IAA/B;MACAk9B,QAAQ;;;SAGLE,IAAL,CAAU,YAAV,EAAwBp9B,OAAxB,EAAiC,IAAjC,EAzIkB;;;;QA8IdA,OAAO,CAACy7B,SAAR,KAAsB,IAA1B,EAAgC;UAC1BwB,EAAE,GAAG,CAAT,EAAY;aACL1B,UAAL,GAAkB,CAAlB;;;WAEGA,UAAL,IAAmBv7B,OAAO,CAAC+8B,SAAR,IAAqB,CAAxC;aACQ,KAAK94B,QAAL,CAAc6kB,CAAd,GAAkBA,CAA1B;KALF,MAMO;aACG,KAAK7kB,QAAL,CAAc+I,CAAd,GAAkB,KAAKmuB,MAA/B;;;;EAIJ2B,WAAW,CAAC98B,OAAD,EAAU;SACdo9B,IAAL,CAAU,YAAV,EAAwBp9B,OAAxB,EAAiC,IAAjC;;QAEI,EAAE,KAAKq7B,MAAP,GAAgB,KAAKL,OAAzB,EAAkC;;;UAG5B,KAAK5xB,MAAL,IAAe,IAAnB,EAAyB;eAChB,KAAP;;;WAGGnF,QAAL,CAAcq5B,iBAAd;WACKjC,MAAL,GAAc,CAAd;WACKD,MAAL,GAAc,KAAKn3B,QAAL,CAAcgU,IAAd,CAAmBjP,OAAnB,CAA2BzD,GAAzC;WACK0F,IAAL,GAAY,KAAKhH,QAAL,CAAcgU,IAAd,CAAmBhN,IAAnB,EAAZ;WACKhH,QAAL,CAAc+I,CAAd,GAAkB,KAAKmuB,MAAvB;;UACI,KAAKl3B,QAAL,CAAc2Y,UAAlB,EAA8B;aACvB3Y,QAAL,CAAcwY,SAAd,CAAwB,GAAG,KAAKxY,QAAL,CAAc2Y,UAAzC;;;WAEGwgB,IAAL,CAAU,WAAV,EAAuBp9B,OAAvB,EAAgC,IAAhC;KAfF,MAgBO;WACAiE,QAAL,CAAc+I,CAAd,IAAmB,KAAKqf,SAAL,GAAiB,KAAK4O,SAAzC;WACKh3B,QAAL,CAAc6kB,CAAd,GAAkB,KAAKsS,MAAvB;WACKgC,IAAL,CAAU,aAAV,EAAyBp9B,OAAzB,EAAkC,IAAlC;;;SAGGo9B,IAAL,CAAU,cAAV,EAA0Bp9B,OAA1B,EAAmC,IAAnC;WACO,IAAP;;;;;ACrVJ,IAAM;UAAE4D;IAAWzC,SAAnB;AAEA,gBAAe;EACbo8B,QAAQ,GAAG;SACJC,KAAL,GAAa,KAAKA,KAAL,CAAWC,IAAX,CAAgB,IAAhB,CAAb,CADS;;SAGJzwB,CAAL,GAAS,CAAT;SACK8b,CAAL,GAAS,CAAT;WACQ,KAAK4U,QAAL,GAAgB,CAAxB;GANW;;EASbxL,OAAO,CAACwL,QAAD,EAAW;SACXA,QAAL,GAAgBA,QAAhB;WACO,IAAP;GAXW;;EAcbC,QAAQ,CAACC,KAAD,EAAQ;QACVA,KAAK,IAAI,IAAb,EAAmB;MACjBA,KAAK,GAAG,CAAR;;;SAEG9U,CAAL,IAAU,KAAKyR,iBAAL,CAAuB,IAAvB,IAA+BqD,KAA/B,GAAuC,KAAKF,QAAtD;WACO,IAAP;GAnBW;;EAsBbG,MAAM,CAACD,KAAD,EAAQ;QACRA,KAAK,IAAI,IAAb,EAAmB;MACjBA,KAAK,GAAG,CAAR;;;SAEG9U,CAAL,IAAU,KAAKyR,iBAAL,CAAuB,IAAvB,IAA+BqD,KAA/B,GAAuC,KAAKF,QAAtD;WACO,IAAP;GA3BW;;EA8BbI,KAAK,CAACvL,IAAD,EAAOvlB,CAAP,EAAU8b,CAAV,EAAa9oB,OAAb,EAAsB+9B,YAAtB,EAAoC;IACvC/9B,OAAO,GAAG,KAAKg+B,YAAL,CAAkBhxB,CAAlB,EAAqB8b,CAArB,EAAwB9oB,OAAxB,CAAV,CADuC;;IAIvCuyB,IAAI,GAAGA,IAAI,IAAI,IAAR,GAAe,EAAf,aAAuBA,IAAvB,CAAP,CAJuC;;QAOnCvyB,OAAO,CAAC+6B,WAAZ,EAAyB;MACvBxI,IAAI,GAAGA,IAAI,CAAC1vB,OAAL,CAAa,SAAb,EAAwB,GAAxB,CAAP;;;QAGIo7B,YAAY,GAAG,MAAM;UACrBj+B,OAAO,CAACk+B,YAAZ,EAA0B;QACxBl+B,OAAO,CAACk+B,YAAR,CAAqB/9B,GAArB,CAAyB,KAAKg+B,MAAL,CAAYn+B,OAAO,CAACo+B,UAAR,IAAsB,GAAlC,EACvB,CAAE,KAAKC,oBAAL,CAA0Br+B,OAAO,CAACo+B,UAAR,IAAsB,GAAhD,CAAF,CADuB,CAAzB;;KAFJ,CAXuC;;;QAmBnCp+B,OAAO,CAACmJ,KAAZ,EAAmB;UACbm1B,OAAO,GAAG,KAAKC,QAAnB;;UACI,CAACD,OAAL,EAAc;QACZA,OAAO,GAAG,IAAI3D,WAAJ,CAAgB,IAAhB,EAAsB36B,OAAtB,CAAV;QACAs+B,OAAO,CAAC9H,EAAR,CAAW,MAAX,EAAmBuH,YAAnB;QACAO,OAAO,CAAC9H,EAAR,CAAW,WAAX,EAAwByH,YAAxB;;;WAGGM,QAAL,GAAgBv+B,OAAO,CAACy7B,SAAR,GAAoB6C,OAApB,GAA8B,IAA9C;WACKE,YAAL,GAAoBx+B,OAAO,CAACy7B,SAAR,GAAoBz7B,OAApB,GAA8B,IAAlD;MACAs+B,OAAO,CAAC1B,IAAR,CAAarK,IAAb,EAAmBvyB,OAAnB,EAViB;KAAnB,MAaO;WACA,IAAIoyB,IAAT,IAAiBG,IAAI,CAACtB,KAAL,CAAW,IAAX,CAAjB,EAAmC;QACjCgN,YAAY;QACZF,YAAY,CAAC3L,IAAD,EAAOpyB,OAAP,CAAZ;;;;WAIG,IAAP;GArEW;;EAwEbuyB,IAAI,CAACA,IAAD,EAAOvlB,CAAP,EAAU8b,CAAV,EAAa9oB,OAAb,EAAsB;WACjB,KAAK89B,KAAL,CAAWvL,IAAX,EAAiBvlB,CAAjB,EAAoB8b,CAApB,EAAuB9oB,OAAvB,EAAgC,KAAKw9B,KAArC,CAAP;GAzEW;;EA4EbpK,aAAa,CAAC9wB,MAAD,EAAuB;QAAdtC,OAAc,uEAAJ,EAAI;WAEhC,KAAKm6B,KAAL,CAAW/G,aAAX,CAAyB9wB,MAAzB,EAAiC,KAAK43B,SAAtC,EAAiDl6B,OAAO,CAAC01B,QAAzD,IACA,CAAC11B,OAAO,CAAC86B,gBAAR,IAA4B,CAA7B,KAAmCx4B,MAAM,CAACvB,MAAP,GAAgB,CAAnD,CAFF;GA7EW;;EAmFb09B,cAAc,CAAClM,IAAD,EAAOvyB,OAAP,EAAgB;QACtB;MAAEgN,CAAF;MAAK8b;QAAM,IAAjB;IAEA9oB,OAAO,GAAG,KAAKg+B,YAAL,CAAkBh+B,OAAlB,CAAV;IACAA,OAAO,CAACoJ,MAAR,GAAiBs1B,QAAjB,CAJ4B;;QAMtBxM,OAAO,GAAGlyB,OAAO,CAACkyB,OAAR,IAAmB,KAAKwL,QAAxB,IAAoC,CAApD;;SACKI,KAAL,CAAWvL,IAAX,EAAiB,KAAKvlB,CAAtB,EAAyB,KAAK8b,CAA9B,EAAiC9oB,OAAjC,EAA0C,MAAM;aACtC,KAAK8oB,CAAL,IAAU,KAAKyR,iBAAL,CAAuB,IAAvB,IAA+BrI,OAAjD;KADF;;QAIM9oB,MAAM,GAAG,KAAK0f,CAAL,GAASA,CAAxB;SACK9b,CAAL,GAASA,CAAT;SACK8b,CAAL,GAASA,CAAT;WAEO1f,MAAP;GAlGW;;EAqGbu1B,IAAI,CAACA,IAAD,EAAO3xB,CAAP,EAAU8b,CAAV,EAAa9oB,OAAb,EAAsBs+B,OAAtB,EAA+B;IACjCt+B,OAAO,GAAG,KAAKg+B,YAAL,CAAkBhxB,CAAlB,EAAqB8b,CAArB,EAAwB9oB,OAAxB,CAAV;QAEM4+B,QAAQ,GAAG5+B,OAAO,CAAC4+B,QAAR,IAAoB,QAArC;QACMC,IAAI,GAAG/6B,IAAI,CAACC,KAAL,CAAY,KAAKo2B,KAAL,CAAWrI,QAAX,GAAsB,IAAvB,GAA+B,KAAKoI,SAA/C,CAAb;QACM4E,OAAO,GAAGD,IAAI,GAAG,CAAvB;QACM5uB,CAAC,GAAGjQ,OAAO,CAAC++B,YAAR,IAAwBF,IAAI,GAAG,CAAzC;QACMhE,MAAM,GACV76B,OAAO,CAACg/B,UAAR,KAAuBJ,QAAQ,KAAK,QAAb,GAAwB3uB,CAAC,GAAG,CAA5B,GAAgC4uB,IAAI,GAAG,CAA9D,CADF;QAEMI,UAAU,GACdj/B,OAAO,CAACk/B,YAAR,KAAyBN,QAAQ,KAAK,QAAb,GAAwB3uB,CAAC,GAAG,CAA5B,GAAgC4uB,IAAI,GAAG,CAAhE,CADF;QAGIM,KAAK,GAAG,CAAZ;QACM37B,KAAK,GAAG,EAAd;QACM47B,MAAM,GAAG,EAAf;QACMC,OAAO,GAAG,EAAhB;;QAEIC,OAAO,GAAG,SAAVA,OAAU,CAASX,IAAT,EAAe;UACvB96B,CAAC,GAAG,CAAR;;WACK,IAAI5B,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG08B,IAAI,CAAC59B,MAAzB,EAAiCkB,CAAC,EAAlC,EAAsC;YAC9Bs9B,IAAI,GAAGZ,IAAI,CAAC18B,CAAD,CAAjB;;YACIP,KAAK,CAAC6B,OAAN,CAAcg8B,IAAd,CAAJ,EAAyB;UACvBJ,KAAK;UACLG,OAAO,CAACC,IAAD,CAAP;UACAJ,KAAK;SAHP,MAIO;UACL37B,KAAK,CAACtC,IAAN,CAAWq+B,IAAX;UACAH,MAAM,CAACl+B,IAAP,CAAYi+B,KAAZ;;cACIP,QAAQ,KAAK,QAAjB,EAA2B;YACzBS,OAAO,CAACn+B,IAAR,CAAa2C,CAAC,EAAd;;;;KAZR;;IAkBAy7B,OAAO,CAACX,IAAD,CAAP;;QAEMa,KAAK,GAAG,SAARA,KAAQ,CAAS37B,CAAT,EAAY;cAChB+6B,QAAR;aACO,UAAL;2BACY/6B,CAAV;;aACG,UAAL;cACM47B,MAAM,GAAGp9B,MAAM,CAAC80B,YAAP,CAAqB,CAACtzB,CAAC,GAAG,CAAL,IAAU,EAAX,GAAiB,EAArC,CAAb;cACI67B,KAAK,GAAG57B,IAAI,CAAC6H,KAAL,CAAW,CAAC9H,CAAC,GAAG,CAAL,IAAU,EAAV,GAAe,CAA1B,CAAZ;cACI0uB,IAAI,GAAG7wB,KAAK,CAACg+B,KAAK,GAAG,CAAT,CAAL,CAAiBn+B,IAAjB,CAAsBk+B,MAAtB,CAAX;2BACUlN,IAAV;;KARN;;QAYMoN,YAAY,GAAG,SAAfA,YAAe,CAASC,QAAT,EAAmB;MACtCtB,OAAO,GAAG,IAAI3D,WAAJ,CAAgB,IAAhB,EAAsB36B,OAAtB,CAAV;MACAs+B,OAAO,CAAC9H,EAAR,CAAW,MAAX,EAAmB,KAAKgH,KAAxB;MAEA2B,KAAK,GAAG,CAAR;UACIl9B,CAAC,GAAG,CAAR;MACAq8B,OAAO,CAAC9C,IAAR,CAAa,WAAb,EAA0B,MAAM;YAC1B+D,IAAJ,EAAUM,QAAV,EAAoBC,SAApB,EAA+BC,QAA/B;;YACI//B,OAAO,CAACk+B,YAAZ,EAA0B;cACpBl+B,OAAO,CAACggC,WAAZ,EAAyB;aACtBH,QAAD,EAAWC,SAAX,EAAsBC,QAAtB,IAAkC//B,OAAO,CAACggC,WAA1C;WADF,MAEO;aACJH,QAAD,EAAWC,SAAX,EAAsBC,QAAtB,IAAkC,CAAC,IAAD,EAAO,KAAP,EAAc,OAAd,CAAlC;;;;YAIAF,QAAJ,EAAc;UACZN,IAAI,GAAG,KAAKpB,MAAL,CAAY0B,QAAZ,CAAP;UACA7/B,OAAO,CAACk+B,YAAR,CAAqB/9B,GAArB,CAAyBo/B,IAAzB;SAFF,MAGO,IAAIv/B,OAAO,CAACk+B,YAAZ,EAA0B;UAC/BqB,IAAI,GAAGv/B,OAAO,CAACk+B,YAAf;;;YAGEl8B,CAAJ;;YACI,CAACA,CAAC,GAAGo9B,MAAM,CAACn9B,CAAC,EAAF,CAAX,MAAsBk9B,KAA1B,EAAiC;cACzBc,IAAI,GAAGhB,UAAU,IAAIj9B,CAAC,GAAGm9B,KAAR,CAAvB;eACKnyB,CAAL,IAAUizB,IAAV;UACA3B,OAAO,CAACjS,SAAR,IAAqB4T,IAArB;UACAd,KAAK,GAAGn9B,CAAR;;;YAGEu9B,IAAI,KAAKO,SAAS,IAAIC,QAAlB,CAAR,EAAqC;UACnCR,IAAI,CAACp/B,GAAL,CAAS,KAAKg+B,MAAL,CAAY2B,SAAS,IAAIC,QAAzB,EACP,CAAC,KAAK1B,oBAAL,CAA0ByB,SAAS,IAAIC,QAAvC,CAAD,CADO,CAAT;;;gBAGMnB,QAAR;eACO,QAAL;iBACOpQ,MAAL,CAAY,KAAKxhB,CAAL,GAAS6tB,MAAT,GAAkB5qB,CAA9B,EAAiC,KAAK6Y,CAAL,GAASgW,OAA1C,EAAmD7uB,CAAnD;iBACKigB,IAAL;;;eAEG,UAAL;eACK,UAAL;gBACMqC,IAAI,GAAGiN,KAAK,CAACH,OAAO,CAACp9B,CAAC,GAAG,CAAL,CAAR,CAAhB;;iBACKi+B,SAAL,CAAe3N,IAAf,EAAqB,KAAKvlB,CAAL,GAAS6tB,MAA9B,EAAsC,KAAK/R,CAA3C,EAA8C9oB,OAA9C;;;;;YAIAu/B,IAAI,IAAIO,SAAR,IAAqBC,QAAzB,EAAmC;UACjCR,IAAI,CAACp/B,GAAL,CAAS,KAAKg+B,MAAL,CAAY4B,QAAZ,EAAsB,CAAC,KAAK1B,oBAAL,CAA0B0B,QAA1B,CAAD,CAAtB,CAAT;;;YAEER,IAAI,IAAIA,IAAI,KAAKv/B,OAAO,CAACk+B,YAA7B,EAA2C;UACzCqB,IAAI,CAACr9B,GAAL;;OA7CJ;MAiDAo8B,OAAO,CAAC9H,EAAR,CAAW,cAAX,EAA2B,MAAM;YACzB1gB,GAAG,GAAG+kB,MAAM,GAAGoE,UAAU,IAAIE,KAAK,GAAG,CAAZ,CAA/B;aACKnyB,CAAL,IAAU8I,GAAV;eACQwoB,OAAO,CAACjS,SAAR,IAAqBvW,GAA7B;OAHF;MAMAwoB,OAAO,CAAC9H,EAAR,CAAW,YAAX,EAAyB,MAAM;YACvB1gB,GAAG,GAAG+kB,MAAM,GAAGoE,UAAU,IAAIE,KAAK,GAAG,CAAZ,CAA/B;aACKnyB,CAAL,IAAU8I,GAAV;eACQwoB,OAAO,CAACjS,SAAR,IAAqBvW,GAA7B;OAHF;MAMAwoB,OAAO,CAAC1B,IAAR,CAAagD,QAAb,EAAuB5/B,OAAvB;KAnEF;;SAuEK,IAAIiC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGuB,KAAK,CAACzC,MAA1B,EAAkCkB,CAAC,EAAnC,EAAuC;MACrC09B,YAAY,CAACh8B,IAAb,CAAkB,IAAlB,EAAwBH,KAAK,CAACvB,CAAD,CAA7B;;;WAGK,IAAP;GAjOW;;EAoOb+7B,YAAY,GAA0B;QAAzBhxB,CAAyB,uEAArB,EAAqB;QAAjB8b,CAAiB;QAAd9oB,OAAc,uEAAJ,EAAI;;QAChC,OAAOgN,CAAP,KAAa,QAAjB,EAA2B;MACzBhN,OAAO,GAAGgN,CAAV;MACAA,CAAC,GAAG,IAAJ;KAHkC;;;QAO9Bwe,MAAM,GAAGhrB,MAAM,CAAC2/B,MAAP,CAAc,EAAd,EAAkBngC,OAAlB,CAAf,CAPoC;;QAUhC,KAAKw+B,YAAT,EAAuB;WAChB,IAAIp+B,GAAT,IAAgB,KAAKo+B,YAArB,EAAmC;YAC3Bn+B,GAAG,GAAG,KAAKm+B,YAAL,CAAkBp+B,GAAlB,CAAZ;;YACIA,GAAG,KAAK,WAAZ,EAAyB;cACnBorB,MAAM,CAACprB,GAAD,CAAN,KAAgBu2B,SAApB,EAA+B;YAC7BnL,MAAM,CAACprB,GAAD,CAAN,GAAcC,GAAd;;;;KAf4B;;;QAsBhC2M,CAAC,IAAI,IAAT,EAAe;WACRA,CAAL,GAASA,CAAT;;;QAEE8b,CAAC,IAAI,IAAT,EAAe;WACRA,CAAL,GAASA,CAAT;KA1BkC;;;QA8BhC0C,MAAM,CAAC4U,SAAP,KAAqB,KAAzB,EAAgC;UAC1B5U,MAAM,CAACriB,KAAP,IAAgB,IAApB,EAA0B;QACxBqiB,MAAM,CAACriB,KAAP,GAAe,KAAK8O,IAAL,CAAU9O,KAAV,GAAkB,KAAK6D,CAAvB,GAA2B,KAAKiL,IAAL,CAAUjP,OAAV,CAAkBtD,KAA5D;;;MAEF8lB,MAAM,CAACriB,KAAP,GAAerF,IAAI,CAACqS,GAAL,CAASqV,MAAM,CAACriB,KAAhB,EAAuB,CAAvB,CAAf;;;QAGE,CAACqiB,MAAM,CAACwP,OAAZ,EAAqB;MACnBxP,MAAM,CAACwP,OAAP,GAAiB,CAAjB;;;QAEExP,MAAM,CAACyP,SAAP,IAAoB,IAAxB,EAA8B;MAC5BzP,MAAM,CAACyP,SAAP,GAAmB,EAAnB;KAzCkC;;;WA4C7BzP,MAAP;GAhRW;;EAmRbgS,KAAK,CAACjL,IAAD,EAA8B;QAAvBvyB,OAAuB,uEAAb,EAAa;QAATs+B,OAAS;;SAC5B4B,SAAL,CAAe3N,IAAf,EAAqB,KAAKvlB,CAA1B,EAA6B,KAAK8b,CAAlC,EAAqC9oB,OAArC;;QACMkyB,OAAO,GAAGlyB,OAAO,CAACkyB,OAAR,IAAmB,KAAKwL,QAAxB,IAAoC,CAApD;;QAEI,CAACY,OAAL,EAAc;aACJ,KAAKtxB,CAAL,IAAU,KAAKomB,aAAL,CAAmBb,IAAnB,CAAlB;KADF,MAEO;aACG,KAAKzJ,CAAL,IAAU,KAAKyR,iBAAL,CAAuB,IAAvB,IAA+BrI,OAAjD;;GA1RS;;EA8RbgO,SAAS,CAAC3N,IAAD,EAAOvlB,CAAP,EAAU8b,CAAV,EAAa9oB,OAAb,EAAsB;QACzB0W,EAAJ,EAAQwd,OAAR,EAAiBjyB,CAAjB,EAAoBkyB,SAApB,EAA+B4I,SAA/B,EAA0C7qB,KAA1C;IACAqgB,IAAI,GAAG,UAAGA,IAAH,EAAU1vB,OAAV,CAAkB,KAAlB,EAAyB,EAAzB,CAAP;;QACI0vB,IAAI,CAACxxB,MAAL,KAAgB,CAApB,EAAuB;;KAHM;;;QAQvB26B,KAAK,GAAG17B,OAAO,CAAC07B,KAAR,IAAiB,MAA/B;QACIX,WAAW,GAAG/6B,OAAO,CAAC+6B,WAAR,IAAuB,CAAzC;QACMD,gBAAgB,GAAG96B,OAAO,CAAC86B,gBAAR,IAA4B,CAArD,CAV6B;;QAazB96B,OAAO,CAACmJ,KAAZ,EAAmB;cACTuyB,KAAR;aACO,OAAL;UACEqB,SAAS,GAAG,KAAK3J,aAAL,CAAmBb,IAAI,CAAC1vB,OAAL,CAAa,MAAb,EAAqB,EAArB,CAAnB,EAA6C7C,OAA7C,CAAZ;UACAgN,CAAC,IAAIhN,OAAO,CAACqsB,SAAR,GAAoB0Q,SAAzB;;;aAGG,QAAL;UACE/vB,CAAC,IAAIhN,OAAO,CAACqsB,SAAR,GAAoB,CAApB,GAAwBrsB,OAAO,CAAC+8B,SAAR,GAAoB,CAAjD;;;aAGG,SAAL;;UAEE7qB,KAAK,GAAGqgB,IAAI,CAAC8N,IAAL,GAAYpP,KAAZ,CAAkB,KAAlB,CAAR;UACA8L,SAAS,GAAG,KAAK3J,aAAL,CAAmBb,IAAI,CAAC1vB,OAAL,CAAa,MAAb,EAAqB,EAArB,CAAnB,EAA6C7C,OAA7C,CAAZ;cACIsgC,UAAU,GAAG,KAAKlN,aAAL,CAAmB,GAAnB,IAA0B0H,gBAA3C;UACAC,WAAW,GAAGj3B,IAAI,CAACqS,GAAL,CACZ,CADY,EAEZ,CAACnW,OAAO,CAACqsB,SAAR,GAAoB0Q,SAArB,IAAkCj5B,IAAI,CAACqS,GAAL,CAAS,CAAT,EAAYjE,KAAK,CAACnR,MAAN,GAAe,CAA3B,CAAlC,GACEu/B,UAHU,CAAd;;;KA7BuB;;;QAuCzB,OAAOtgC,OAAO,CAACugC,QAAf,KAA4B,QAAhC,EAA0C;MACxC7pB,EAAE,GAAG,CAAC1W,OAAO,CAACugC,QAAd;KADF,MAEO;cACGvgC,OAAO,CAACugC,QAAhB;aACO,YAAL;UACE7pB,EAAE,GAAG,MAAM,KAAKyjB,KAAL,CAAWnI,OAAtB;;;aAEG,QAAL;aACK,aAAL;UACEtb,EAAE,GAAG,OAAO,KAAKyjB,KAAL,CAAWpI,SAAX,GAAuB,KAAKoI,KAAL,CAAWrI,QAAzC,CAAL;;;aAEG,QAAL;aACK,aAAL;UACEpb,EAAE,GAAG,KAAKyjB,KAAL,CAAWpI,SAAhB;;;aAEG,YAAL;UACErb,EAAE,GAAG,CAAL;;;aAEG,cAAL;UACEA,EAAE,GAAG,MAAM,KAAKyjB,KAAL,CAAWrI,QAAtB;;;aAEG,SAAL;UACEpb,EAAE,GAAG,MAAM,KAAKyjB,KAAL,CAAWrI,QAAtB;;;aAEG,KAAL;UACEpb,EAAE,GAAG,KAAKyjB,KAAL,CAAWrI,QAAhB;;;;UAGApb,EAAE,GAAG,KAAKyjB,KAAL,CAAWrI,QAAhB;;;MAEJpb,EAAE,GAAIA,EAAE,GAAG,IAAN,GAAc,KAAKwjB,SAAxB;KArE2B;;;QAyEvBsG,aAAa,GACjBxgC,OAAO,CAAC+8B,SAAR,GACAhC,WAAW,IAAI/6B,OAAO,CAACm9B,SAAR,GAAoB,CAAxB,CADX,GAEArC,gBAAgB,IAAIvI,IAAI,CAACxxB,MAAL,GAAc,CAAlB,CAHlB,CAzE6B;;QA+EzBf,OAAO,CAACygC,IAAR,IAAgB,IAApB,EAA0B;WACnBA,IAAL,CAAUzzB,CAAV,EAAa8b,CAAb,EAAgB0X,aAAhB,EAA+B,KAAKjG,iBAAL,EAA/B,EAAyDv6B,OAAO,CAACygC,IAAjE;;;QAEEzgC,OAAO,CAAC0gC,IAAR,IAAgB,IAApB,EAA0B;WACnBA,IAAL,CAAU1zB,CAAV,EAAa8b,CAAb,EAAgB0X,aAAhB,EAA+B,KAAKjG,iBAAL,EAA/B,EAAyDv6B,OAAO,CAAC0gC,IAAjE;;;QAEE1gC,OAAO,CAAC2gC,WAAR,IAAuB,IAA3B,EAAiC;WAC1BC,mBAAL,CAAyB5gC,OAAO,CAAC2gC,WAAjC,EAA8C,KAA9C,EAAqD3zB,CAArD,EAAwD8b,CAAxD,EAA2D,IAA3D;KAtF2B;;;QA0FzB9oB,OAAO,CAAC6gC,SAAZ,EAAuB;WAChB3U,IAAL;;UACI,CAAClsB,OAAO,CAACmZ,MAAb,EAAqB;aACd0D,WAAL,CAAiB,IAAI,KAAKD,UAAL,IAAmB,EAAvB,CAAjB;;;UAGIyP,SAAS,GACb,KAAK6N,SAAL,GAAiB,EAAjB,GAAsB,GAAtB,GAA4Bp2B,IAAI,CAAC6H,KAAL,CAAW,KAAKuuB,SAAL,GAAiB,EAA5B,CAD9B;WAEK7N,SAAL,CAAeA,SAAf;UAEIyU,KAAK,GAAIhY,CAAC,GAAG,KAAKyR,iBAAL,EAAL,GAAkClO,SAA9C;WACK7D,MAAL,CAAYxb,CAAZ,EAAe8zB,KAAf;WACKlY,MAAL,CAAY5b,CAAC,GAAGwzB,aAAhB,EAA+BM,KAA/B;WACK3nB,MAAL;WACKgT,OAAL;KAxG2B;;;QA4GzBnsB,OAAO,CAAC+gC,MAAZ,EAAoB;WACb7U,IAAL;;UACI,CAAClsB,OAAO,CAACmZ,MAAb,EAAqB;aACd0D,WAAL,CAAiB,IAAI,KAAKD,UAAL,IAAmB,EAAvB,CAAjB;;;UAGIyP,UAAS,GACb,KAAK6N,SAAL,GAAiB,EAAjB,GAAsB,GAAtB,GAA4Bp2B,IAAI,CAAC6H,KAAL,CAAW,KAAKuuB,SAAL,GAAiB,EAA5B,CAD9B;;WAEK7N,SAAL,CAAeA,UAAf;;UAEIyU,MAAK,GAAGhY,CAAC,GAAG,KAAKyR,iBAAL,KAA2B,CAA3C;;WACK/R,MAAL,CAAYxb,CAAZ,EAAe8zB,MAAf;WACKlY,MAAL,CAAY5b,CAAC,GAAGwzB,aAAhB,EAA+BM,MAA/B;WACK3nB,MAAL;WACKgT,OAAL;;;SAGGD,IAAL,GA7H6B;;QAgIzBlsB,OAAO,CAACghC,OAAZ,EAAqB;UACfC,IAAJ;;UACI,OAAOjhC,OAAO,CAACghC,OAAf,KAA2B,QAA/B,EAAyC;QACvCC,IAAI,GAAG,CAACn9B,IAAI,CAACgiB,GAAL,CAAU9lB,OAAO,CAACghC,OAAR,GAAkBl9B,IAAI,CAACkmB,EAAxB,GAA8B,GAAvC,CAAR;OADF,MAEO;QACLiX,IAAI,GAAG,CAAC,IAAR;;;WAEGrrB,SAAL,CAAe,CAAf,EAAkB,CAAlB,EAAqB,CAArB,EAAwB,CAAxB,EAA2B5I,CAA3B,EAA8B8b,CAA9B;WACKlT,SAAL,CAAe,CAAf,EAAkB,CAAlB,EAAqBqrB,IAArB,EAA2B,CAA3B,EAA8B,CAACA,IAAD,GAAQvqB,EAAtC,EAA0C,CAA1C;WACKd,SAAL,CAAe,CAAf,EAAkB,CAAlB,EAAqB,CAArB,EAAwB,CAAxB,EAA2B,CAAC5I,CAA5B,EAA+B,CAAC8b,CAAhC;KAzI2B;;;SA6IxBlT,SAAL,CAAe,CAAf,EAAkB,CAAlB,EAAqB,CAArB,EAAwB,CAAC,CAAzB,EAA4B,CAA5B,EAA+B,KAAKqC,IAAL,CAAU7O,MAAzC;IACA0f,CAAC,GAAG,KAAK7Q,IAAL,CAAU7O,MAAV,GAAmB0f,CAAnB,GAAuBpS,EAA3B,CA9I6B;;QAiJzB,KAAKuB,IAAL,CAAU/N,KAAV,CAAgB,KAAKiwB,KAAL,CAAWj2B,EAA3B,KAAkC,IAAtC,EAA4C;WACrC+T,IAAL,CAAU/N,KAAV,CAAgB,KAAKiwB,KAAL,CAAWj2B,EAA3B,IAAiC,KAAKi2B,KAAL,CAAW7wB,GAAX,EAAjC;KAlJ2B;;;SAsJxBuQ,UAAL,CAAgB,IAAhB,EAtJ6B;;SAyJxBA,UAAL,mBAA2BjW,QAAM,CAACoJ,CAAD,CAAjC,cAAwCpJ,QAAM,CAACklB,CAAD,CAA9C,UAzJ6B;;SA4JxBjP,UAAL,YAAoB,KAAKsgB,KAAL,CAAWj2B,EAA/B,cAAqCN,QAAM,CAAC,KAAKs2B,SAAN,CAA3C,UA5J6B;;QA+JvB3mB,IAAI,GAAGvT,OAAO,CAACkwB,IAAR,IAAgBlwB,OAAO,CAACmZ,MAAxB,GAAiC,CAAjC,GAAqCnZ,OAAO,CAACmZ,MAAR,GAAiB,CAAjB,GAAqB,CAAvE;;QACI5F,IAAJ,EAAU;WACHsG,UAAL,WAAmBtG,IAAnB;KAjK2B;;;QAqKzBunB,gBAAJ,EAAsB;WACfjhB,UAAL,WAAmBjW,QAAM,CAACk3B,gBAAD,CAAzB;KAtK2B;;;;;;QA6KzBC,WAAJ,EAAiB;MACf7oB,KAAK,GAAGqgB,IAAI,CAAC8N,IAAL,GAAYpP,KAAZ,CAAkB,KAAlB,CAAR;MACA8J,WAAW,IAAI,KAAK3H,aAAL,CAAmB,GAAnB,IAA0B0H,gBAAzC;MACAC,WAAW,IAAI,OAAO,KAAKb,SAA3B;MAEAhG,OAAO,GAAG,EAAV;MACAC,SAAS,GAAG,EAAZ;;WACK,IAAI2H,IAAT,IAAiB5pB,KAAjB,EAAwB;YAChB,CAACgvB,WAAD,EAAcC,aAAd,IAA+B,KAAKhH,KAAL,CAAWljB,MAAX,CACnC6kB,IADmC,EAEnC97B,OAAO,CAAC01B,QAF2B,CAArC;;QAIAxB,OAAO,GAAGA,OAAO,CAACjvB,MAAR,CAAei8B,WAAf,CAAV;QACA/M,SAAS,GAAGA,SAAS,CAAClvB,MAAV,CAAiBk8B,aAAjB,CAAZ,CANsB;;;YAUhB3kB,KAAK,GAAG,EAAd;YACMra,MAAM,GAAGgyB,SAAS,CAACA,SAAS,CAACpzB,MAAV,GAAmB,CAApB,CAAxB;;aACK,IAAIX,GAAT,IAAgB+B,MAAhB,EAAwB;cAChB9B,GAAG,GAAG8B,MAAM,CAAC/B,GAAD,CAAlB;UACAoc,KAAK,CAACpc,GAAD,CAAL,GAAaC,GAAb;;;QAEFmc,KAAK,CAAC4X,QAAN,IAAkB2G,WAAlB;QACA5G,SAAS,CAACA,SAAS,CAACpzB,MAAV,GAAmB,CAApB,CAAT,GAAkCyb,KAAlC;;KAxBJ,MA0BO;OACJ0X,OAAD,EAAUC,SAAV,IAAuB,KAAKgG,KAAL,CAAWljB,MAAX,CAAkBsb,IAAlB,EAAwBvyB,OAAO,CAAC01B,QAAhC,CAAvB;;;QAGI9E,KAAK,GAAG,KAAKsJ,SAAL,GAAiB,IAA/B;QACM5R,QAAQ,GAAG,EAAjB;QACIrnB,IAAI,GAAG,CAAX;QACImgC,SAAS,GAAG,KAAhB,CA9M6B;;QAiNvBC,UAAU,GAAGC,GAAG,IAAI;UACpBrgC,IAAI,GAAGqgC,GAAX,EAAgB;YACRplB,GAAG,GAAGgY,OAAO,CAACvyB,KAAR,CAAcV,IAAd,EAAoBqgC,GAApB,EAAyB//B,IAAzB,CAA8B,EAA9B,CAAZ;YACMkzB,OAAO,GACXN,SAAS,CAACmN,GAAG,GAAG,CAAP,CAAT,CAAmBlN,QAAnB,GAA8BD,SAAS,CAACmN,GAAG,GAAG,CAAP,CAAT,CAAmB9M,YADnD;QAEAlM,QAAQ,CAACpnB,IAAT,YAAkBgb,GAAlB,eAA0BtY,QAAM,CAAC,CAAC6wB,OAAF,CAAhC;;;aAGMxzB,IAAI,GAAGqgC,GAAf;KARF,CAjN6B;;;QA6NvBC,KAAK,GAAGt/B,CAAC,IAAI;MACjBo/B,UAAU,CAACp/B,CAAD,CAAV;;UAEIqmB,QAAQ,CAACvnB,MAAT,GAAkB,CAAtB,EAAyB;aAClB8Y,UAAL,YAAoByO,QAAQ,CAAC/mB,IAAT,CAAc,GAAd,CAApB;eACQ+mB,QAAQ,CAACvnB,MAAT,GAAkB,CAA1B;;KALJ;;SASKkB,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAGkyB,SAAS,CAACpzB,MAA1B,EAAkCkB,CAAC,EAAnC,EAAuC;;;UAG/B6T,GAAG,GAAGqe,SAAS,CAAClyB,CAAD,CAArB;;UACI6T,GAAG,CAACwe,OAAJ,IAAexe,GAAG,CAACye,OAAvB,EAAgC;;QAE9BgN,KAAK,CAACt/B,CAAD,CAAL,CAF8B;;aAKzB4X,UAAL,mBACajW,QAAM,CAACoJ,CAAC,GAAG8I,GAAG,CAACwe,OAAJ,GAAc1D,KAAnB,CADnB,cACgDhtB,QAAM,CAClDklB,CAAC,GAAGhT,GAAG,CAACye,OAAJ,GAAc3D,KADgC,CADtD;QAKA2Q,KAAK,CAACt/B,CAAC,GAAG,CAAL,CAAL;QAEAm/B,SAAS,GAAG,IAAZ;OAZF,MAaO;;YAEDA,SAAJ,EAAe;eACRvnB,UAAL,mBAA2BjW,QAAM,CAACoJ,CAAD,CAAjC,cAAwCpJ,QAAM,CAACklB,CAAD,CAA9C;UACAsY,SAAS,GAAG,KAAZ;SAJG;;;YAQDtrB,GAAG,CAACse,QAAJ,GAAete,GAAG,CAAC0e,YAAnB,KAAoC,CAAxC,EAA2C;UACzC6M,UAAU,CAACp/B,CAAC,GAAG,CAAL,CAAV;;;;MAIJ+K,CAAC,IAAI8I,GAAG,CAACse,QAAJ,GAAexD,KAApB;KApQ2B;;;IAwQ7B2Q,KAAK,CAACt/B,CAAD,CAAL,CAxQ6B;;SA2QxB4X,UAAL,CAAgB,IAAhB,EA3Q6B;;WA8QtB,KAAKsS,OAAL,EAAP;;;CA5iBJ;;ACHA,IAAMqV,OAAO,GAAG,CACd,MADc,EAEd,MAFc,EAGd,MAHc,EAId,MAJc,EAKd,MALc,EAMd,MANc,EAOd,MAPc,EAQd,MARc,EASd,MATc,EAUd,MAVc,EAWd,MAXc,EAYd,MAZc,EAad,MAbc,EAcd,MAdc,EAed,MAfc,CAAhB;AAkBA,IAAMC,eAAe,GAAG;KACnB,YADmB;KAEnB,WAFmB;KAGnB;CAHL;;AAMA,MAAMC,IAAN,CAAW;EACT3hC,WAAW,CAACoE,IAAD,EAAOq7B,KAAP,EAAc;QACnBmC,MAAJ;SACKx9B,IAAL,GAAYA,IAAZ;SACKq7B,KAAL,GAAaA,KAAb;;QACI,KAAKr7B,IAAL,CAAUy9B,YAAV,CAAuB,CAAvB,MAA8B,MAAlC,EAA0C;YAClC,uBAAN;KALqB;;;SASlBC,WAAL,GAAmBC,IAAI,CAACC,UAAL,CAAgB,KAAK59B,IAArB,EAA2B69B,WAA3B,IAA0C,CAA7D;QAEIlsB,GAAG,GAAG,CAAV;;WACOA,GAAG,GAAG,KAAK3R,IAAL,CAAUpD,MAAvB,EAA+B;MAC7B4gC,MAAM,GAAG,KAAKx9B,IAAL,CAAUy9B,YAAV,CAAuB9rB,GAAvB,CAAT;MACAA,GAAG,IAAI,CAAP;;UACI0rB,OAAO,CAACnZ,QAAR,CAAiBsZ,MAAjB,CAAJ,EAA8B;;;;MAG9B7rB,GAAG,IAAI,KAAK3R,IAAL,CAAUy9B,YAAV,CAAuB9rB,GAAvB,CAAP;;;QAGE,CAAC0rB,OAAO,CAACnZ,QAAR,CAAiBsZ,MAAjB,CAAL,EAA+B;YACvB,eAAN;;;IAEF7rB,GAAG,IAAI,CAAP;SAEKmsB,IAAL,GAAY,KAAK99B,IAAL,CAAU2R,GAAG,EAAb,CAAZ;SACK1M,MAAL,GAAc,KAAKjF,IAAL,CAAUy9B,YAAV,CAAuB9rB,GAAvB,CAAd;IACAA,GAAG,IAAI,CAAP;SAEK3M,KAAL,GAAa,KAAKhF,IAAL,CAAUy9B,YAAV,CAAuB9rB,GAAvB,CAAb;IACAA,GAAG,IAAI,CAAP;QAEMosB,QAAQ,GAAG,KAAK/9B,IAAL,CAAU2R,GAAG,EAAb,CAAjB;SACKqsB,UAAL,GAAkBV,eAAe,CAACS,QAAD,CAAjC;SAEKpvB,GAAL,GAAW,IAAX;;;EAGF6D,KAAK,CAAC1S,QAAD,EAAW;QACV,KAAK6O,GAAT,EAAc;;;;SAITA,GAAL,GAAW7O,QAAQ,CAACqF,GAAT,CAAa;MACtBI,IAAI,EAAE,SADgB;MAEtByO,OAAO,EAAE,OAFa;MAGtBiqB,gBAAgB,EAAE,KAAKH,IAHD;MAItBI,KAAK,EAAE,KAAKl5B,KAJU;MAKtBm5B,MAAM,EAAE,KAAKl5B,MALS;MAMtBuB,UAAU,EAAE,KAAKw3B,UANK;MAOtB79B,MAAM,EAAE;KAPC,CAAX,CALc;;;;QAkBV,KAAK69B,UAAL,KAAoB,YAAxB,EAAsC;WAC/BrvB,GAAL,CAAS3O,IAAT,CAAc,QAAd,IAA0B,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,EAAgB,GAAhB,EAAqB,GAArB,EAA0B,GAA1B,EAA+B,GAA/B,EAAoC,GAApC,CAA1B;;;SAGG2O,GAAL,CAAS5Q,GAAT,CAAa,KAAKiC,IAAlB,EAtBc;;WAyBN,KAAKA,IAAL,GAAY,IAApB;;;;;ACxFJ,MAAMo+B,QAAN,CAAe;EACbxiC,WAAW,CAACoE,IAAD,EAAOq7B,KAAP,EAAc;SAClBA,KAAL,GAAaA,KAAb;SACKgD,KAAL,GAAa,IAAIC,GAAJ,CAAQt+B,IAAR,CAAb;SACKgF,KAAL,GAAa,KAAKq5B,KAAL,CAAWr5B,KAAxB;SACKC,MAAL,GAAc,KAAKo5B,KAAL,CAAWp5B,MAAzB;SACKs5B,OAAL,GAAe,KAAKF,KAAL,CAAWE,OAA1B;SACK5vB,GAAL,GAAW,IAAX;;;EAGF6D,KAAK,CAAC1S,QAAD,EAAW;QACV0+B,WAAW,GAAG,KAAlB;SAEK1+B,QAAL,GAAgBA,QAAhB;;QACI,KAAK6O,GAAT,EAAc;;;;QAIR8vB,eAAe,GAAG,KAAKJ,KAAL,CAAWI,eAAnC;QACMC,YAAY,GAAG,KAAKL,KAAL,CAAWM,eAAX,KAA+B,CAApD;SAEKhwB,GAAL,GAAW,KAAK7O,QAAL,CAAcqF,GAAd,CAAkB;MAC3BI,IAAI,EAAE,SADqB;MAE3ByO,OAAO,EAAE,OAFkB;MAG3BiqB,gBAAgB,EAAEQ,eAAe,GAAG,CAAH,GAAO,KAAKJ,KAAL,CAAWP,IAHxB;MAI3BI,KAAK,EAAE,KAAKl5B,KAJe;MAK3Bm5B,MAAM,EAAE,KAAKl5B,MALc;MAM3B9E,MAAM,EAAE;KANC,CAAX;;QASI,CAACs+B,eAAL,EAAsB;UACdxa,MAAM,GAAG,KAAKnkB,QAAL,CAAcqF,GAAd,CAAkB;QAC/By5B,SAAS,EAAEF,YAAY,GAAG,CAAH,GAAO,EADC;QAE/BG,MAAM,EAAE,KAAKR,KAAL,CAAWS,MAFY;QAG/Bb,gBAAgB,EAAE,KAAKI,KAAL,CAAWP,IAHE;QAI/BiB,OAAO,EAAE,KAAK/5B;OAJD,CAAf;WAOK2J,GAAL,CAAS3O,IAAT,CAAc,aAAd,IAA+BikB,MAA/B;MACAA,MAAM,CAAClmB,GAAP;;;QAGE,KAAKsgC,KAAL,CAAWW,OAAX,CAAmBpiC,MAAnB,KAA8B,CAAlC,EAAqC;WAC9B+R,GAAL,CAAS3O,IAAT,CAAc,YAAd,IAA8B,KAAKq+B,KAAL,CAAWL,UAAzC;KADF,MAEO;;UAECgB,OAAO,GAAG,KAAKl/B,QAAL,CAAcqF,GAAd,EAAhB;MACA65B,OAAO,CAACjhC,GAAR,CAAYQ,MAAM,CAACC,IAAP,CAAY,KAAK6/B,KAAL,CAAWW,OAAvB,CAAZ,EAHK;;WAMArwB,GAAL,CAAS3O,IAAT,CAAc,YAAd,IAA8B,CAC5B,SAD4B,EAE5B,WAF4B,EAG5B,KAAKq+B,KAAL,CAAWW,OAAX,CAAmBpiC,MAAnB,GAA4B,CAA5B,GAAgC,CAHJ,EAI5BoiC,OAJ4B,CAA9B;KAxCY;;;;QAkDV,KAAKX,KAAL,CAAWY,YAAX,CAAwBC,SAAxB,IAAqC,IAAzC,EAA+C;;;UAGvChjC,GAAG,GAAG,KAAKmiC,KAAL,CAAWY,YAAX,CAAwBC,SAApC;WACKvwB,GAAL,CAAS3O,IAAT,CAAc,MAAd,IAAwB,CAAC9D,GAAD,EAAMA,GAAN,CAAxB;KAJF,MAKO,IAAI,KAAKmiC,KAAL,CAAWY,YAAX,CAAwBE,GAA5B,EAAiC;;;UAGhC;QAAEA;UAAQ,KAAKd,KAAL,CAAWY,YAA3B;UACMG,IAAI,GAAG,EAAb;;WACK,IAAIv2B,CAAT,IAAcs2B,GAAd,EAAmB;QACjBC,IAAI,CAACriC,IAAL,CAAU8L,CAAV,EAAaA,CAAb;;;WAGG8F,GAAL,CAAS3O,IAAT,CAAc,MAAd,IAAwBo/B,IAAxB;KATK,MAUA,IAAI,KAAKf,KAAL,CAAWY,YAAX,CAAwBI,OAA5B,EAAqC;;;MAG1Cb,WAAW,GAAG,IAAd;aACO,KAAKc,uBAAL,EAAP;KAJK,MAKA,IAAIb,eAAJ,EAAqB;;;;MAI1BD,WAAW,GAAG,IAAd;aACO,KAAKe,iBAAL,EAAP;;;QAGEb,YAAY,IAAI,CAACF,WAArB,EAAkC;aACzB,KAAKgB,UAAL,EAAP;;;SAGG/+B,QAAL;;;EAGFA,QAAQ,GAAG;QACL,KAAKg/B,YAAT,EAAuB;UACfC,KAAK,GAAG,KAAK5/B,QAAL,CAAcqF,GAAd,CAAkB;QAC9BI,IAAI,EAAE,SADwB;QAE9ByO,OAAO,EAAE,OAFqB;QAG9BmqB,MAAM,EAAE,KAAKl5B,MAHiB;QAI9Bi5B,KAAK,EAAE,KAAKl5B,KAJkB;QAK9Bi5B,gBAAgB,EAAE,CALY;QAM9B99B,MAAM,EAAE,aANsB;QAO9BqG,UAAU,EAAE,YAPkB;QAQ9Bm5B,MAAM,EAAE,CAAC,CAAD,EAAI,CAAJ;OARI,CAAd;MAWAD,KAAK,CAAC3hC,GAAN,CAAU,KAAK0hC,YAAf;WACK9wB,GAAL,CAAS3O,IAAT,CAAc,OAAd,IAAyB0/B,KAAzB;KAdO;;;SAkBJ/wB,GAAL,CAAS5Q,GAAT,CAAa,KAAKwgC,OAAlB,EAlBS;;SAqBJF,KAAL,GAAa,IAAb;WACQ,KAAKE,OAAL,GAAe,IAAvB;;;EAGFgB,iBAAiB,GAAG;WACX,KAAKlB,KAAL,CAAWuB,YAAX,CAAwBC,MAAM,IAAI;UACnCrjC,CAAJ,EAAOsjC,CAAP;UACMC,UAAU,GAAG,KAAK1B,KAAL,CAAWS,MAA9B;UACMkB,UAAU,GAAG,KAAKh7B,KAAL,GAAa,KAAKC,MAArC;UACMs5B,OAAO,GAAGhgC,MAAM,CAACsS,KAAP,CAAamvB,UAAU,GAAGD,UAA1B,CAAhB;UACMN,YAAY,GAAGlhC,MAAM,CAACsS,KAAP,CAAamvB,UAAb,CAArB;UAEIliC,CAAC,GAAIgiC,CAAC,GAAGtjC,CAAC,GAAG,CAAjB;UACM8xB,GAAG,GAAGuR,MAAM,CAACjjC,MAAnB,CARuC;;UAUjCqjC,aAAa,GAAG,KAAK5B,KAAL,CAAWP,IAAX,KAAoB,EAApB,GAAyB,CAAzB,GAA6B,CAAnD;;aACOhgC,CAAC,GAAGwwB,GAAX,EAAgB;aACT,IAAI4R,UAAU,GAAG,CAAtB,EAAyBA,UAAU,GAAGH,UAAtC,EAAkDG,UAAU,EAA5D,EAAgE;UAC9D3B,OAAO,CAACuB,CAAC,EAAF,CAAP,GAAeD,MAAM,CAAC/hC,CAAC,EAAF,CAArB;UACAA,CAAC,IAAImiC,aAAL;;;QAEFR,YAAY,CAACjjC,CAAC,EAAF,CAAZ,GAAoBqjC,MAAM,CAAC/hC,CAAC,EAAF,CAA1B;QACAA,CAAC,IAAImiC,aAAL;;;WAGG1B,OAAL,GAAex9B,IAAI,CAACC,WAAL,CAAiBu9B,OAAjB,CAAf;WACKkB,YAAL,GAAoB1+B,IAAI,CAACC,WAAL,CAAiBy+B,YAAjB,CAApB;aACO,KAAKh/B,QAAL,EAAP;KAtBK,CAAP;;;EA0BF6+B,uBAAuB,GAAG;QAClBL,YAAY,GAAG,KAAKZ,KAAL,CAAWY,YAAX,CAAwBI,OAA7C;WACO,KAAKhB,KAAL,CAAWuB,YAAX,CAAwBC,MAAM,IAAI;UACjCJ,YAAY,GAAGlhC,MAAM,CAACsS,KAAP,CAAa,KAAK7L,KAAL,GAAa,KAAKC,MAA/B,CAArB;UAEInH,CAAC,GAAG,CAAR;;WACK,IAAIuS,CAAC,GAAG,CAAR,EAAWtS,GAAG,GAAG8hC,MAAM,CAACjjC,MAA7B,EAAqCyT,CAAC,GAAGtS,GAAzC,EAA8CsS,CAAC,EAA/C,EAAmD;QACjDovB,YAAY,CAAC3hC,CAAC,EAAF,CAAZ,GAAoBmhC,YAAY,CAACY,MAAM,CAACxvB,CAAD,CAAP,CAAhC;;;WAGGovB,YAAL,GAAoB1+B,IAAI,CAACC,WAAL,CAAiBy+B,YAAjB,CAApB;aACO,KAAKh/B,QAAL,EAAP;KATK,CAAP;;;EAaF++B,UAAU,GAAG;SACNnB,KAAL,CAAWuB,YAAX,CAAwBC,MAAM,IAAI;WAC3BtB,OAAL,GAAex9B,IAAI,CAACC,WAAL,CAAiB6+B,MAAjB,CAAf;WACKp/B,QAAL;KAFF;;;;;ACtKJ;;;;AAKA;AAIA,MAAM0/B,QAAN,CAAe;SACNnT,IAAP,CAAYsI,GAAZ,EAAiB+F,KAAjB,EAAwB;QAClBr7B,IAAJ;;QACIzB,MAAM,CAACK,QAAP,CAAgB02B,GAAhB,CAAJ,EAA0B;MACxBt1B,IAAI,GAAGs1B,GAAP;KADF,MAEO,IAAIA,GAAG,YAAYI,WAAnB,EAAgC;MACrC11B,IAAI,GAAGzB,MAAM,CAACC,IAAP,CAAY,IAAIi3B,UAAJ,CAAeH,GAAf,CAAZ,CAAP;KADK,MAEA;UACDpH,KAAJ;;UACKA,KAAK,GAAG,yBAAyBkS,IAAzB,CAA8B9K,GAA9B,CAAb,EAAkD;QAChDt1B,IAAI,GAAGzB,MAAM,CAACC,IAAP,CAAY0vB,KAAK,CAAC,CAAD,CAAjB,EAAsB,QAAtB,CAAP;OADF,MAEO;QACLluB,IAAI,GAAGktB,EAAE,CAACC,YAAH,CAAgBmI,GAAhB,CAAP;;YACI,CAACt1B,IAAL,EAAW;;;;;;QAMXA,IAAI,CAAC,CAAD,CAAJ,KAAY,IAAZ,IAAoBA,IAAI,CAAC,CAAD,CAAJ,KAAY,IAApC,EAA0C;aACjC,IAAIu9B,IAAJ,CAASv9B,IAAT,EAAeq7B,KAAf,CAAP;KADF,MAEO,IAAIr7B,IAAI,CAAC,CAAD,CAAJ,KAAY,IAAZ,IAAoBA,IAAI,CAACvE,QAAL,CAAc,OAAd,EAAuB,CAAvB,EAA0B,CAA1B,MAAiC,KAAzD,EAAgE;aAC9D,IAAI6iC,QAAJ,CAAQt+B,IAAR,EAAcq7B,KAAd,CAAP;KADK,MAEA;YACC,IAAI3/B,KAAJ,CAAU,uBAAV,CAAN;;;;;;AC/BN,kBAAe;EACb2kC,UAAU,GAAG;SACNC,cAAL,GAAsB,EAAtB;WACQ,KAAKC,WAAL,GAAmB,CAA3B;GAHW;;EAMblC,KAAK,CAAC/I,GAAD,EAAMzsB,CAAN,EAAS8b,CAAT,EAA0B;QAAd9oB,OAAc,uEAAJ,EAAI;QACzB2kC,EAAJ,EAAQC,EAAR,EAAYC,EAAZ,EAAgBrC,KAAhB,EAAuBsC,EAAvB,EAA2Bt/B,IAA3B,EAAiCu/B,KAAjC,EAAwCC,WAAxC,EAAqDC,OAArD,EAA8DC,OAA9D;;QACI,OAAOl4B,CAAP,KAAa,QAAjB,EAA2B;MACzBhN,OAAO,GAAGgN,CAAV;MACAA,CAAC,GAAG,IAAJ;KAJ2B;;;QAQvBm4B,iBAAiB,GACrBnlC,OAAO,CAACmlC,iBAAR,IACCnlC,OAAO,CAACmlC,iBAAR,KAA8B,KAA9B,IAAuC,KAAKnlC,OAAL,CAAamlC,iBAFvD;IAIAn4B,CAAC,GAAG,CAACxH,IAAI,GAAGwH,CAAC,IAAI,IAAL,GAAYA,CAAZ,GAAgBhN,OAAO,CAACgN,CAAhC,KAAsC,IAAtC,GAA6CxH,IAA7C,GAAoD,KAAKwH,CAA7D;IACA8b,CAAC,GAAG,CAACic,KAAK,GAAGjc,CAAC,IAAI,IAAL,GAAYA,CAAZ,GAAgB9oB,OAAO,CAAC8oB,CAAjC,KAAuC,IAAvC,GAA8Cic,KAA9C,GAAsD,KAAKjc,CAA/D;;QAEI,OAAO2Q,GAAP,KAAe,QAAnB,EAA6B;MAC3B+I,KAAK,GAAG,KAAKiC,cAAL,CAAoBhL,GAApB,CAAR;;;QAGE,CAAC+I,KAAL,EAAY;UACN/I,GAAG,CAACtwB,KAAJ,IAAaswB,GAAG,CAACrwB,MAArB,EAA6B;QAC3Bo5B,KAAK,GAAG/I,GAAR;OADF,MAEO;QACL+I,KAAK,GAAG,KAAK4C,SAAL,CAAe3L,GAAf,CAAR;;;;QAIA,CAAC+I,KAAK,CAAC1vB,GAAX,EAAgB;MACd0vB,KAAK,CAAC7rB,KAAN,CAAY,IAAZ;;;QAGE,KAAKsB,IAAL,CAAU7N,QAAV,CAAmBo4B,KAAK,CAAChD,KAAzB,KAAmC,IAAvC,EAA6C;WACtCvnB,IAAL,CAAU7N,QAAV,CAAmBo4B,KAAK,CAAChD,KAAzB,IAAkCgD,KAAK,CAAC1vB,GAAxC;;;QAGE;MAAE3J,KAAF;MAASC;QAAWo5B,KAAxB,CAnC6B;;QAsCzB,CAAC2C,iBAAD,IAAsB3C,KAAK,CAACX,WAAN,GAAoB,CAA9C,EAAiD;OAC9C14B,KAAD,EAAQC,MAAR,IAAkB,CAACA,MAAD,EAASD,KAAT,CAAlB;;;QAGEmjB,CAAC,GAAGtsB,OAAO,CAACmJ,KAAR,IAAiBA,KAAzB;QACIge,CAAC,GAAGnnB,OAAO,CAACoJ,MAAR,IAAkBA,MAA1B;;QAEIpJ,OAAO,CAACmJ,KAAR,IAAiB,CAACnJ,OAAO,CAACoJ,MAA9B,EAAsC;UAC9Bi8B,EAAE,GAAG/Y,CAAC,GAAGnjB,KAAf;MACAmjB,CAAC,GAAGnjB,KAAK,GAAGk8B,EAAZ;MACAle,CAAC,GAAG/d,MAAM,GAAGi8B,EAAb;KAHF,MAIO,IAAIrlC,OAAO,CAACoJ,MAAR,IAAkB,CAACpJ,OAAO,CAACmJ,KAA/B,EAAsC;UACrCm8B,EAAE,GAAGne,CAAC,GAAG/d,MAAf;MACAkjB,CAAC,GAAGnjB,KAAK,GAAGm8B,EAAZ;MACAne,CAAC,GAAG/d,MAAM,GAAGk8B,EAAb;KAHK,MAIA,IAAItlC,OAAO,CAAC4wB,KAAZ,EAAmB;MACxBtE,CAAC,GAAGnjB,KAAK,GAAGnJ,OAAO,CAAC4wB,KAApB;MACAzJ,CAAC,GAAG/d,MAAM,GAAGpJ,OAAO,CAAC4wB,KAArB;KAFK,MAGA,IAAI5wB,OAAO,CAACulC,GAAZ,EAAiB;OACrBV,EAAD,EAAKF,EAAL,IAAW3kC,OAAO,CAACulC,GAAnB;MACAX,EAAE,GAAGC,EAAE,GAAGF,EAAV;MACAG,EAAE,GAAG37B,KAAK,GAAGC,MAAb;;UACI07B,EAAE,GAAGF,EAAT,EAAa;QACXtY,CAAC,GAAGuY,EAAJ;QACA1d,CAAC,GAAG0d,EAAE,GAAGC,EAAT;OAFF,MAGO;QACL3d,CAAC,GAAGwd,EAAJ;QACArY,CAAC,GAAGqY,EAAE,GAAGG,EAAT;;KATG,MAWA,IAAI9kC,OAAO,CAACwlC,KAAZ,EAAmB;OACvBX,EAAD,EAAKF,EAAL,IAAW3kC,OAAO,CAACwlC,KAAnB;MACAZ,EAAE,GAAGC,EAAE,GAAGF,EAAV;MACAG,EAAE,GAAG37B,KAAK,GAAGC,MAAb;;UACI07B,EAAE,GAAGF,EAAT,EAAa;QACXzd,CAAC,GAAGwd,EAAJ;QACArY,CAAC,GAAGqY,EAAE,GAAGG,EAAT;OAFF,MAGO;QACLxY,CAAC,GAAGuY,EAAJ;QACA1d,CAAC,GAAG0d,EAAE,GAAGC,EAAT;;;;QAIA9kC,OAAO,CAACulC,GAAR,IAAevlC,OAAO,CAACwlC,KAA3B,EAAkC;UAC5BxlC,OAAO,CAAC07B,KAAR,KAAkB,QAAtB,EAAgC;QAC9B1uB,CAAC,GAAGA,CAAC,GAAG63B,EAAE,GAAG,CAAT,GAAavY,CAAC,GAAG,CAArB;OADF,MAEO,IAAItsB,OAAO,CAAC07B,KAAR,KAAkB,OAAtB,EAA+B;QACpC1uB,CAAC,GAAGA,CAAC,GAAG63B,EAAJ,GAASvY,CAAb;;;UAGEtsB,OAAO,CAACylC,MAAR,KAAmB,QAAvB,EAAiC;QAC/B3c,CAAC,GAAGA,CAAC,GAAG6b,EAAE,GAAG,CAAT,GAAaxd,CAAC,GAAG,CAArB;OADF,MAEO,IAAInnB,OAAO,CAACylC,MAAR,KAAmB,QAAvB,EAAiC;QACtC3c,CAAC,GAAGA,CAAC,GAAG6b,EAAJ,GAASxd,CAAb;;;;QAIA,CAACge,iBAAL,EAAwB;cACd3C,KAAK,CAACX,WAAd;;;aAGO,CAAL;UACE1a,CAAC,GAAG,CAACA,CAAL;UACA2B,CAAC,IAAI3B,CAAL;UAEA6d,WAAW,GAAG,CAAd;;;;aAGG,CAAL;UACE1Y,CAAC,GAAG,CAACA,CAAL;UACAnF,CAAC,GAAG,CAACA,CAAL;UACAna,CAAC,IAAIsf,CAAL;UACAxD,CAAC,IAAI3B,CAAL;UAEA6d,WAAW,GAAG,CAAd;;;;aAGG,CAAL;UACEC,OAAO,GAAGj4B,CAAV;UACAk4B,OAAO,GAAGpc,CAAV;UAEA3B,CAAC,GAAG,CAACA,CAAL;UACAna,CAAC,IAAIsf,CAAL;UAEA0Y,WAAW,GAAG,GAAd;;;;aAGG,CAAL;;;;;aAKK,CAAL;UACEC,OAAO,GAAGj4B,CAAV;UACAk4B,OAAO,GAAGpc,CAAV;WAECwD,CAAD,EAAInF,CAAJ,IAAS,CAACA,CAAD,EAAImF,CAAJ,CAAT;UACAxD,CAAC,IAAI3B,CAAL;UAEA6d,WAAW,GAAG,EAAd;;;;aAGG,CAAL;UACEC,OAAO,GAAGj4B,CAAV;UACAk4B,OAAO,GAAGpc,CAAV;WAECwD,CAAD,EAAInF,CAAJ,IAAS,CAACA,CAAD,EAAImF,CAAJ,CAAT;UACAnF,CAAC,GAAG,CAACA,CAAL;UAEA6d,WAAW,GAAG,EAAd;;;;aAGG,CAAL;UACEC,OAAO,GAAGj4B,CAAV;UACAk4B,OAAO,GAAGpc,CAAV;WAECwD,CAAD,EAAInF,CAAJ,IAAS,CAACA,CAAD,EAAImF,CAAJ,CAAT;UACAnF,CAAC,GAAG,CAACA,CAAL;UACAmF,CAAC,GAAG,CAACA,CAAL;UACAtf,CAAC,IAAIsf,CAAL;UAEA0Y,WAAW,GAAG,EAAd;;;;aAGG,CAAL;UACEC,OAAO,GAAGj4B,CAAV;UACAk4B,OAAO,GAAGpc,CAAV;WAECwD,CAAD,EAAInF,CAAJ,IAAS,CAACA,CAAD,EAAImF,CAAJ,CAAT;UACAnF,CAAC,GAAG,CAACA,CAAL;UACAna,CAAC,IAAIsf,CAAL;UACAxD,CAAC,IAAI3B,CAAL;UAEA6d,WAAW,GAAG,CAAC,EAAf;;;KA5EN,MA+EO;MACL7d,CAAC,GAAG,CAACA,CAAL;MACA2B,CAAC,IAAI3B,CAAL;MACA6d,WAAW,GAAG,CAAd;KAhL2B;;;QAoLzBhlC,OAAO,CAACygC,IAAR,IAAgB,IAApB,EAA0B;WACnBA,IAAL,CAAUzzB,CAAV,EAAa8b,CAAb,EAAgBwD,CAAhB,EAAmBnF,CAAnB,EAAsBnnB,OAAO,CAACygC,IAA9B;;;QAEEzgC,OAAO,CAAC0gC,IAAR,IAAgB,IAApB,EAA0B;WACnBA,IAAL,CAAU1zB,CAAV,EAAa8b,CAAb,EAAgBwD,CAAhB,EAAmBnF,CAAnB,EAAsBnnB,OAAO,CAAC0gC,IAA9B;;;QAEE1gC,OAAO,CAAC2gC,WAAR,IAAuB,IAA3B,EAAiC;WAC1BC,mBAAL,CAAyB5gC,OAAO,CAAC2gC,WAAjC,EAA8C,KAA9C,EAAqD3zB,CAArD,EAAwD8b,CAAxD,EAA2D,IAA3D;KA3L2B;;;QA+LzB,KAAKA,CAAL,KAAWA,CAAf,EAAkB;WACXA,CAAL,IAAU3B,CAAV;;;SAGG+E,IAAL;;QAEI8Y,WAAJ,EAAiB;WACVxU,MAAL,CAAYwU,WAAZ,EAAyB;QACvBrU,MAAM,EAAE,CAACsU,OAAD,EAAUC,OAAV;OADV;;;SAKGtvB,SAAL,CAAe0W,CAAf,EAAkB,CAAlB,EAAqB,CAArB,EAAwBnF,CAAxB,EAA2Bna,CAA3B,EAA8B8b,CAA9B;SACKjP,UAAL,YAAoB2oB,KAAK,CAAChD,KAA1B;SACKrT,OAAL;WAEO,IAAP;GArNW;;EAwNbiZ,SAAS,CAAC3L,GAAD,EAAM;QACT+I,KAAJ;;QACI,OAAO/I,GAAP,KAAe,QAAnB,EAA6B;MAC3B+I,KAAK,GAAG,KAAKiC,cAAL,CAAoBhL,GAApB,CAAR;;;QAGE,CAAC+I,KAAL,EAAY;MACVA,KAAK,GAAG8B,QAAQ,CAACnT,IAAT,CAAcsI,GAAd,aAAuB,EAAE,KAAKiL,WAA9B,EAAR;;UACI,OAAOjL,GAAP,KAAe,QAAnB,EAA6B;aACtBgL,cAAL,CAAoBhL,GAApB,IAA2B+I,KAA3B;;;;WAIGA,KAAP;;;CArOJ;;ACFA,uBAAe;EACbkD,QAAQ,CAAC14B,CAAD,EAAI8b,CAAJ,EAAOwD,CAAP,EAAUnF,CAAV,EAAannB,OAAb,EAAsB;IAC5BA,OAAO,CAAC0J,IAAR,GAAe,OAAf;IACA1J,OAAO,CAAC2lC,IAAR,GAAe,KAAKC,YAAL,CAAkB54B,CAAlB,EAAqB8b,CAArB,EAAwBwD,CAAxB,EAA2BnF,CAA3B,CAAf;IACAnnB,OAAO,CAAC6lC,MAAR,GAAiB,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,CAAjB;;QAEI7lC,OAAO,CAACmY,OAAR,KAAoB,MAApB,IAA8B,OAAOnY,OAAO,CAAC8lC,CAAf,KAAqB,WAAvD,EAAoE;MAClE9lC,OAAO,CAAC8lC,CAAR,GAAY,KAAK,CAAjB,CADkE;;;QAIhE9lC,OAAO,CAACmY,OAAR,KAAoB,MAAxB,EAAgC;UAC1BnY,OAAO,CAACinB,CAAR,IAAa,IAAjB,EAAuB;QACrBjnB,OAAO,CAACinB,CAAR,GAAY,KAAKhR,eAAL,CAAqBjW,OAAO,CAAC+V,KAAR,IAAiB,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,CAAtC,CAAZ;;KAXwB;;;WAcrB/V,OAAO,CAAC+V,KAAf;;QAEI,OAAO/V,OAAO,CAAC+lC,IAAf,KAAwB,QAA5B,EAAsC;MACpC/lC,OAAO,CAAC+lC,IAAR,GAAe,IAAI1jC,MAAJ,CAAWrC,OAAO,CAAC+lC,IAAnB,CAAf;KAjB0B;;;SAqBvB,IAAI3lC,GAAT,IAAgBJ,OAAhB,EAAyB;UACjBK,GAAG,GAAGL,OAAO,CAACI,GAAD,CAAnB;MACAJ,OAAO,CAACI,GAAG,CAAC,CAAD,CAAH,CAAO8I,WAAP,KAAuB9I,GAAG,CAACuB,KAAJ,CAAU,CAAV,CAAxB,CAAP,GAA+CtB,GAA/C;;;QAGIiJ,GAAG,GAAG,KAAKA,GAAL,CAAStJ,OAAT,CAAZ;SACKiY,IAAL,CAAUrN,WAAV,CAAsB1J,IAAtB,CAA2BoI,GAA3B;IACAA,GAAG,CAACpH,GAAJ;WACO,IAAP;GA9BW;;EAiCb8jC,IAAI,CAACh5B,CAAD,EAAI8b,CAAJ,EAAOwD,CAAP,EAAUnF,CAAV,EAAaoK,QAAb,EAAqC;QAAdvxB,OAAc,uEAAJ,EAAI;IACvCA,OAAO,CAACmY,OAAR,GAAkB,MAAlB;IACAnY,OAAO,CAAC+J,QAAR,GAAmB,IAAI1H,MAAJ,CAAWkvB,QAAX,CAAnB;IACAvxB,OAAO,CAACimC,IAAR,GAAe,SAAf;;QACIjmC,OAAO,CAAC+V,KAAR,IAAiB,IAArB,EAA2B;MACzB/V,OAAO,CAAC+V,KAAR,GAAgB,CAAC,GAAD,EAAM,GAAN,EAAW,EAAX,CAAhB;;;WAEK,KAAK2vB,QAAL,CAAc14B,CAAd,EAAiB8b,CAAjB,EAAoBwD,CAApB,EAAuBnF,CAAvB,EAA0BnnB,OAA1B,CAAP;GAxCW;;EA2Cb0gC,IAAI,CAAC1zB,CAAD,EAAI8b,CAAJ,EAAOwD,CAAP,EAAUnF,CAAV,EAAanK,IAAb,EAAiC;QAAdhd,OAAc,uEAAJ,EAAI;IACnCA,OAAO,CAACmY,OAAR,GAAkB,MAAlB;IACAnY,OAAO,CAACgnB,CAAR,GAAY,KAAK1d,GAAL,CAAS;MACnBiP,CAAC,EAAE,MADgB;MAEnB2tB,CAAC,EAAE,IAAI7jC,MAAJ,CAAW2a,IAAX;KAFO,CAAZ;IAIAhd,OAAO,CAACgnB,CAAR,CAAU9kB,GAAV;WACO,KAAKwjC,QAAL,CAAc14B,CAAd,EAAiB8b,CAAjB,EAAoBwD,CAApB,EAAuBnF,CAAvB,EAA0BnnB,OAA1B,CAAP;GAlDW;;EAqDbygC,IAAI,CAACzzB,CAAD,EAAI8b,CAAJ,EAAOwD,CAAP,EAAUnF,CAAV,EAAagf,GAAb,EAAgC;QAAdnmC,OAAc,uEAAJ,EAAI;IAClCA,OAAO,CAACmY,OAAR,GAAkB,MAAlB;;QAEI,OAAOguB,GAAP,KAAe,QAAnB,EAA6B;;UAErBC,KAAK,GAAG,KAAKx8B,KAAL,CAAWzF,IAAX,CAAgB0F,KAAhB,CAAsB1F,IAApC;;UACIgiC,GAAG,IAAI,CAAP,IAAYA,GAAG,GAAGC,KAAK,CAACC,IAAN,CAAWtlC,MAAjC,EAAyC;QACvCf,OAAO,CAACgnB,CAAR,GAAY,KAAK1d,GAAL,CAAS;UACnBiP,CAAC,EAAE,MADgB;UAEnB2tB,CAAC,EAAE,CAACE,KAAK,CAACC,IAAN,CAAWF,GAAX,CAAD,EAAkB,KAAlB,EAAyB,IAAzB,EAA+B,IAA/B,EAAqC,IAArC;SAFO,CAAZ;QAIAnmC,OAAO,CAACgnB,CAAR,CAAU9kB,GAAV;OALF,MAMO;cACC,IAAIrC,KAAJ,oCAAsCsmC,GAAtC,EAAN;;KAVJ,MAYO;;MAELnmC,OAAO,CAACgnB,CAAR,GAAY,KAAK1d,GAAL,CAAS;QACnBiP,CAAC,EAAE,KADgB;QAEnB+tB,GAAG,EAAE,IAAIjkC,MAAJ,CAAW8jC,GAAX;OAFK,CAAZ;MAIAnmC,OAAO,CAACgnB,CAAR,CAAU9kB,GAAV;;;WAGK,KAAKwjC,QAAL,CAAc14B,CAAd,EAAiB8b,CAAjB,EAAoBwD,CAApB,EAAuBnF,CAAvB,EAA0BnnB,OAA1B,CAAP;GA7EW;;EAgFbumC,OAAO,CAACv5B,CAAD,EAAI8b,CAAJ,EAAOwD,CAAP,EAAUnF,CAAV,EAA2B;QAAdnnB,OAAc,uEAAJ,EAAI;;QAC1B,CAAC+Z,EAAD,EAAKC,EAAL,EAASC,EAAT,EAAaC,EAAb,IAAmB,KAAK0rB,YAAL,CAAkB54B,CAAlB,EAAqB8b,CAArB,EAAwBwD,CAAxB,EAA2BnF,CAA3B,CAAzB;;IACAnnB,OAAO,CAACwmC,UAAR,GAAqB,CAACzsB,EAAD,EAAKG,EAAL,EAASD,EAAT,EAAaC,EAAb,EAAiBH,EAAjB,EAAqBC,EAArB,EAAyBC,EAAzB,EAA6BD,EAA7B,CAArB;IACAha,OAAO,CAAC+J,QAAR,GAAmB,IAAI1H,MAAJ,EAAnB;WACO,KAAKqjC,QAAL,CAAc14B,CAAd,EAAiB8b,CAAjB,EAAoBwD,CAApB,EAAuBnF,CAAvB,EAA0BnnB,OAA1B,CAAP;GApFW;;EAuFbymC,SAAS,CAACz5B,CAAD,EAAI8b,CAAJ,EAAOwD,CAAP,EAAUnF,CAAV,EAA2B;QAAdnnB,OAAc,uEAAJ,EAAI;IAClCA,OAAO,CAACmY,OAAR,GAAkB,WAAlB;;QACInY,OAAO,CAAC+V,KAAR,IAAiB,IAArB,EAA2B;MACzB/V,OAAO,CAAC+V,KAAR,GAAgB,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAAhB;;;WAEK,KAAKwwB,OAAL,CAAav5B,CAAb,EAAgB8b,CAAhB,EAAmBwD,CAAnB,EAAsBnF,CAAtB,EAAyBnnB,OAAzB,CAAP;GA5FW;;EA+Fb6gC,SAAS,CAAC7zB,CAAD,EAAI8b,CAAJ,EAAOwD,CAAP,EAAUnF,CAAV,EAA2B;QAAdnnB,OAAc,uEAAJ,EAAI;IAClCA,OAAO,CAACmY,OAAR,GAAkB,WAAlB;WACO,KAAKouB,OAAL,CAAav5B,CAAb,EAAgB8b,CAAhB,EAAmBwD,CAAnB,EAAsBnF,CAAtB,EAAyBnnB,OAAzB,CAAP;GAjGW;;EAoGb+gC,MAAM,CAAC/zB,CAAD,EAAI8b,CAAJ,EAAOwD,CAAP,EAAUnF,CAAV,EAA2B;QAAdnnB,OAAc,uEAAJ,EAAI;IAC/BA,OAAO,CAACmY,OAAR,GAAkB,WAAlB;WACO,KAAKouB,OAAL,CAAav5B,CAAb,EAAgB8b,CAAhB,EAAmBwD,CAAnB,EAAsBnF,CAAtB,EAAyBnnB,OAAzB,CAAP;GAtGW;;EAyGb0mC,cAAc,CAAC3sB,EAAD,EAAKC,EAAL,EAASC,EAAT,EAAaC,EAAb,EAA+B;QAAdla,OAAc,uEAAJ,EAAI;IAC3CA,OAAO,CAACmY,OAAR,GAAkB,MAAlB;IACAnY,OAAO,CAAC+J,QAAR,GAAmB,IAAI1H,MAAJ,EAAnB;IACArC,OAAO,CAAConB,CAAR,GAAY,CAACrN,EAAD,EAAK,KAAK9B,IAAL,CAAU7O,MAAV,GAAmB4Q,EAAxB,EAA4BC,EAA5B,EAAgC,KAAKhC,IAAL,CAAU7O,MAAV,GAAmB8Q,EAAnD,CAAZ;WACO,KAAKwrB,QAAL,CAAc3rB,EAAd,EAAkBC,EAAlB,EAAsBC,EAAtB,EAA0BC,EAA1B,EAA8Bla,OAA9B,CAAP;GA7GW;;EAgHb2mC,cAAc,CAAC35B,CAAD,EAAI8b,CAAJ,EAAOwD,CAAP,EAAUnF,CAAV,EAA2B;QAAdnnB,OAAc,uEAAJ,EAAI;IACvCA,OAAO,CAACmY,OAAR,GAAkB,QAAlB;IACAnY,OAAO,CAAC+J,QAAR,GAAmB,IAAI1H,MAAJ,EAAnB;WACO,KAAKqjC,QAAL,CAAc14B,CAAd,EAAiB8b,CAAjB,EAAoBwD,CAApB,EAAuBnF,CAAvB,EAA0BnnB,OAA1B,CAAP;GAnHW;;EAsHb4mC,iBAAiB,CAAC55B,CAAD,EAAI8b,CAAJ,EAAOwD,CAAP,EAAUnF,CAAV,EAA2B;QAAdnnB,OAAc,uEAAJ,EAAI;IAC1CA,OAAO,CAACmY,OAAR,GAAkB,QAAlB;IACAnY,OAAO,CAAC+J,QAAR,GAAmB,IAAI1H,MAAJ,EAAnB;WACO,KAAKqjC,QAAL,CAAc14B,CAAd,EAAiB8b,CAAjB,EAAoBwD,CAApB,EAAuBnF,CAAvB,EAA0BnnB,OAA1B,CAAP;GAzHW;;EA4Hb6mC,cAAc,CAAC75B,CAAD,EAAI8b,CAAJ,EAAOwD,CAAP,EAAUnF,CAAV,EAAaoL,IAAb,EAAiC;QAAdvyB,OAAc,uEAAJ,EAAI;IAC7CA,OAAO,CAACmY,OAAR,GAAkB,UAAlB;IACAnY,OAAO,CAAC+J,QAAR,GAAmB,IAAI1H,MAAJ,CAAWkwB,IAAX,CAAnB;IACAvyB,OAAO,CAAC8mC,EAAR,GAAa,IAAIzkC,MAAJ,EAAb;WACO,KAAKqjC,QAAL,CAAc14B,CAAd,EAAiB8b,CAAjB,EAAoBwD,CAApB,EAAuBnF,CAAvB,EAA0BnnB,OAA1B,CAAP;GAhIW;;EAmIb+mC,cAAc,CAAC/5B,CAAD,EAAI8b,CAAJ,EAAOwD,CAAP,EAAUnF,CAAV,EAAsC;QAAzB6f,IAAyB,uEAAlB,EAAkB;QAAdhnC,OAAc,uEAAJ,EAAI;;QAE5CinC,QAAQ,GAAG,KAAKD,IAAL,CACfA,IAAI,CAACvN,GADU,EAEfj5B,MAAM,CAAC2/B,MAAP,CAAc;MAAE+G,MAAM,EAAE;KAAxB,EAAgCF,IAAhC,CAFe,CAAjB;IAKAhnC,OAAO,CAACmY,OAAR,GAAkB,gBAAlB;IACAnY,OAAO,CAACmnC,EAAR,GAAaF,QAAb,CARkD;;QAW9CjnC,OAAO,CAAC+J,QAAZ,EAAsB;MACpB/J,OAAO,CAAC+J,QAAR,GAAmB,IAAI1H,MAAJ,CAAWrC,OAAO,CAAC+J,QAAnB,CAAnB;KADF,MAEO,IAAIk9B,QAAQ,CAAC9iC,IAAT,CAAcijC,IAAlB,EAAwB;MAC7BpnC,OAAO,CAAC+J,QAAR,GAAmBk9B,QAAQ,CAAC9iC,IAAT,CAAcijC,IAAjC;;;WAGK,KAAK1B,QAAL,CAAc14B,CAAd,EAAiB8b,CAAjB,EAAoBwD,CAApB,EAAuBnF,CAAvB,EAA0BnnB,OAA1B,CAAP;GApJW;;EAuJb4lC,YAAY,CAAC7rB,EAAD,EAAKC,EAAL,EAASsS,CAAT,EAAYnF,CAAZ,EAAe;;QAErBjN,EAAE,GAAGF,EAAT;IACAA,EAAE,IAAImN,CAAN,CAHyB;;QAMrBlN,EAAE,GAAGF,EAAE,GAAGuS,CAAd,CANyB;;QASnB,CAAClT,EAAD,EAAKC,EAAL,EAASC,EAAT,EAAaC,EAAb,EAAiBC,EAAjB,EAAqBC,EAArB,IAA2B,KAAKC,IAAtC;IACAK,EAAE,GAAGX,EAAE,GAAGW,EAAL,GAAUT,EAAE,GAAGU,EAAf,GAAoBR,EAAzB;IACAQ,EAAE,GAAGX,EAAE,GAAGU,EAAL,GAAUR,EAAE,GAAGS,EAAf,GAAoBP,EAAzB;IACAQ,EAAE,GAAGb,EAAE,GAAGa,EAAL,GAAUX,EAAE,GAAGY,EAAf,GAAoBV,EAAzB;IACAU,EAAE,GAAGb,EAAE,GAAGY,EAAL,GAAUV,EAAE,GAAGW,EAAf,GAAoBT,EAAzB;WAEO,CAACM,EAAD,EAAKC,EAAL,EAASC,EAAT,EAAaC,EAAb,CAAP;;;CAtKJ;;ACAA,MAAMmtB,UAAN,CAAiB;EACftnC,WAAW,CAACkE,QAAD,EAAWqjC,MAAX,EAAmBC,KAAnB,EAA0BC,IAA1B,EAA+D;QAA/BxnC,OAA+B,uEAArB;MAAEynC,QAAQ,EAAE;KAAS;SACnExjC,QAAL,GAAgBA,QAAhB;SACKjE,OAAL,GAAeA,OAAf;SACK0nC,WAAL,GAAmB,EAAnB;;QAEIF,IAAI,KAAK,IAAb,EAAmB;WACZE,WAAL,CAAiB,MAAjB,IAA2B,CAACF,IAAI,CAAC/9B,UAAN,EAAkB,KAAlB,CAA3B;;;QAGE69B,MAAM,KAAK,IAAf,EAAqB;WACdI,WAAL,CAAiB,QAAjB,IAA6BJ,MAA7B;;;QAGEC,KAAK,KAAK,IAAd,EAAoB;WACbG,WAAL,CAAiB,OAAjB,IAA4B,IAAIrlC,MAAJ,CAAWklC,KAAX,CAA5B;;;SAGG99B,UAAL,GAAkB,KAAKxF,QAAL,CAAcqF,GAAd,CAAkB,KAAKo+B,WAAvB,CAAlB;SACKC,QAAL,GAAgB,EAAhB;;;EAGFC,OAAO,CAACL,KAAD,EAAuC;QAA/BvnC,OAA+B,uEAArB;MAAEynC,QAAQ,EAAE;KAAS;QACtCjc,MAAM,GAAG,IAAI6b,UAAJ,CACb,KAAKpjC,QADQ,EAEb,KAAKwF,UAFQ,EAGb89B,KAHa,EAIb,KAAKtjC,QAAL,CAAcgU,IAJD,EAKbjY,OALa,CAAf;SAOK2nC,QAAL,CAAczmC,IAAd,CAAmBsqB,MAAnB;WAEOA,MAAP;;;EAGFqc,UAAU,GAAG;QACP,KAAKF,QAAL,CAAc5mC,MAAd,GAAuB,CAA3B,EAA8B;UACxB,KAAKf,OAAL,CAAaynC,QAAjB,EAA2B;aACpBC,WAAL,CAAiBI,KAAjB,GAAyB,KAAKH,QAAL,CAAc5mC,MAAvC;;;UAGIC,KAAK,GAAG,KAAK2mC,QAAL,CAAc,CAAd,CAAd;UACE1mC,IAAI,GAAG,KAAK0mC,QAAL,CAAc,KAAKA,QAAL,CAAc5mC,MAAd,GAAuB,CAArC,CADT;WAEK2mC,WAAL,CAAiBK,KAAjB,GAAyB/mC,KAAK,CAACyI,UAA/B;WACKi+B,WAAL,CAAiBM,IAAjB,GAAwB/mC,IAAI,CAACwI,UAA7B;;WAEK,IAAIxH,CAAC,GAAG,CAAR,EAAWwwB,GAAG,GAAG,KAAKkV,QAAL,CAAc5mC,MAApC,EAA4CkB,CAAC,GAAGwwB,GAAhD,EAAqDxwB,CAAC,EAAtD,EAA0D;YAClDgmC,KAAK,GAAG,KAAKN,QAAL,CAAc1lC,CAAd,CAAd;;YACIA,CAAC,GAAG,CAAR,EAAW;UACTgmC,KAAK,CAACP,WAAN,CAAkBQ,IAAlB,GAAyB,KAAKP,QAAL,CAAc1lC,CAAC,GAAG,CAAlB,EAAqBwH,UAA9C;;;YAEExH,CAAC,GAAG,KAAK0lC,QAAL,CAAc5mC,MAAd,GAAuB,CAA/B,EAAkC;UAChCknC,KAAK,CAACP,WAAN,CAAkBS,IAAlB,GAAyB,KAAKR,QAAL,CAAc1lC,CAAC,GAAG,CAAlB,EAAqBwH,UAA9C;;;QAEFw+B,KAAK,CAACJ,UAAN;;;;WAIG,KAAKp+B,UAAL,CAAgBvH,GAAhB,EAAP;;;;;ACxDJ,mBAAe;EACbkmC,WAAW,GAAG;WACJ,KAAKC,OAAL,GAAe,IAAIhB,UAAJ,CAAe,IAAf,EAAqB,IAArB,EAA2B,IAA3B,EAAiC,IAAjC,CAAvB;GAFW;;EAKbQ,UAAU,GAAG;SACNQ,OAAL,CAAaR,UAAb;;QACI,KAAKQ,OAAL,CAAaV,QAAb,CAAsB5mC,MAAtB,GAA+B,CAAnC,EAAsC;WAC/B6I,KAAL,CAAWzF,IAAX,CAAgBmkC,QAAhB,GAA2B,KAAKD,OAAL,CAAa5+B,UAAxC;aACQ,KAAKG,KAAL,CAAWzF,IAAX,CAAgBokC,QAAhB,GAA2B,aAAnC;;;;CATN;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACFA;;;;AAKA,MAAMC,mBAAN,CAA0B;EACxBzoC,WAAW,CAAC0oC,OAAD,EAAUC,IAAV,EAAgB;SACpBC,IAAL,GAAY,CAAC;MAAEF,OAAF;MAAWC;KAAZ,CAAZ;;;EAGFxnC,IAAI,CAAC0nC,aAAD,EAAgB;IAClBA,aAAa,CAACD,IAAd,CAAmBxtB,OAAnB,CAA4B7R,GAAD,IAAS,KAAKq/B,IAAL,CAAUznC,IAAV,CAAeoI,GAAf,CAApC;;;;;ACXJ;;;;AAKA;AAEA,MAAMu/B,mBAAN,CAA0B;EACxB9oC,WAAW,CAACkE,QAAD,EAAW6kC,IAAX,EAAgD;QAA/B9oC,OAA+B,uEAArB,EAAqB;QAAjB2nC,QAAiB,uEAAN,IAAM;SACpD1jC,QAAL,GAAgBA,QAAhB;SAEK8kC,SAAL,GAAiB,KAAjB;SACKC,MAAL,GAAc,KAAd;SACKC,QAAL,GAAgB,KAAhB;SACKx/B,UAAL,GAAkBxF,QAAQ,CAACqF,GAAT,CAAa;;MAE7BiP,CAAC,EAAEuwB;KAFa,CAAlB;QAKM3kC,IAAI,GAAG,KAAKsF,UAAL,CAAgBtF,IAA7B;;QAEIzC,KAAK,CAAC6B,OAAN,CAAcvD,OAAd,KAA0B,KAAKkpC,aAAL,CAAmBlpC,OAAnB,CAA9B,EAA2D;MACzD2nC,QAAQ,GAAG3nC,OAAX;MACAA,OAAO,GAAG,EAAV;;;QAGE,OAAOA,OAAO,CAACunC,KAAf,KAAyB,WAA7B,EAA0C;MACxCpjC,IAAI,CAACsjB,CAAL,GAAS,IAAIplB,MAAJ,CAAWrC,OAAO,CAACunC,KAAnB,CAAT;;;QAEE,OAAOvnC,OAAO,CAACmpC,IAAf,KAAwB,WAA5B,EAAyC;MACvChlC,IAAI,CAACilC,IAAL,GAAY,IAAI/mC,MAAJ,CAAWrC,OAAO,CAACmpC,IAAnB,CAAZ;;;QAEE,OAAOnpC,OAAO,CAACqpC,GAAf,KAAuB,WAA3B,EAAwC;MACtCllC,IAAI,CAACmlC,GAAL,GAAW,IAAIjnC,MAAJ,CAAWrC,OAAO,CAACqpC,GAAnB,CAAX;;;QAEE,OAAOrpC,OAAO,CAACynC,QAAf,KAA4B,WAAhC,EAA6C;MAC3CtjC,IAAI,CAAColC,CAAL,GAAS,IAAIlnC,MAAJ,CAAWrC,OAAO,CAACynC,QAAnB,CAAT;;;QAEE,OAAOznC,OAAO,CAACwpC,MAAf,KAA0B,WAA9B,EAA2C;MACzCrlC,IAAI,CAACslC,UAAL,GAAkB,IAAIpnC,MAAJ,CAAWrC,OAAO,CAACwpC,MAAnB,CAAlB;;;SAGGE,SAAL,GAAiB,EAAjB;;QAEI/B,QAAJ,EAAc;UACR,CAACjmC,KAAK,CAAC6B,OAAN,CAAcokC,QAAd,CAAL,EAA8B;QAC5BA,QAAQ,GAAG,CAACA,QAAD,CAAX;;;MAEFA,QAAQ,CAACxsB,OAAT,CAAkB8sB,KAAD,IAAW,KAAK9nC,GAAL,CAAS8nC,KAAT,CAA5B;WACK/lC,GAAL;;;;EAIJ/B,GAAG,CAAC8nC,KAAD,EAAQ;QACL,KAAKe,MAAT,EAAiB;YACT,IAAInpC,KAAJ,uDAAN;;;QAGE,CAAC,KAAKqpC,aAAL,CAAmBjB,KAAnB,CAAL,EAAgC;YACxB,IAAIpoC,KAAJ,mCAAN;;;QAGEooC,KAAK,YAAYY,mBAArB,EAA0C;MACxCZ,KAAK,CAAC0B,SAAN,CAAgB,KAAKlgC,UAArB;;UACI,KAAKs/B,SAAT,EAAoB;QAClBd,KAAK,CAAC2B,WAAN;;;;QAIA3B,KAAK,YAAYO,mBAArB,EAA0C;WACnCqB,uBAAL,CAA6B5B,KAA7B;;;QAGE,OAAOA,KAAP,KAAiB,UAAjB,IAA+B,KAAKc,SAAxC,EAAmD;;MAEjDd,KAAK,GAAG,KAAK6B,kBAAL,CAAwB7B,KAAxB,CAAR;;;SAGGyB,SAAL,CAAexoC,IAAf,CAAoB+mC,KAApB;;WAEO,IAAP;;;EAGF4B,uBAAuB,CAACxgC,OAAD,EAAU;IAC/BA,OAAO,CAACs/B,IAAR,CAAaxtB,OAAb,CAAqB,UAAuB;UAAtB;QAAEstB,OAAF;QAAWC;OAAW;UACpCqB,iBAAiB,GAAG,KAAK9lC,QAAL,CAAc+lC,mBAAd,GACvB1pC,GADuB,CACnBmoC,OAAO,CAACtkC,IAAR,CAAa4G,aADM,CAA1B;MAEAg/B,iBAAiB,CAACrB,IAAD,CAAjB,GAA0B,KAAKj/B,UAA/B;KAHF;;;EAOFkgC,SAAS,CAACM,SAAD,EAAY;QACf,KAAKxgC,UAAL,CAAgBtF,IAAhB,CAAqBwN,CAAzB,EAA4B;YACpB,IAAI9R,KAAJ,mDAAN;;;SAGG4J,UAAL,CAAgBtF,IAAhB,CAAqBwN,CAArB,GAAyBs4B,SAAzB;;SAEKC,MAAL;;;EAGFN,WAAW,GAAG;QACR,KAAKb,SAAT,EAAoB;;;;SAIfW,SAAL,CAAevuB,OAAf,CAAuB,CAAC8sB,KAAD,EAAQhzB,KAAR,KAAkB;UACnCgzB,KAAK,YAAYY,mBAArB,EAA0C;QACxCZ,KAAK,CAAC2B,WAAN;;;UAEE,OAAO3B,KAAP,KAAiB,UAArB,EAAiC;aAC1ByB,SAAL,CAAez0B,KAAf,IAAwB,KAAK60B,kBAAL,CAAwB7B,KAAxB,CAAxB;;KALJ;;SASKc,SAAL,GAAiB,IAAjB;;SAEKmB,MAAL;;;EAGFhoC,GAAG,GAAG;QACA,KAAK8mC,MAAT,EAAiB;;;;SAIZU,SAAL,CACGh8B,MADH,CACWu6B,KAAD,IAAWA,KAAK,YAAYY,mBADtC,EAEG1tB,OAFH,CAEY8sB,KAAD,IAAWA,KAAK,CAAC/lC,GAAN,EAFtB;;SAIK8mC,MAAL,GAAc,IAAd;;SAEKkB,MAAL;;;EAGFhB,aAAa,CAACjB,KAAD,EAAQ;WACZA,KAAK,YAAYY,mBAAjB,IACHZ,KAAK,YAAYO,mBADd,IAEH,OAAOP,KAAP,KAAiB,UAFrB;;;EAKF6B,kBAAkB,CAACK,OAAD,EAAU;QACpB9gC,OAAO,GAAG,KAAKpF,QAAL,CAAco6B,oBAAd,CAAmC,KAAK50B,UAAL,CAAgBtF,IAAhB,CAAqBoU,CAAxD,CAAhB;IACA4xB,OAAO;SACFlmC,QAAL,CAAcmmC,gBAAd;;SAEKP,uBAAL,CAA6BxgC,OAA7B;;WAEOA,OAAP;;;EAGFghC,YAAY,GAAG;QACT,CAAC,KAAK5gC,UAAL,CAAgBtF,IAAhB,CAAqBwN,CAAtB,IAA2B,CAAC,KAAKq3B,MAArC,EAA6C;aACpC,KAAP;;;WAGK,KAAKU,SAAL,CAAetc,KAAf,CAAsB6a,KAAD,IAAW;UACjC,OAAOA,KAAP,KAAiB,UAArB,EAAiC;eACxB,KAAP;;;UAEEA,KAAK,YAAYY,mBAArB,EAA0C;eACjCZ,KAAK,CAACoC,YAAN,EAAP;;;aAEK,IAAP;KAPK,CAAP;;;EAWFH,MAAM,GAAG;QACH,KAAKjB,QAAL,IAAiB,CAAC,KAAKoB,YAAL,EAAtB,EAA2C;;;;SAItC5gC,UAAL,CAAgBtF,IAAhB,CAAqBmmC,CAArB,GAAyB,EAAzB;;SAEKZ,SAAL,CAAevuB,OAAf,CAAwB8sB,KAAD,IAAW,KAAKsC,WAAL,CAAiBtC,KAAjB,CAAlC;;SAEKx+B,UAAL,CAAgBvH,GAAhB,GATO;;;;SAcFwnC,SAAL,GAAiB,EAAjB;SACKjgC,UAAL,CAAgBtF,IAAhB,CAAqBmmC,CAArB,GAAyB,IAAzB;SAEKrB,QAAL,GAAgB,IAAhB;;;EAGFsB,WAAW,CAACtC,KAAD,EAAQ;QACbA,KAAK,YAAYY,mBAArB,EAA0C;WACnCp/B,UAAL,CAAgBtF,IAAhB,CAAqBmmC,CAArB,CAAuBppC,IAAvB,CAA4B+mC,KAAK,CAACx+B,UAAlC;;;QAGEw+B,KAAK,YAAYO,mBAArB,EAA0C;MACxCP,KAAK,CAACU,IAAN,CAAWxtB,OAAX,CAAmB,WAAuB;YAAtB;UAAEstB,OAAF;UAAWC;SAAW;;YACpC,CAAC,KAAKj/B,UAAL,CAAgBtF,IAAhB,CAAqBqmC,EAA1B,EAA8B;eACvB/gC,UAAL,CAAgBtF,IAAhB,CAAqBqmC,EAArB,GAA0B/B,OAA1B;;;YAGE,KAAKh/B,UAAL,CAAgBtF,IAAhB,CAAqBqmC,EAArB,KAA4B/B,OAAhC,EAAyC;eAClCh/B,UAAL,CAAgBtF,IAAhB,CAAqBmmC,CAArB,CAAuBppC,IAAvB,CAA4BwnC,IAA5B;SADF,MAEO;eACAj/B,UAAL,CAAgBtF,IAAhB,CAAqBmmC,CAArB,CAAuBppC,IAAvB,CAA4B;YAC1BwI,IAAI,EAAE,KADoB;YAE1B8gC,EAAE,EAAE/B,OAFsB;YAG1BgC,IAAI,EAAE/B;WAHR;;OARJ;;;;;;AChMN;;;AAIA;AAEA,MAAMgC,aAAN,SAA4B5qC,OAA5B,CAAoC;EAClCe,YAAY,CAACF,CAAD,EAAIC,CAAJ,EAAO;WACVub,QAAQ,CAACxb,CAAD,CAAR,GAAcwb,QAAQ,CAACvb,CAAD,CAA7B;;;EAGFU,SAAS,GAAG;WACH,MAAP;;;EAGFD,WAAW,CAAC+J,CAAD,EAAI;WACN+Q,QAAQ,CAAC/Q,CAAD,CAAf;;;;;ACNJ,oBAAe;EAEbu/B,YAAY,CAAC3qC,OAAD,EAAU;SACf4qC,cAAL,GAAsB,EAAtB;;QAEI5qC,OAAO,CAAC6qC,MAAZ,EAAoB;WACbC,qBAAL,GAA6B3mC,IAA7B,CAAkC4mC,MAAlC,GAA2C,IAA3C;WACKC,iBAAL;;GAPS;;EAWbC,WAAW,CAAC/T,GAAD,EAAsB;QAAhBl3B,OAAgB,uEAAN,IAAM;;QAC3Bk3B,GAAG,KAAK,UAAR,IAAuBl3B,OAAO,IAAIA,OAAO,CAAC0oC,IAA9C,EAAqD;UAC/CwC,OAAO,GAAG,CAAd;WACKjzB,IAAL,CAAUhO,QAAV,CAAmBkR,OAAnB,CAA4BgwB,OAAD,IAAa;YAClCD,OAAO,IAAIC,OAAO,CAACvC,aAAnB,IAAoCuC,OAAO,CAACjU,GAAR,KAAgB,UAAxD,EAAoE;UAClEgU,OAAO;;OAFX;;aAKOA,OAAO,EAAd,EAAkB;aACXd,gBAAL;;;;QAIA,CAACpqC,OAAL,EAAc;WACPiY,IAAL,CAAUhO,QAAV,CAAmB/I,IAAnB,CAAwB;QAAEg2B;OAA1B;WACKrd,UAAL,YAAoBqd,GAApB;aACO,IAAP;;;SAGGjf,IAAL,CAAUhO,QAAV,CAAmB/I,IAAnB,CAAwB;MAAEg2B,GAAF;MAAOl3B;KAA/B;QAEMyJ,UAAU,GAAG,EAAnB;;QAEI,OAAOzJ,OAAO,CAAC0oC,IAAf,KAAwB,WAA5B,EAAyC;MACvCj/B,UAAU,CAACghC,IAAX,GAAkBzqC,OAAO,CAAC0oC,IAA1B;;;QAEExR,GAAG,KAAK,UAAZ,EAAwB;UAClB,OAAOl3B,OAAO,CAAC8oC,IAAf,KAAwB,QAA5B,EAAsC;QACpCr/B,UAAU,CAACC,IAAX,GAAkB1J,OAAO,CAAC8oC,IAA1B;;;UAEEpnC,KAAK,CAAC6B,OAAN,CAAcvD,OAAO,CAACqd,IAAtB,CAAJ,EAAiC;QAC/B5T,UAAU,CAAC4O,IAAX,GAAkB,CAACrY,OAAO,CAACqd,IAAR,CAAa,CAAb,CAAD,EAAkB,KAAKpF,IAAL,CAAU7O,MAAV,GAAmBpJ,OAAO,CAACqd,IAAR,CAAa,CAAb,CAArC,EAChBrd,OAAO,CAACqd,IAAR,CAAa,CAAb,CADgB,EACC,KAAKpF,IAAL,CAAU7O,MAAV,GAAmBpJ,OAAO,CAACqd,IAAR,CAAa,CAAb,CADpB,CAAlB;;;UAGE3b,KAAK,CAAC6B,OAAN,CAAcvD,OAAO,CAACorC,QAAtB,KACFprC,OAAO,CAACorC,QAAR,CAAiBhe,KAAjB,CAAuB/sB,GAAG,IAAI,OAAOA,GAAP,KAAe,QAA7C,CADF,EAC0D;QACxDoJ,UAAU,CAAC4hC,QAAX,GAAsBrrC,OAAO,CAACorC,QAA9B;;;;QAGAlU,GAAG,KAAK,MAAZ,EAAoB;UACdl3B,OAAO,CAACmpC,IAAZ,EAAkB;QAChB1/B,UAAU,CAAC2/B,IAAX,GAAkB,IAAI/mC,MAAJ,CAAWrC,OAAO,CAACmpC,IAAnB,CAAlB;;;UAEEnpC,OAAO,CAACqpC,GAAZ,EAAiB;QACf5/B,UAAU,CAAC6/B,GAAX,GAAiB,IAAIjnC,MAAJ,CAAWrC,OAAO,CAACqpC,GAAnB,CAAjB;;;UAEErpC,OAAO,CAACynC,QAAZ,EAAsB;QACpBh+B,UAAU,CAAC8/B,CAAX,GAAe,IAAIlnC,MAAJ,CAAWrC,OAAO,CAACynC,QAAnB,CAAf;;;UAEEznC,OAAO,CAACwpC,MAAZ,EAAoB;QAClB//B,UAAU,CAACggC,UAAX,GAAwB,IAAIpnC,MAAJ,CAAWrC,OAAO,CAACwpC,MAAnB,CAAxB;;;;SAIC3vB,UAAL,YAAoBqd,GAApB,cAA2B/1B,SAAS,CAACC,OAAV,CAAkBqI,UAAlB,CAA3B;WACO,IAAP;GAlEW;;EAqEb40B,oBAAoB,CAACnH,GAAD,EAAoB;QAAdl3B,OAAc,uEAAJ,EAAI;QAChC+pC,iBAAiB,GAAG,KAAKC,mBAAL,GAA2B1pC,GAA3B,CAA+B,KAAK2X,IAAL,CAAUnN,mBAAzC,CAA1B;QACM49B,IAAI,GAAGqB,iBAAiB,CAAChpC,MAA/B;IACAgpC,iBAAiB,CAAC7oC,IAAlB,CAAuB,IAAvB;SAEK+pC,WAAL,CAAiB/T,GAAjB,oCAA2Bl3B,OAA3B;MAAoC0oC;;QAE9BE,aAAa,GAAG,IAAIJ,mBAAJ,CAAwB,KAAKvwB,IAAL,CAAUxO,UAAlC,EAA8Ci/B,IAA9C,CAAtB;SACKzwB,IAAL,CAAUhO,QAAV,CAAmBtI,KAAnB,CAAyB,CAAC,CAA1B,EAA6B,CAA7B,EAAgCinC,aAAhC,GAAgDA,aAAhD;WACOA,aAAP;GA9EW;;EAiFbwB,gBAAgB,GAAG;SACZnyB,IAAL,CAAUhO,QAAV,CAAmBmiB,GAAnB;SACKvS,UAAL,CAAgB,KAAhB;WACO,IAAP;GApFW;;EAuFbskB,MAAM,CAAC2K,IAAD,EAAsC;QAA/B9oC,OAA+B,uEAArB,EAAqB;QAAjB2nC,QAAiB,uEAAN,IAAM;WACnC,IAAIkB,mBAAJ,CAAwB,IAAxB,EAA8BC,IAA9B,EAAoC9oC,OAApC,EAA6C2nC,QAA7C,CAAP;GAxFW;;EA2Fb1J,YAAY,CAACqN,UAAD,EAAa;QACjBC,cAAc,GAAG,KAAKP,iBAAL,EAAvB;IACAM,UAAU,CAAC3B,SAAX,CAAqB4B,cAArB;IACAD,UAAU,CAAC1B,WAAX;SACKgB,cAAL,CAAoB1pC,IAApB,CAAyBoqC,UAAzB;;QACI,CAACC,cAAc,CAACpnC,IAAf,CAAoBmmC,CAAzB,EAA4B;MAC1BiB,cAAc,CAACpnC,IAAf,CAAoBmmC,CAApB,GAAwB,EAAxB;;;IAEFiB,cAAc,CAACpnC,IAAf,CAAoBmmC,CAApB,CAAsBppC,IAAtB,CAA2BoqC,UAAU,CAAC7hC,UAAtC;WACO,IAAP;GApGW;;EAuGb+hC,gBAAgB,CAACC,YAAD,EAAe;IAC7BA,YAAY,CAACtwB,OAAb,CAAsBgwB,OAAD,IAAa;UAC5BA,OAAO,CAACvC,aAAZ,EAA2B;YACnBA,aAAa,GAAGuC,OAAO,CAACvC,aAA9B;YACM8C,gBAAgB,GAAG,KAAKrN,oBAAL,CAA0B8M,OAAO,CAACjU,GAAlC,EAAuCiU,OAAO,CAACnrC,OAA/C,CAAzB;QACA4oC,aAAa,CAAC1nC,IAAd,CAAmBwqC,gBAAnB;aACKzzB,IAAL,CAAUhO,QAAV,CAAmBtI,KAAnB,CAAyB,CAAC,CAA1B,EAA6B,CAA7B,EAAgCinC,aAAhC,GAAgDA,aAAhD;OAJF,MAKO;aACAqC,WAAL,CAAiBE,OAAO,CAACjU,GAAzB,EAA8BiU,OAAO,CAACnrC,OAAtC;;KAPJ;GAxGW;;EAoHb2rC,eAAe,CAAC1zB,IAAD,EAAO;QACdwzB,YAAY,GAAGxzB,IAAI,CAAChO,QAA1B;IACAwhC,YAAY,CAACtwB,OAAb,CAAqB,MAAMlD,IAAI,CAACxT,KAAL,CAAW,KAAX,CAA3B;IACAwT,IAAI,CAAChO,QAAL,GAAgB,EAAhB;WACOwhC,YAAP;GAxHW;;EA2HbX,qBAAqB,GAAG;QAClB,CAAC,KAAKlhC,KAAL,CAAWzF,IAAX,CAAgBynC,QAArB,EAA+B;WACxBhiC,KAAL,CAAWzF,IAAX,CAAgBynC,QAAhB,GAA2B,KAAKtiC,GAAL,CAAS,EAAT,CAA3B;;;WAEK,KAAKM,KAAL,CAAWzF,IAAX,CAAgBynC,QAAvB;GA/HW;;EAkIbZ,iBAAiB,GAAG;QACd,CAAC,KAAKphC,KAAL,CAAWzF,IAAX,CAAgB0nC,cAArB,EAAqC;WAC9BjiC,KAAL,CAAWzF,IAAX,CAAgB0nC,cAAhB,GAAiC,KAAKviC,GAAL,CAAS;QACxCI,IAAI,EAAE,gBADkC;QAExCoiC,UAAU,EAAE,IAAIpB,aAAJ,EAF4B;QAGxCqB,iBAAiB,EAAE;OAHY,CAAjC;;;WAMK,KAAKniC,KAAL,CAAWzF,IAAX,CAAgB0nC,cAAvB;GA1IW;;EA6Ib7B,mBAAmB,GAAG;WACb,KAAKgB,iBAAL,GAAyB7mC,IAAzB,CAA8B2nC,UAArC;GA9IW;;EAiJb9gC,6BAA6B,GAAG;;SAEzB8/B,qBAAL;QAEMS,cAAc,GAAG,KAAKP,iBAAL,EAAvB;QACM5qC,GAAG,GAAGmrC,cAAc,CAACpnC,IAAf,CAAoB4nC,iBAApB,EAAZ;IACAR,cAAc,CAACpnC,IAAf,CAAoB2nC,UAApB,CAA+B3rC,GAA/B,CAAmCC,GAAnC,EAAwC,EAAxC;WACOA,GAAP;GAxJW;;EA2Jb4rC,WAAW,GAAG;QACNT,cAAc,GAAG,KAAK3hC,KAAL,CAAWzF,IAAX,CAAgB0nC,cAAvC;;QACIN,cAAJ,EAAoB;MAClBA,cAAc,CAACrpC,GAAf;WACK0oC,cAAL,CAAoBzvB,OAApB,CAA6BmwB,UAAD,IAAgBA,UAAU,CAACppC,GAAX,EAA5C;;;QAEE,KAAK0H,KAAL,CAAWzF,IAAX,CAAgBynC,QAApB,EAA8B;WACvBhiC,KAAL,CAAWzF,IAAX,CAAgBynC,QAAhB,CAAyB1pC,GAAzB;;;;CAlKN;;ACVA,IAAM+pC,WAAW,GAAG;EAClBC,QAAQ,EAAE,CADQ;EAElBvP,QAAQ,EAAE,CAFQ;EAGlBwP,QAAQ,EAAE,CAHQ;EAIlBC,SAAS,EAAE,MAJO;EAKlBr3B,QAAQ,EAAE,MALQ;EAMlBs3B,iBAAiB,EAAE,MAND;EAOlBC,WAAW,EAAE,MAPK;EAQlBC,UAAU,EAAE,OARM;EASlBC,KAAK,EAAE,OATW;EAUlBC,IAAI,EAAE,OAVY;EAWlB/rC,IAAI,EAAE,OAXY;EAYlBgsC,WAAW,EAAE,QAZK;EAalBC,OAAO,EAAE;CAbX;AAeA,IAAMC,aAAa,GAAG;EACpBpnC,IAAI,EAAE,CADc;EAEpBqnC,MAAM,EAAE,CAFY;EAGpBnnC,KAAK,EAAE;CAHT;AAKA,IAAMonC,SAAS,GAAG;EAAExhC,KAAK,EAAE,GAAT;EAAcyhC,YAAY,EAAE;CAA9C;AACA,IAAMC,cAAc,GAAG;EACrBC,GAAG,EAAE,GADgB;EAErBC,QAAQ,EAAE,GAFW;EAGrBC,IAAI,EAAE,GAHe;EAIrBC,KAAK,EAAE,GAJc;EAKrBC,GAAG,EAAE;CALP;AAOA,IAAMC,cAAc,GAAG;EACrB1pC,MAAM,EAAE;IACN2pC,IAAI,EAAE,CADA;IAENC,QAAQ,EAAE,KAFJ;IAGNC,QAAQ,EAAE,YAHJ;IAINC,QAAQ,EAAE,EAJJ;IAKNC,eAAe,EAAE;GANE;EAQrBC,OAAO,EAAE;IACPL,IAAI,EAAE,CADC;IAEPC,QAAQ,EAAE;;CAVd;AAcA,oBAAe;;;;;EAKbK,QAAQ,GAAG;QACL,CAAC,KAAK1T,KAAV,EAAiB;YACT,IAAIt6B,KAAJ,CAAU,gDAAV,CAAN;;;SAEGiuC,SAAL,GAAiB;MACf5jC,KAAK,EAAE,EADQ;MAEf6vB,WAAW,EAAE,KAAKI,KAAL,CAAWnd;KAF1B;SAIK8wB,SAAL,CAAe5jC,KAAf,CAAqB,KAAKiwB,KAAL,CAAWj2B,EAAhC,IAAsC,KAAKi2B,KAAL,CAAW7wB,GAAX,EAAtC;QAEInF,IAAI,GAAG;MACT4pC,MAAM,EAAE,EADC;MAETC,eAAe,EAAE,IAFR;MAGTlH,EAAE,EAAE,IAAIzkC,MAAJ,YAAe,KAAK83B,KAAL,CAAWj2B,EAA1B,eAHK;MAIT+pC,EAAE,EAAE;QACF9jC,IAAI,EAAE;;KALV;IAQAhG,IAAI,CAAC8pC,EAAL,CAAQ9jC,IAAR,CAAa,KAAKgwB,KAAL,CAAWj2B,EAAxB,IAA8B,KAAKi2B,KAAL,CAAW7wB,GAAX,EAA9B;QACM4kC,QAAQ,GAAG,KAAK5kC,GAAL,CAASnF,IAAT,CAAjB;SACKyF,KAAL,CAAWzF,IAAX,CAAgB+pC,QAAhB,GAA2BA,QAA3B;WACO,IAAP;GA1BW;;;;;EAgCbC,WAAW,GAAG;QACR,KAAKvkC,KAAL,CAAWzF,IAAX,CAAgB+pC,QAApB,EAA8B;UAE1B,CAAC1tC,MAAM,CAACC,IAAP,CAAY,KAAKqtC,SAAL,CAAe5jC,KAA3B,EAAkCnJ,MAAnC,IACA,CAAC,KAAK+sC,SAAL,CAAe/T,WAFlB,EAGE;cACM,IAAIl6B,KAAJ,CAAU,iCAAV,CAAN;;;UAEEuuC,QAAQ,GAAG,KAAKxkC,KAAL,CAAWzF,IAAX,CAAgB+pC,QAAhB,CAAyB/pC,IAAzB,CAA8B8pC,EAA9B,CAAiC9jC,IAAhD;MACA3J,MAAM,CAACC,IAAP,CAAY,KAAKqtC,SAAL,CAAe5jC,KAA3B,EAAkCiR,OAAlC,CAA0C6B,IAAI,IAAI;QAChDoxB,QAAQ,CAACpxB,IAAD,CAAR,GAAiB,KAAK8wB,SAAL,CAAe5jC,KAAf,CAAqB8S,IAArB,CAAjB;OADF;;WAGKpT,KAAL,CAAWzF,IAAX,CAAgB+pC,QAAhB,CAAyB/pC,IAAzB,CAA8B4pC,MAA9B,CAAqC5yB,OAArC,CAA6CkzB,QAAQ,IAAI;aAClDC,SAAL,CAAeD,QAAf;OADF;;WAGKzkC,KAAL,CAAWzF,IAAX,CAAgB+pC,QAAhB,CAAyBhsC,GAAzB;;;WAEK,IAAP;GAjDW;;EAoDbosC,SAAS,CAAChlC,GAAD,EAAM;QACT5H,KAAK,CAAC6B,OAAN,CAAc+F,GAAG,CAACnF,IAAJ,CAASkiC,IAAvB,CAAJ,EAAkC;MAChC/8B,GAAG,CAACnF,IAAJ,CAASkiC,IAAT,CAAclrB,OAAd,CAAsBozB,QAAQ,IAAI;aAC3BD,SAAL,CAAeC,QAAf;OADF;MAGAjlC,GAAG,CAACpH,GAAJ;;;WAEK,IAAP;GA3DW;;;;;;;;;EAqEbssC,SAAS,CAACxxB,IAAD,EAAqB;QAAdhd,OAAc,uEAAJ,EAAI;;QACxByuC,SAAS,GAAG,KAAKC,UAAL,CAAgB1xB,IAAhB,EAAsB,IAAtB,EAA4Bhd,OAA5B,CAAhB;;QACIquC,QAAQ,GAAG,KAAK/kC,GAAL,CAASmlC,SAAT,CAAf;;SACKE,YAAL,CAAkBN,QAAlB;;WACOA,QAAP;GAzEW;;;;;;;;;;;;;EAuFbO,cAAc,CAAC5xB,IAAD,EAAO8rB,IAAP,EAAa97B,CAAb,EAAgB8b,CAAhB,EAAmBwD,CAAnB,EAAsBnF,CAAtB,EAAuC;QAAdnnB,OAAc,uEAAJ,EAAI;;QAC/CyuC,SAAS,GAAG,KAAKC,UAAL,CAAgB1xB,IAAhB,EAAsB8rB,IAAtB,EAA4B9oC,OAA5B,CAAhB;;IACAyuC,SAAS,CAACt2B,OAAV,GAAoB,QAApB;;QACIs2B,SAAS,CAAC3I,CAAV,KAAgBnP,SAApB,EAA+B;MAC7B8X,SAAS,CAAC3I,CAAV,GAAc,CAAd,CAD6B;KAHoB;;;SAQ9CJ,QAAL,CAAc14B,CAAd,EAAiB8b,CAAjB,EAAoBwD,CAApB,EAAuBnF,CAAvB,EAA0BsnB,SAA1B;QACII,QAAQ,GAAG,KAAK52B,IAAL,CAAUrN,WAAV,CAAsB,KAAKqN,IAAL,CAAUrN,WAAV,CAAsB7J,MAAtB,GAA+B,CAArD,CAAf;WAEO,KAAK4tC,YAAL,CAAkBE,QAAlB,CAAP;GAlGW;;EAqGbC,QAAQ,CAAC9xB,IAAD,EAAOhQ,CAAP,EAAU8b,CAAV,EAAawD,CAAb,EAAgBnF,CAAhB,EAAiC;QAAdnnB,OAAc,uEAAJ,EAAI;WAChC,KAAK4uC,cAAL,CAAoB5xB,IAApB,EAA0B,MAA1B,EAAkChQ,CAAlC,EAAqC8b,CAArC,EAAwCwD,CAAxC,EAA2CnF,CAA3C,EAA8CnnB,OAA9C,CAAP;GAtGW;;EAyGb+uC,cAAc,CAAC/xB,IAAD,EAAOhQ,CAAP,EAAU8b,CAAV,EAAawD,CAAb,EAAgBnF,CAAhB,EAAiC;QAAdnnB,OAAc,uEAAJ,EAAI;WACtC,KAAK4uC,cAAL,CAAoB5xB,IAApB,EAA0B,YAA1B,EAAwChQ,CAAxC,EAA2C8b,CAA3C,EAA8CwD,CAA9C,EAAiDnF,CAAjD,EAAoDnnB,OAApD,CAAP;GA1GW;;EA6GbgvC,SAAS,CAAChyB,IAAD,EAAOhQ,CAAP,EAAU8b,CAAV,EAAawD,CAAb,EAAgBnF,CAAhB,EAAiC;QAAdnnB,OAAc,uEAAJ,EAAI;WACjC,KAAK4uC,cAAL,CAAoB5xB,IAApB,EAA0B,OAA1B,EAAmChQ,CAAnC,EAAsC8b,CAAtC,EAAyCwD,CAAzC,EAA4CnF,CAA5C,EAA+CnnB,OAA/C,CAAP;GA9GW;;EAiHbivC,QAAQ,CAACjyB,IAAD,EAAOhQ,CAAP,EAAU8b,CAAV,EAAawD,CAAb,EAAgBnF,CAAhB,EAAiC;QAAdnnB,OAAc,uEAAJ,EAAI;WAChC,KAAK4uC,cAAL,CAAoB5xB,IAApB,EAA0B,MAA1B,EAAkChQ,CAAlC,EAAqC8b,CAArC,EAAwCwD,CAAxC,EAA2CnF,CAA3C,EAA8CnnB,OAA9C,CAAP;GAlHW;;EAqHbkvC,eAAe,CAAClyB,IAAD,EAAOhQ,CAAP,EAAU8b,CAAV,EAAawD,CAAb,EAAgBnF,CAAhB,EAAiC;QAAdnnB,OAAc,uEAAJ,EAAI;WACvC,KAAK4uC,cAAL,CAAoB5xB,IAApB,EAA0B,aAA1B,EAAyChQ,CAAzC,EAA4C8b,CAA5C,EAA+CwD,CAA/C,EAAkDnF,CAAlD,EAAqDnnB,OAArD,CAAP;GAtHW;;EAyHbmvC,YAAY,CAACnyB,IAAD,EAAOhQ,CAAP,EAAU8b,CAAV,EAAawD,CAAb,EAAgBnF,CAAhB,EAAiC;QAAdnnB,OAAc,uEAAJ,EAAI;WACpC,KAAK4uC,cAAL,CAAoB5xB,IAApB,EAA0B,UAA1B,EAAsChQ,CAAtC,EAAyC8b,CAAzC,EAA4CwD,CAA5C,EAA+CnF,CAA/C,EAAkDnnB,OAAlD,CAAP;GA1HW;;EA6Hb2uC,YAAY,CAACN,QAAD,EAAW;QACjB/G,MAAM,GAAG+G,QAAQ,CAAClqC,IAAT,CAAcwF,MAA3B;;QACI29B,MAAJ,EAAY;UACN,CAACA,MAAM,CAACnjC,IAAP,CAAYkiC,IAAjB,EAAuB;QACrBiB,MAAM,CAACnjC,IAAP,CAAYkiC,IAAZ,GAAmB,EAAnB;;;MAEFiB,MAAM,CAACnjC,IAAP,CAAYkiC,IAAZ,CAAiBnlC,IAAjB,CAAsBmtC,QAAtB;KAJF,MAKO;WACAzkC,KAAL,CAAWzF,IAAX,CAAgB+pC,QAAhB,CAAyB/pC,IAAzB,CAA8B4pC,MAA9B,CAAqC7sC,IAArC,CAA0CmtC,QAA1C;;;WAEK,IAAP;GAvIW;;EA0IbK,UAAU,CAAC1xB,IAAD,EAAO8rB,IAAP,EAA2B;QAAd9oC,OAAc,uEAAJ,EAAI;;QAC/B,CAAC,KAAK8tC,SAAV,EAAqB;YACb,IAAIjuC,KAAJ,CACJ,0EADI,CAAN;;;QAIE0N,IAAI,GAAG/M,MAAM,CAAC2/B,MAAP,CAAc,EAAd,EAAkBngC,OAAlB,CAAX;;QACI8oC,IAAI,KAAK,IAAb,EAAmB;MACjBv7B,IAAI,GAAG,KAAK6hC,YAAL,CAAkBtG,IAAlB,EAAwB9oC,OAAxB,CAAP;;;IAEFuN,IAAI,GAAG,KAAK8hC,aAAL,CAAmB9hC,IAAnB,CAAP;IACAA,IAAI,GAAG,KAAK+hC,eAAL,CAAqB/hC,IAArB,CAAP;IACAA,IAAI,GAAG,KAAKgiC,YAAL,CAAkBhiC,IAAlB,CAAP;IACAA,IAAI,GAAG,KAAKiiC,eAAL,CAAqBjiC,IAArB,CAAP;IACAA,IAAI,GAAG,KAAKkiC,cAAL,CAAoBliC,IAApB,CAAP;IACAA,IAAI,GAAG,KAAKmiC,cAAL,CAAoBniC,IAApB,CAAP;IACAA,IAAI,CAACka,CAAL,GAAS,IAAIplB,MAAJ,CAAW2a,IAAX,CAAT;;QACIzP,IAAI,CAAC+5B,MAAT,EAAiB;MACf/5B,IAAI,CAAC5D,MAAL,GAAc4D,IAAI,CAAC+5B,MAAnB;aACO/5B,IAAI,CAAC+5B,MAAZ;;;WAEK/5B,IAAP;GA/JW;;EAkKb6hC,YAAY,CAACtG,IAAD,EAAOv7B,IAAP,EAAa;QACnBu7B,IAAI,KAAK,MAAb,EAAqB;MACnBv7B,IAAI,CAACoiC,EAAL,GAAU,IAAV;KADF,MAEO,IAAI7G,IAAI,KAAK,YAAb,EAA2B;MAChCv7B,IAAI,CAACoiC,EAAL,GAAU,KAAV;MACApiC,IAAI,CAACg/B,UAAL,GAAkB,IAAlB;KAFK,MAGA,IAAIzD,IAAI,KAAK,aAAb,EAA4B;MACjCv7B,IAAI,CAACoiC,EAAL,GAAU,KAAV;MACApiC,IAAI,CAAC++B,WAAL,GAAmB,IAAnB;KAFK,MAGA,IAAIxD,IAAI,KAAK,UAAb,EAAyB;MAC9Bv7B,IAAI,CAACoiC,EAAL,GAAU,KAAV;KADK,MAEA,IAAI7G,IAAI,KAAK,OAAb,EAAsB;MAC3Bv7B,IAAI,CAACoiC,EAAL,GAAU,IAAV;MACApiC,IAAI,CAACi/B,KAAL,GAAa,IAAb;KAFK,MAGA,IAAI1D,IAAI,KAAK,MAAb,EAAqB;MAC1Bv7B,IAAI,CAACoiC,EAAL,GAAU,IAAV;KADK,MAEA;YACC,IAAI9vC,KAAJ,yCAA2CipC,IAA3C,OAAN;;;WAEKv7B,IAAP;GArLW;;EAwLbmiC,cAAc,CAACniC,IAAD,EAAO;QACbqiC,CAAC,GAAGriC,IAAI,CAACsiC,MAAf;;QACID,CAAC,IAAIA,CAAC,CAAC9G,IAAX,EAAiB;UACXgH,WAAJ;UACIC,QAAJ;UACI3nB,MAAM,GAAG,EAAb;;UACI4kB,cAAc,CAAC4C,CAAC,CAAC9G,IAAH,CAAd,KAA2BnS,SAA/B,EAA0C;QACxCmZ,WAAW,wBAAX;QACAC,QAAQ,qBAAR;QACA3nB,MAAM,GAAG4kB,cAAc,CAAC4C,CAAC,CAAC9G,IAAH,CAAvB;OAHF,MAIO;YACD+G,MAAM,GAAGD,CAAC,CAAC9G,IAAF,CAAO7sB,MAAP,CAAc,CAAd,EAAiB/S,WAAjB,KAAiC0mC,CAAC,CAAC9G,IAAF,CAAOnnC,KAAP,CAAa,CAAb,CAA9C;QACAmuC,WAAW,eAAQD,MAAR,eAAX;QACAE,QAAQ,eAAQF,MAAR,YAAR;;YAEID,CAAC,CAAC9G,IAAF,KAAW,MAAf,EAAuB;UACrBgH,WAAW,IAAI,IAAf;UACA1nB,MAAM,GAAG/lB,MAAM,CAACutC,CAAC,CAACI,KAAH,CAAf;SAFF,MAGO,IAAIJ,CAAC,CAAC9G,IAAF,KAAW,MAAf,EAAuB;UAC5B1gB,MAAM,GAAG/lB,MAAM,CAACutC,CAAC,CAACI,KAAH,CAAf;SADK,MAEA,IAAIJ,CAAC,CAAC9G,IAAF,KAAW,QAAf,EAAyB;cAC1B7E,CAAC,GAAGzjC,MAAM,CAAC2/B,MAAP,CAAc,EAAd,EAAkBmN,cAAc,CAAC1pC,MAAjC,EAAyCgsC,CAAzC,CAAR;UACAxnB,MAAM,GAAG/lB,MAAM,CACb,CACEA,MAAM,CAAC4hC,CAAC,CAACsJ,IAAH,CADR,EAEEtJ,CAAC,CAACuJ,QAAF,GAAa,GAAb,GAAmB,GAFrB,EAGE,MAAMvJ,CAAC,CAACwJ,QAAR,GAAmB,GAHrB,EAIE,MAJF,EAKE,MAAMxJ,CAAC,CAACyJ,QAAR,GAAmB,GALrB,EAMErrC,MAAM,CAAC4hC,CAAC,CAAC0J,eAAH,CANR,EAOEpsC,IAPF,CAOO,GAPP,CADa,CAAf;SAFK,MAYA,IAAIquC,CAAC,CAAC9G,IAAF,KAAW,SAAf,EAA0B;cAC3B7E,EAAC,GAAGzjC,MAAM,CAAC2/B,MAAP,CAAc,EAAd,EAAkBmN,cAAc,CAACM,OAAjC,EAA0CgC,CAA1C,CAAR;;UACAxnB,MAAM,GAAG/lB,MAAM,CAAC,CAACA,MAAM,CAAC4hC,EAAC,CAACsJ,IAAH,CAAP,EAAiBtJ,EAAC,CAACuJ,QAAF,GAAa,GAAb,GAAmB,GAApC,EAAyCjsC,IAAzC,CAA8C,GAA9C,CAAD,CAAf;;;;MAGJgM,IAAI,CAAC0iC,EAAL,GAAU1iC,IAAI,CAAC0iC,EAAL,GAAU1iC,IAAI,CAAC0iC,EAAf,GAAoB,EAA9B;MACA1iC,IAAI,CAAC0iC,EAAL,CAAQ3F,CAAR,GAAY;QACV/xB,CAAC,EAAE,YADO;QAEV23B,EAAE,EAAE,IAAI7tC,MAAJ,WAAcytC,WAAd,cAA6B1nB,MAA7B;OAFN;MAIA7a,IAAI,CAAC0iC,EAAL,CAAQnK,CAAR,GAAY;QACVvtB,CAAC,EAAE,YADO;QAEV23B,EAAE,EAAE,IAAI7tC,MAAJ,WAAc0tC,QAAd,cAA0B3nB,MAA1B;OAFN;;;WAKK7a,IAAI,CAACsiC,MAAZ;WACOtiC,IAAP;GAxOW;;EA2ObkiC,cAAc,CAACliC,IAAD,EAAO;QACfwI,KAAK,GAAG,KAAKE,eAAL,CAAqB1I,IAAI,CAAC4iC,eAA1B,CAAZ;;QACIp6B,KAAJ,EAAW;UACL,CAACxI,IAAI,CAAC6iC,EAAV,EAAc;QACZ7iC,IAAI,CAAC6iC,EAAL,GAAU,EAAV;;;MAEF7iC,IAAI,CAAC6iC,EAAL,CAAQC,EAAR,GAAat6B,KAAb;;;IAEFA,KAAK,GAAG,KAAKE,eAAL,CAAqB1I,IAAI,CAAC+iC,WAA1B,CAAR;;QACIv6B,KAAJ,EAAW;UACL,CAACxI,IAAI,CAAC6iC,EAAV,EAAc;QACZ7iC,IAAI,CAAC6iC,EAAL,GAAU,EAAV;;;MAEF7iC,IAAI,CAAC6iC,EAAL,CAAQG,EAAR,GAAax6B,KAAb;;;WAEKxI,IAAI,CAAC4iC,eAAZ;WACO5iC,IAAI,CAAC+iC,WAAZ;WACO/iC,IAAP;GA5PW;;EA+Pb8hC,aAAa,CAACrvC,OAAD,EAAU;QACjBwrB,MAAM,GAAG,CAAb;IACAhrB,MAAM,CAACC,IAAP,CAAYT,OAAZ,EAAqBmb,OAArB,CAA6B/a,GAAG,IAAI;UAC9B6rC,WAAW,CAAC7rC,GAAD,CAAf,EAAsB;YAChBJ,OAAO,CAACI,GAAD,CAAX,EAAkB;UAChBorB,MAAM,IAAIygB,WAAW,CAAC7rC,GAAD,CAArB;;;eAEKJ,OAAO,CAACI,GAAD,CAAd;;KALJ;;QAQIorB,MAAM,KAAK,CAAf,EAAkB;MAChBxrB,OAAO,CAACwwC,EAAR,GAAaxwC,OAAO,CAACwwC,EAAR,GAAaxwC,OAAO,CAACwwC,EAArB,GAA0B,CAAvC;MACAxwC,OAAO,CAACwwC,EAAR,IAAchlB,MAAd;;;WAEKxrB,OAAP;GA7QW;;EAgRbsvC,eAAe,CAACtvC,OAAD,EAAU;QACnBwrB,MAAM,GAAG,CAAb;;QACIxrB,OAAO,CAAC07B,KAAR,KAAkB/E,SAAtB,EAAiC;UAC3B,OAAOiW,aAAa,CAAC5sC,OAAO,CAAC07B,KAAT,CAApB,KAAwC,QAA5C,EAAsD;QACpDlQ,MAAM,GAAGohB,aAAa,CAAC5sC,OAAO,CAAC07B,KAAT,CAAtB;;;aAEK17B,OAAO,CAAC07B,KAAf;;;QAEElQ,MAAM,KAAK,CAAf,EAAkB;MAChBxrB,OAAO,CAACsnB,CAAR,GAAYkE,MAAZ,CADgB;;;WAGXxrB,OAAP;GA3RW;;EA8RbuvC,YAAY,CAACvvC,OAAD,EAAU;;QAEhB,KAAK8tC,SAAL,CAAe5jC,KAAf,CAAqB,KAAKiwB,KAAL,CAAWj2B,EAAhC,MAAwC,IAA5C,EAAkD;WAC3C4pC,SAAL,CAAe5jC,KAAf,CAAqB,KAAKiwB,KAAL,CAAWj2B,EAAhC,IAAsC,KAAKi2B,KAAL,CAAW7wB,GAAX,EAAtC;KAHkB;;;QAOhB,KAAKwkC,SAAL,CAAe/T,WAAf,KAA+B,KAAKI,KAAL,CAAWnd,IAA9C,EAAoD;MAClDhd,OAAO,CAACiuC,EAAR,GAAa;QAAE9jC,IAAI,EAAE;OAArB,CADkD;;UAI5CmwB,QAAQ,GAAGt6B,OAAO,CAACs6B,QAAR,IAAoB,CAArC;MAEAt6B,OAAO,CAACiuC,EAAR,CAAW9jC,IAAX,CAAgB,KAAKgwB,KAAL,CAAWj2B,EAA3B,IAAiC,KAAKi2B,KAAL,CAAW7wB,GAAX,EAAjC;MACAtJ,OAAO,CAAC8mC,EAAR,GAAa,IAAIzkC,MAAJ,YAAe,KAAK83B,KAAL,CAAWj2B,EAA1B,cAAgCo2B,QAAhC,aAAb;;;WAEKt6B,OAAP;GA9SW;;EAiTbwvC,eAAe,CAACxvC,OAAD,EAAU;QACnBywC,MAAM,GAAG,EAAb;;aACSC,aAAT,CAAuB/vC,CAAvB,EAA0B;UACpBe,KAAK,CAAC6B,OAAN,CAAc5C,CAAd,CAAJ,EAAsB;aACf,IAAIgwC,GAAG,GAAG,CAAf,EAAkBA,GAAG,GAAGhwC,CAAC,CAACI,MAA1B,EAAkC4vC,GAAG,EAArC,EAAyC;cACnC,OAAOhwC,CAAC,CAACgwC,GAAD,CAAR,KAAkB,QAAtB,EAAgC;YAC9BF,MAAM,CAACvvC,IAAP,CAAY,IAAImB,MAAJ,CAAW1B,CAAC,CAACgwC,GAAD,CAAZ,CAAZ;WADF,MAEO;YACLF,MAAM,CAACvvC,IAAP,CAAYP,CAAC,CAACgwC,GAAD,CAAb;;;;;;IAKRD,aAAa,CAAC1wC,OAAO,CAAC4wC,GAAT,CAAb;;QACI5wC,OAAO,CAACywC,MAAZ,EAAoB;MAClBC,aAAa,CAAC1wC,OAAO,CAACywC,MAAT,CAAb;aACOzwC,OAAO,CAACywC,MAAf;;;QAEEA,MAAM,CAAC1vC,MAAX,EAAmB;MACjBf,OAAO,CAAC4wC,GAAR,GAAcH,MAAd;;;IAGFjwC,MAAM,CAACC,IAAP,CAAYqsC,SAAZ,EAAuB3xB,OAAvB,CAA+B/a,GAAG,IAAI;UAChCJ,OAAO,CAACI,GAAD,CAAP,KAAiBu2B,SAArB,EAAgC;QAC9B32B,OAAO,CAAC8sC,SAAS,CAAC1sC,GAAD,CAAV,CAAP,GAA0BJ,OAAO,CAACI,GAAD,CAAjC;eACOJ,OAAO,CAACI,GAAD,CAAd;;KAHJ;KAMC,GAAD,EAAM,IAAN,EAAY+a,OAAZ,CAAoB/a,GAAG,IAAI;UACrB,OAAOJ,OAAO,CAACI,GAAD,CAAd,KAAwB,QAA5B,EAAsC;QACpCJ,OAAO,CAACI,GAAD,CAAP,GAAe,IAAIiC,MAAJ,CAAWrC,OAAO,CAACI,GAAD,CAAlB,CAAf;;KAFJ;;QAMIJ,OAAO,CAACowC,EAAR,IAAcpwC,OAAO,CAACowC,EAAR,CAAWlzB,EAA7B,EAAiC;MAC/Bld,OAAO,CAACowC,EAAR,CAAWlzB,EAAX,GAAgB,IAAI7a,MAAJ,CAAWrC,OAAO,CAACowC,EAAR,CAAWlzB,EAAtB,CAAhB;;;QAEEld,OAAO,CAACw/B,KAAZ,EAAmB;MACjBx/B,OAAO,CAACowC,EAAR,GAAapwC,OAAO,CAACowC,EAAR,GAAapwC,OAAO,CAACowC,EAArB,GAA0B,EAAvC;MACApwC,OAAO,CAACowC,EAAR,CAAWlzB,EAAX,GAAgB,IAAI7a,MAAJ,CAAWrC,OAAO,CAACw/B,KAAnB,CAAhB;aACOx/B,OAAO,CAACw/B,KAAf;;;WAEKx/B,OAAP;;;CA3VJ;;ACvCA,uBAAe;;;;;;;;;;;;;EAabgnC,IAAI,CAACvN,GAAD,EAAoB;QAAdz5B,OAAc,uEAAJ,EAAI;IACtBA,OAAO,CAACgd,IAAR,GAAehd,OAAO,CAACgd,IAAR,IAAgByc,GAA/B;QAEMoX,OAAO,GAAG;MACdnnC,IAAI,EAAE,cADQ;MAEdonC,MAAM,EAAE;KAFV;QAII3sC,IAAJ;;QAEI,CAACs1B,GAAL,EAAU;YACF,IAAI55B,KAAJ,CAAU,kBAAV,CAAN;;;QAEE6C,MAAM,CAACK,QAAP,CAAgB02B,GAAhB,CAAJ,EAA0B;MACxBt1B,IAAI,GAAGs1B,GAAP;KADF,MAEO,IAAIA,GAAG,YAAYI,WAAnB,EAAgC;MACrC11B,IAAI,GAAGzB,MAAM,CAACC,IAAP,CAAY,IAAIi3B,UAAJ,CAAeH,GAAf,CAAZ,CAAP;KADK,MAEA;UACDpH,KAAJ;;UACKA,KAAK,GAAG,2BAA2BkS,IAA3B,CAAgC9K,GAAhC,CAAb,EAAoD;YAC9CpH,KAAK,CAAC,CAAD,CAAT,EAAc;UACZwe,OAAO,CAAC14B,OAAR,GAAkBka,KAAK,CAAC,CAAD,CAAL,CAASxvB,OAAT,CAAiB,GAAjB,EAAsB,KAAtB,CAAlB;;;QAEFsB,IAAI,GAAGzB,MAAM,CAACC,IAAP,CAAY0vB,KAAK,CAAC,CAAD,CAAjB,EAAsB,QAAtB,CAAP;OAJF,MAKO;QACLluB,IAAI,GAAGktB,EAAE,CAACC,YAAH,CAAgBmI,GAAhB,CAAP;;YACI,CAACt1B,IAAL,EAAW;gBACH,IAAItE,KAAJ,uDAAyD45B,GAAzD,EAAN;SAHG;;;YAOC;UAAEsX,SAAF;UAAaC;YAAU3f,EAAE,CAAC4f,QAAH,CAAYxX,GAAZ,CAA7B;QACAoX,OAAO,CAACC,MAAR,CAAeliC,YAAf,GAA8BmiC,SAA9B;QACAF,OAAO,CAACC,MAAR,CAAeI,OAAf,GAAyBF,KAAzB;;KAhCkB;;;QAqClBhxC,OAAO,CAACmxC,YAAR,YAAgCnuC,IAApC,EAA0C;MACxC6tC,OAAO,CAACC,MAAR,CAAeliC,YAAf,GAA8B5O,OAAO,CAACmxC,YAAtC;;;QAEEnxC,OAAO,CAACoxC,YAAR,YAAgCpuC,IAApC,EAA0C;MACxC6tC,OAAO,CAACC,MAAR,CAAeI,OAAf,GAAyBlxC,OAAO,CAACoxC,YAAjC;KAzCoB;;;QA4ClBpxC,OAAO,CAAC8oC,IAAZ,EAAkB;MAChB+H,OAAO,CAAC14B,OAAR,GAAkBnY,OAAO,CAAC8oC,IAAR,CAAajmC,OAAb,CAAqB,GAArB,EAA0B,KAA1B,CAAlB;KA7CoB;;;QAiDhBwuC,QAAQ,GAAGriC,QAAQ,CAACC,GAAT,CACfD,QAAQ,CAACI,GAAT,CAAaC,SAAb,CAAuBE,MAAvB,CAA8B,IAAIqqB,UAAJ,CAAez1B,IAAf,CAA9B,CADe,CAAjB;IAGA0sC,OAAO,CAACC,MAAR,CAAeQ,QAAf,GAA0B,IAAIjvC,MAAJ,CAAWgvC,QAAX,CAA1B;IACAR,OAAO,CAACC,MAAR,CAAeS,IAAf,GAAsBptC,IAAI,CAACqtC,UAA3B,CArDsB;;;QAyDlBloC,GAAJ;QACI,CAAC,KAAKmoC,aAAV,EAAyB,KAAKA,aAAL,GAAqB,EAArB;QACrBzK,IAAI,GAAG,KAAKyK,aAAL,CAAmBzxC,OAAO,CAACgd,IAA3B,CAAX;;QACIgqB,IAAI,IAAI0K,OAAO,CAACb,OAAD,EAAU7J,IAAV,CAAnB,EAAoC;MAClC19B,GAAG,GAAG09B,IAAI,CAAC19B,GAAX;KADF,MAEO;MACLA,GAAG,GAAG,KAAKA,GAAL,CAASunC,OAAT,CAAN;MACAvnC,GAAG,CAACpH,GAAJ,CAAQiC,IAAR;WAEKstC,aAAL,CAAmBzxC,OAAO,CAACgd,IAA3B,sCAAwC6zB,OAAxC;QAAiDvnC;;KAlE7B;;;QAqEhBqoC,YAAY,GAAG;MACnBjoC,IAAI,EAAE,UADa;MAEnBo8B,CAAC,EAAE,IAAIzjC,MAAJ,CAAWrC,OAAO,CAACgd,IAAnB,CAFgB;MAGnB40B,EAAE,EAAE;QAAE9L,CAAC,EAAEx8B;OAHU;MAInBuoC,EAAE,EAAE,IAAIxvC,MAAJ,CAAWrC,OAAO,CAACgd,IAAnB;KAJN;;QAMIhd,OAAO,CAAC8xC,WAAZ,EAAyB;MACvBH,YAAY,CAACvK,IAAb,GAAoB,IAAI/kC,MAAJ,CAAWrC,OAAO,CAAC8xC,WAAnB,CAApB;;;QAEI7K,QAAQ,GAAG,KAAK39B,GAAL,CAASqoC,YAAT,CAAjB;IACA1K,QAAQ,CAAC/kC,GAAT;;QAEI,CAAClC,OAAO,CAACknC,MAAb,EAAqB;WACd6K,oBAAL,CAA0B/xC,OAAO,CAACgd,IAAlC,EAAwCiqB,QAAxC;;;WAGKA,QAAP;;;CAlGJ;;;AAuGA,SAASyK,OAAT,CAAiB/wC,CAAjB,EAAoBC,CAApB,EAAuB;SAEnBD,CAAC,CAACwX,OAAF,KAAcvX,CAAC,CAACuX,OAAhB,IACAxX,CAAC,CAACmwC,MAAF,CAASQ,QAAT,CAAkB1xC,QAAlB,OAAiCgB,CAAC,CAACkwC,MAAF,CAASQ,QAAT,CAAkB1xC,QAAlB,EADjC,IAEAe,CAAC,CAACmwC,MAAF,CAASS,IAAT,KAAkB3wC,CAAC,CAACkwC,MAAF,CAASS,IAF3B,IAGA5wC,CAAC,CAACmwC,MAAF,CAASliC,YAAT,KAA0BhO,CAAC,CAACkwC,MAAF,CAASliC,YAHnC,IAIAjO,CAAC,CAACmwC,MAAF,CAASI,OAAT,KAAqBtwC,CAAC,CAACkwC,MAAF,CAASI,OALhC;;;ACzGF,WAAe;EAEXc,QAAQ,CAACC,OAAD,EAAU;QACVA,OAAO,CAACh2B,MAAR,CAAeg2B,OAAO,CAAClxC,MAAR,GAAiB,CAAhC,MAAuC,GAA3C,EAAgD;WACvCmxC,kBAAL,GAA0BD,OAAO,CAACh2B,MAAR,CAAeg2B,OAAO,CAAClxC,MAAR,GAAiB,CAAhC,EAAmCmI,WAAnC,EAA1B;WACK4rB,MAAL,GAAc3Y,QAAQ,CAAC81B,OAAO,CAACh2B,MAAR,CAAeg2B,OAAO,CAAClxC,MAAR,GAAiB,CAAhC,CAAD,CAAtB;KAFJ,MAGO;;WAEEmxC,kBAAL,GAA0B,GAA1B;WACKpd,MAAL,GAAc3Y,QAAQ,CAAC81B,OAAO,CAACh2B,MAAR,CAAeg2B,OAAO,CAAClxC,MAAR,GAAiB,CAAhC,CAAD,CAAtB;;GATG;;EAaXoxC,SAAS,GAAG;SACHC,gBAAL;;QACMC,MAAM,aAAM3e,SAAN,gCAAZ;QACM4e,QAAQ,aAAM5e,SAAN,6CAAd;;SACK6e,qBAAL,CAA2BlhB,EAAE,CAACmhB,UAAH,CAAcH,MAAd,IAAwBA,MAAxB,GAAiCC,QAA5D;GAjBO;;EAoBXC,qBAAqB,CAACE,QAAD,EAAW;QACtBC,UAAU,GAAGrhB,EAAE,CAACC,YAAH,CAAgBmhB,QAAhB,CAAnB;QAEME,eAAe,GAAG,KAAKrpC,GAAL,CAAS;MAC7B3E,MAAM,EAAE+tC,UAAU,CAAC3xC,MADU;MAE7BqW,CAAC,EAAE;KAFiB,CAAxB;IAIAu7B,eAAe,CAACluC,KAAhB,CAAsBiuC,UAAtB;IACAC,eAAe,CAACzwC,GAAhB;QAEM0wC,SAAS,GAAG,KAAKtpC,GAAL,CAAS;MACvBI,IAAI,EAAE,cADiB;MAEvB6O,CAAC,EAAE,WAFoB;MAGvBs6B,IAAI,EAAE,IAAIxwC,MAAJ,CAAW,mBAAX,CAHiB;MAIvBywC,yBAAyB,EAAE,IAAIzwC,MAAJ,CAAW,mBAAX,CAJJ;MAKvB0wC,iBAAiB,EAAEJ;KALL,CAAlB;IAOAC,SAAS,CAAC1wC,GAAV;SAEK0H,KAAL,CAAWzF,IAAX,CAAgB6uC,aAAhB,GAAgC,CAACJ,SAAD,CAAhC;GAvCO;;EA0CXK,UAAU,GAAG;2IAGU,KAAKne,MAFxB,6DAG0B,KAAKod,kBAH/B;GA3CO;;EAmDXE,gBAAgB,GAAG;SACVc,SAAL,CAAe,KAAKD,UAAL,EAAf;;;CApDR;;ACDA,YAAe;EAEXE,SAAS,GAAG;SACHre,MAAL,GAAc,CAAd;GAHO;;EAMXqd,SAAS,GAAG;SACHiB,iBAAL;GAPO;;EAUXA,iBAAiB,GAAG;SACXF,SAAL,CAAe,KAAKG,WAAL,EAAf;GAXO;;EAcXA,WAAW,GAAG;8IAGU,KAAKve,MAFzB;;;CAfR;;ACEA,kBAAe;EACXwe,aAAa,CAACxe,MAAD,EAAS;IAClBt0B,MAAM,CAAC2/B,MAAP,CAAc,IAAd,EAAoBrL,MAApB;GAFO;;EAKXye,UAAU,CAACvzC,OAAD,EAAU;YAERA,OAAO,CAAC80B,MAAhB;WACS,SAAL;WACK,UAAL;WACK,UAAL;WACK,SAAL;WACK,UAAL;WACK,UAAL;WACK,SAAL;WACK,UAAL;WACK,UAAL;aACSwe,aAAL,CAAmBE,IAAnB;;aACKxB,QAAL,CAAchyC,OAAO,CAAC80B,MAAtB;;;WAEC,QAAL;aACSwe,aAAL,CAAmBG,KAAnB;;aACKN,SAAL;;;;;CAtBhB;;ACFA,MAAMO,WAAN,CAAkB;EACd3zC,WAAW,GAAG;SACL4zC,SAAL;;;EAOJC,UAAU,GAAG;SACJD,SAAL,GAAiB,KAAKA,SAAL,CAAe1uC,MAAf,mGAAjB;;;EAOJ4uC,MAAM,CAACC,GAAD,EAAoB;QAAdC,OAAc,uEAAN,IAAM;SACjBJ,SAAL,GAAiB,KAAKA,SAAL,CAAe1uC,MAAf,CAAsB6uC,GAAtB,CAAjB;QACIC,OAAJ,EACI,KAAKJ,SAAL,GAAiB,KAAKA,SAAL,CAAe1uC,MAAf,CAAsB,IAAtB,CAAjB;;;EAGR+uC,MAAM,GAAG;WAAS,KAAKL,SAAZ;;;EAEXM,SAAS,GAAG;WAAS,KAAKN,SAAL,CAAe5yC,MAAtB;;;EAEdmB,GAAG,GAAG;SACG0xC,UAAL;;SACKD,SAAL,GAAiB,KAAKA,SAAL,CAAetT,IAAf,EAAjB;;;;;AC5BR,oBAAe;EACX6T,YAAY,GAAG;SACNC,QAAL,GAAgB,IAAIT,WAAJ,EAAhB;GAFO;;EAKXR,SAAS,CAACY,GAAD,EAAoB;QAAdC,OAAc,uEAAN,IAAM;SAAOI,QAAL,CAAcN,MAAd,CAAqBC,GAArB,EAAyBC,OAAzB;GALpB;;EAOXK,QAAQ,GAAG;SACFlB,SAAL,8HAEsB,KAAKxkC,IAAL,CAAUE,YAAV,CAAuBylC,WAAvB,GAAqCpjB,KAArC,CAA2C,GAA3C,EAAgD,CAAhD,IAAmD,GAFzE,6DAGuB,KAAKviB,IAAL,CAAU4lC,OAHjC;;QAQI,KAAK5lC,IAAL,CAAU6lC,KAAV,IAAmB,KAAK7lC,IAAL,CAAU8lC,MAA7B,IAAuC,KAAK9lC,IAAL,CAAU+lC,OAArD,EAA8D;WACrDvB,SAAL;;UAII,KAAKxkC,IAAL,CAAU6lC,KAAd,EAAqB;aACZrB,SAAL,+HAGuC,KAAKxkC,IAAL,CAAU6lC,KAHjD;;;UASA,KAAK7lC,IAAL,CAAU8lC,MAAd,EAAsB;aACbtB,SAAL,0GAGkB,KAAKxkC,IAAL,CAAU8lC,MAH5B;;;UASA,KAAK9lC,IAAL,CAAU+lC,OAAd,EAAuB;aACdvB,SAAL,qIAGuC,KAAKxkC,IAAL,CAAU+lC,OAHjD;;;WASCvB,SAAL;;;SAKCA,SAAL,4HAEoB,KAAKxkC,IAAL,CAAU4lC,OAF9B,sBAEwD,KAFxD;;QAII,KAAK5lC,IAAL,CAAUgmC,QAAd,EAAwB;WACfxB,SAAL,uCACgB,KAAKxkC,IAAL,CAAUgmC,QAD1B,sBACqD,KADrD;;;SAICxB,SAAL;GAjEO;;EAsEXyB,WAAW,GAAG;SACLP,QAAL;;SAEKD,QAAL,CAAcjyC,GAAd;;;;;;QAMI,KAAK0N,OAAL,IAAgB,GAApB,EAAyB;WAChBglC,WAAL,GAAmB,KAAKtrC,GAAL,CAAS;QACxBvI,MAAM,EAAE,KAAKozC,QAAL,CAAcF,SAAd,EADgB;QAExBvqC,IAAI,EAAE,UAFkB;QAGxByO,OAAO,EAAE;OAHM,CAAnB;WAKKy8B,WAAL,CAAiBvwC,QAAjB,GAA4B,KAA5B;WACKuwC,WAAL,CAAiBnwC,KAAjB,CAAuB/B,MAAM,CAACC,IAAP,CAAY,KAAKwxC,QAAL,CAAcH,MAAd,EAAZ,EAAoC,OAApC,CAAvB;WACKY,WAAL,CAAiB1yC,GAAjB;WACK0H,KAAL,CAAWzF,IAAX,CAAgB0wC,QAAhB,GAA2B,KAAKD,WAAhC;;;;CAxFZ;;ACFA;;;;AAKA;AAqBA,MAAME,WAAN,SAA0B/5B,MAAM,CAACg6B,QAAjC,CAA0C;EACxCh1C,WAAW,GAAe;QAAdC,OAAc,uEAAJ,EAAI;UAClBA,OAAN;SACKA,OAAL,GAAeA,OAAf,CAFwB;;YAKhBA,OAAO,CAAC2P,UAAhB;WACO,KAAL;aACOC,OAAL,GAAe,GAAf;;;WAEG,KAAL;aACOA,OAAL,GAAe,GAAf;;;WAEG,KAAL;aACOA,OAAL,GAAe,GAAf;;;WAEG,KAAL;WACK,SAAL;aACOA,OAAL,GAAe,GAAf;;;;aAGKA,OAAL,GAAe,GAAf;;KApBoB;;;SAyBnBvL,QAAL,GACE,KAAKrE,OAAL,CAAaqE,QAAb,IAAyB,IAAzB,GAAgC,KAAKrE,OAAL,CAAaqE,QAA7C,GAAwD,IAD1D;SAGK2wC,WAAL,GAAmB,EAAnB;SACKC,gBAAL,GAAwB,CAAxB,CA7BwB;;SAgCnBC,QAAL,GAAgB,EAAhB;SACKC,QAAL,GAAgB,CAAhB;SACKnM,MAAL,GAAc,KAAd;SACKlkC,OAAL,GAAe,CAAf;QACM+E,KAAK,GAAG,KAAKP,GAAL,CAAS;MACrBI,IAAI,EAAE,OADe;MAErBo+B,KAAK,EAAE,CAFc;MAGrBzB,IAAI,EAAE;KAHM,CAAd;QAMM+O,KAAK,GAAG,KAAK9rC,GAAL,CAAS;MACrB+rC,KAAK,EAAE,IAAInqC,WAAJ;KADK,CAAd;SAIKtB,KAAL,GAAa,KAAKN,GAAL,CAAS;MACpBI,IAAI,EAAE,SADc;MAEpBG,KAFoB;MAGpBurC;KAHW,CAAb;;QAMI,KAAKp1C,OAAL,CAAampC,IAAjB,EAAuB;WAChBv/B,KAAL,CAAWzF,IAAX,CAAgBilC,IAAhB,GAAuB,IAAI/mC,MAAJ,CAAW,KAAKrC,OAAL,CAAampC,IAAxB,CAAvB;KArDsB;;;SAyDnBlxB,IAAL,GAAY,IAAZ,CAzDwB;;SA4DnBi8B,YAAL;SACKp4B,SAAL;SACKkQ,UAAL;SACK8N,SAAL,CAAe95B,OAAO,CAAC+zB,IAAvB;SACKwJ,QAAL;SACKiH,UAAL;SACK4D,WAAL;SACKuC,YAAL,CAAkB3qC,OAAlB;SACKuzC,UAAL,CAAgBvzC,OAAhB,EApEwB;;SAuEnB0O,IAAL,GAAY;MACV4mC,QAAQ,EAAE,QADA;MAEVhB,OAAO,EAAE,QAFC;MAGV1lC,YAAY,EAAE,IAAI5L,IAAJ;KAHhB;;QAMI,KAAKhD,OAAL,CAAa0O,IAAjB,EAAuB;WAChB,IAAItO,GAAT,IAAgB,KAAKJ,OAAL,CAAa0O,IAA7B,EAAmC;YAC3BrO,GAAG,GAAG,KAAKL,OAAL,CAAa0O,IAAb,CAAkBtO,GAAlB,CAAZ;aACKsO,IAAL,CAAUtO,GAAV,IAAiBC,GAAjB;;;;QAIA,KAAKL,OAAL,CAAau1C,YAAjB,EAA+B;WACxB3rC,KAAL,CAAWzF,IAAX,CAAgBqxC,iBAAhB,GAAoC,KAAKlsC,GAAL,CAAS;QAC3CmsC,eAAe,EAAE;OADiB,CAApC;KArFsB;;;SA2FnB5kC,GAAL,GAAWrC,WAAW,CAACC,cAAZ,CAA2B,KAAKC,IAAhC,CAAX,CA3FwB;;SA8FnB3J,SAAL,GAAiByJ,WAAW,CAACe,MAAZ,CAAmB,IAAnB,EAAyBvP,OAAzB,CAAjB,CA9FwB;;;SAkGnBoF,MAAL,gBAAoB,KAAKwK,OAAzB,GAlGwB;;;SAqGnBxK,MAAL,CAAY,mBAAZ,EArGwB;;;QAwGpB,KAAKpF,OAAL,CAAa01C,aAAb,KAA+B,KAAnC,EAA0C;WACnCC,OAAL;;;;EAIJA,OAAO,CAAC31C,OAAD,EAAU;QACXA,OAAO,IAAI,IAAf,EAAqB;OAClB;QAAEA;UAAY,IAAf;KAFa;;;QAMX,CAAC,KAAKA,OAAL,CAAa41C,WAAlB,EAA+B;WACxBC,UAAL;KAPa;;;SAWV59B,IAAL,GAAY,IAAIrP,OAAJ,CAAY,IAAZ,EAAkB5I,OAAlB,CAAZ;;SACKg1C,WAAL,CAAiB9zC,IAAjB,CAAsB,KAAK+W,IAA3B,EAZe;;;QAeTmuB,KAAK,GAAG,KAAKx8B,KAAL,CAAWzF,IAAX,CAAgB0F,KAAhB,CAAsB1F,IAApC;IACAiiC,KAAK,CAACC,IAAN,CAAWnlC,IAAX,CAAgB,KAAK+W,IAAL,CAAUxO,UAA1B;IACA28B,KAAK,CAAC0B,KAAN,GAjBe;;SAoBV96B,CAAL,GAAS,KAAKiL,IAAL,CAAUjP,OAAV,CAAkBxD,IAA3B;SACKsjB,CAAL,GAAS,KAAK7Q,IAAL,CAAUjP,OAAV,CAAkBzD,GAA3B,CArBe;;;SAyBVmU,IAAL,GAAY,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,EAAU,CAAV,EAAa,CAAb,EAAgB,CAAhB,CAAZ;SACK9D,SAAL,CAAe,CAAf,EAAkB,CAAlB,EAAqB,CAArB,EAAwB,CAAC,CAAzB,EAA4B,CAA5B,EAA+B,KAAKqC,IAAL,CAAU7O,MAAzC;SAEKg0B,IAAL,CAAU,WAAV;WAEO,IAAP;;;EAGFE,iBAAiB,CAACt9B,OAAD,EAAU;QACnByrC,YAAY,GAAG,KAAKE,eAAL,CAAqB,KAAK1zB,IAA1B,CAArB;SAEK09B,OAAL,CAAa31C,OAAb;SAEKwrC,gBAAL,CAAsBC,YAAtB;WAEO,IAAP;;;EAGFqK,iBAAiB,GAAG;WACX;MAAEvc,KAAK,EAAE,KAAK0b,gBAAd;MAAgCc,KAAK,EAAE,KAAKf,WAAL,CAAiBj0C;KAA/D;;;EAGFi1C,YAAY,CAACnyC,CAAD,EAAI;QACVoU,IAAJ;;QACI,EAAEA,IAAI,GAAG,KAAK+8B,WAAL,CAAiBnxC,CAAC,GAAG,KAAKoxC,gBAA1B,CAAT,CAAJ,EAA2D;YACnD,IAAIp1C,KAAJ,wBACYgE,CADZ,0DAEF,KAAKoxC,gBAFH,iBAGG,KAAKA,gBAAL,GAAwB,KAAKD,WAAL,CAAiBj0C,MAAzC,GAAkD,CAHrD,EAAN;;;WAOM,KAAKkX,IAAL,GAAYA,IAApB;;;EAGF49B,UAAU,GAAG;;;QAGLzP,KAAK,GAAG,KAAK4O,WAAnB;SACKA,WAAL,GAAmB,EAAnB;SACKC,gBAAL,IAAyB7O,KAAK,CAACrlC,MAA/B;;SACK,IAAIkX,IAAT,IAAiBmuB,KAAjB,EAAwB;WACjBuF,eAAL,CAAqB1zB,IAArB;MACAA,IAAI,CAAC/V,GAAL;;;;EAIJ0+B,mBAAmB,CAAC5jB,IAAD,EAAgB;sCAANiL,IAAM;MAANA,IAAM;;;QAC7BA,IAAI,CAAClnB,MAAL,KAAgB,CAApB,EAAuB;MACrBknB,IAAI,GAAG,CAAC,KAAD,EAAQ,IAAR,EAAc,IAAd,EAAoB,IAApB,CAAP;;;QAEEA,IAAI,CAAC,CAAD,CAAJ,KAAY,KAAZ,IAAqBA,IAAI,CAAC,CAAD,CAAJ,KAAY,IAArC,EAA2C;MACzCA,IAAI,CAAC,CAAD,CAAJ,GAAU,KAAKhQ,IAAL,CAAU7O,MAAV,GAAmB6e,IAAI,CAAC,CAAD,CAAjC;;;IAEFA,IAAI,CAACguB,OAAL,CAAa,KAAKh+B,IAAL,CAAUxO,UAAvB;;SACKG,KAAL,CAAWzF,IAAX,CAAgBixC,KAAhB,CAAsBjxC,IAAtB,CAA2BkxC,KAA3B,CAAiCl1C,GAAjC,CAAqC6c,IAArC,EAA2CiL,IAA3C;;;EAGF8pB,oBAAoB,CAAC/0B,IAAD,EAAO1T,GAAP,EAAY;QAC1B,CAAC,KAAKM,KAAL,CAAWzF,IAAX,CAAgBixC,KAAhB,CAAsBjxC,IAAtB,CAA2B+xC,aAAhC,EAA+C;;WAExCtsC,KAAL,CAAWzF,IAAX,CAAgBixC,KAAhB,CAAsBjxC,IAAtB,CAA2B+xC,aAA3B,GAA2C,IAAIhrC,WAAJ,CAAgB;QAAEhL,MAAM,EAAE;OAA1B,CAA3C;KAH4B;;;SAOzB0J,KAAL,CAAWzF,IAAX,CAAgBixC,KAAhB,CAAsBjxC,IAAtB,CAA2B+xC,aAA3B,CAAyC/1C,GAAzC,CAA6C6c,IAA7C,EAAmD1T,GAAnD;;;EAGF6sC,kBAAkB,CAACn5B,IAAD,EAAOo5B,EAAP,EAAW;QACvB,CAAC,KAAKxsC,KAAL,CAAWzF,IAAX,CAAgBixC,KAAhB,CAAsBjxC,IAAtB,CAA2BkyC,UAAhC,EAA4C;WACrCzsC,KAAL,CAAWzF,IAAX,CAAgBixC,KAAhB,CAAsBjxC,IAAtB,CAA2BkyC,UAA3B,GAAwC,IAAInrC,WAAJ,EAAxC;;;QAEE/G,IAAI,GAAG;MACT+rC,EAAE,EAAE,IAAI7tC,MAAJ,CAAW+zC,EAAX,CADK;MAET79B,CAAC,EAAE;KAFL;;SAIK3O,KAAL,CAAWzF,IAAX,CAAgBixC,KAAhB,CAAsBjxC,IAAtB,CAA2BkyC,UAA3B,CAAsCl2C,GAAtC,CAA0C6c,IAA1C,EAAgD7Y,IAAhD;;;EAGFmF,GAAG,CAACnF,IAAD,EAAO;QACFmF,GAAG,GAAG,IAAItF,YAAJ,CAAiB,IAAjB,EAAuB,KAAKkxC,QAAL,CAAcn0C,MAAd,GAAuB,CAA9C,EAAiDoD,IAAjD,CAAZ;;SACK+wC,QAAL,CAAch0C,IAAd,CAAmB,IAAnB,EAFQ;;;SAGHi0C,QAAL;WACO7rC,GAAP;;;EAGFgtC,KAAK,GAAG,EA7NgC;;;EAgOxClxC,MAAM,CAACjB,IAAD,EAAO;QACP,CAACzB,MAAM,CAACK,QAAP,CAAgBoB,IAAhB,CAAL,EAA4B;MAC1BA,IAAI,GAAGzB,MAAM,CAACC,IAAP,CAAYwB,IAAI,GAAG,IAAnB,EAAyB,QAAzB,CAAP;;;SAGGjD,IAAL,CAAUiD,IAAV;WACQ,KAAKW,OAAL,IAAgBX,IAAI,CAACpD,MAA7B;;;EAGF8Y,UAAU,CAAC1V,IAAD,EAAO;SACV8T,IAAL,CAAUxT,KAAV,CAAgBN,IAAhB;WACO,IAAP;;;EAGFkB,OAAO,CAACiE,GAAD,EAAM;SACN4rC,QAAL,CAAc5rC,GAAG,CAACpF,EAAJ,GAAS,CAAvB,IAA4BoF,GAAG,CAACzE,MAAhC;;QACI,EAAE,KAAKswC,QAAP,KAAoB,CAApB,IAAyB,KAAKnM,MAAlC,EAA0C;WACnCuN,SAAL;;aACQ,KAAKvN,MAAL,GAAc,KAAtB;;;;EAIJvkC,KAAK,CAAC2sB,QAAD,EAAWva,EAAX,EAAe;;QAEZ2/B,GAAG,GAAG,IAAI32C,KAAJ,oIAAZ;IAKA42C,OAAO,CAACC,IAAR,CAAaF,GAAG,CAACG,KAAjB;SAEKC,IAAL,CAAUvlB,EAAE,CAACwlB,iBAAH,CAAqBzlB,QAArB,CAAV;SACKlvB,GAAL;WACO,KAAKs5B,IAAL,CAAU,KAAV,EAAiB3kB,EAAjB,CAAP;;;EAGF3U,GAAG,GAAG;SACC2zC,UAAL;SAEKiB,KAAL,GAAa,KAAKxtC,GAAL,EAAb;;SACK,IAAIlJ,GAAT,IAAgB,KAAKsO,IAArB,EAA2B;UACrBrO,GAAG,GAAG,KAAKqO,IAAL,CAAUtO,GAAV,CAAV;;UACI,OAAOC,GAAP,KAAe,QAAnB,EAA6B;QAC3BA,GAAG,GAAG,IAAIgC,MAAJ,CAAWhC,GAAX,CAAN;;;UAGE02C,KAAK,GAAG,KAAKztC,GAAL,CAASjJ,GAAT,CAAZ;MACA02C,KAAK,CAAC70C,GAAN;WAEK40C,KAAL,CAAW3yC,IAAX,CAAgB/D,GAAhB,IAAuB22C,KAAvB;;;SAGGD,KAAL,CAAW50C,GAAX;;SAEK,IAAI8a,IAAT,IAAiB,KAAKgd,aAAtB,EAAqC;UAC7BjG,IAAI,GAAG,KAAKiG,aAAL,CAAmBhd,IAAnB,CAAb;MACA+W,IAAI,CAACnvB,QAAL;;;SAGGijC,UAAL;SACKmE,WAAL;;QAEI,KAAKlX,MAAT,EAAiB;WACVqd,SAAL;;;SAGGwC,WAAL;;SAEK/qC,KAAL,CAAW1H,GAAX;;SACK0H,KAAL,CAAWzF,IAAX,CAAgB0F,KAAhB,CAAsB3H,GAAtB;;SACK0H,KAAL,CAAWzF,IAAX,CAAgBixC,KAAhB,CAAsBlzC,GAAtB;;SACKisC,WAAL;;QAEI,KAAKvkC,KAAL,CAAWzF,IAAX,CAAgBqxC,iBAApB,EAAuC;WAChC5rC,KAAL,CAAWzF,IAAX,CAAgBqxC,iBAAhB,CAAkCtzC,GAAlC;;;QAGE,KAAK6C,SAAT,EAAoB;WACbA,SAAL,CAAe7C,GAAf;;;QAGE,KAAKizC,QAAL,KAAkB,CAAtB,EAAyB;aAChB,KAAKoB,SAAL,EAAP;KADF,MAEO;aACG,KAAKvN,MAAL,GAAc,IAAtB;;;;EAIJuN,SAAS,GAAG;;QAEJS,UAAU,GAAG,KAAKlyC,OAAxB;;SACKM,MAAL,CAAY,MAAZ;;SACKA,MAAL,aAAiB,KAAK8vC,QAAL,CAAcn0C,MAAd,GAAuB,CAAxC;;SACKqE,MAAL,CAAY,qBAAZ;;SAEK,IAAIP,MAAT,IAAmB,KAAKqwC,QAAxB,EAAkC;MAChCrwC,MAAM,GAAG,oBAAaA,MAAb,EAAsBlD,KAAtB,CAA4B,CAAC,EAA7B,CAAT;;WACKyD,MAAL,CAAYP,MAAM,GAAG,WAArB;KATQ;;;QAaJoyC,OAAO,GAAG;MACd1F,IAAI,EAAE,KAAK2D,QAAL,CAAcn0C,MAAd,GAAuB,CADf;MAEdm2C,IAAI,EAAE,KAAKttC,KAFG;MAGdipC,IAAI,EAAE,KAAKiE,KAHG;MAIdK,EAAE,EAAE,CAAC,KAAKtmC,GAAN,EAAW,KAAKA,GAAhB;KAJN;;QAMI,KAAK9L,SAAT,EAAoB;MAClBkyC,OAAO,CAACG,OAAR,GAAkB,KAAKryC,SAAL,CAAe0E,UAAjC;;;SAGGrE,MAAL,CAAY,SAAZ;;SACKA,MAAL,CAAYjE,SAAS,CAACC,OAAV,CAAkB61C,OAAlB,CAAZ;;SAEK7xC,MAAL,CAAY,WAAZ;;SACKA,MAAL,WAAe4xC,UAAf;;SACK5xC,MAAL,CAAY,OAAZ,EA5BU;;;WA+BH,KAAKlE,IAAL,CAAU,IAAV,CAAP;;;EAGFtB,QAAQ,GAAG;WACF,sBAAP;;;;;AAIJ,IAAMy3C,KAAK,GAAGC,OAAO,IAAI;EACvB92C,MAAM,CAAC2/B,MAAP,CAAc2U,WAAW,CAACyC,SAA1B,EAAqCD,OAArC;CADF;;AAIAD,KAAK,CAACG,aAAD,CAAL;AACAH,KAAK,CAACI,UAAD,CAAL;AACAJ,KAAK,CAACK,WAAD,CAAL;AACAL,KAAK,CAACM,UAAD,CAAL;AACAN,KAAK,CAACO,SAAD,CAAL;AACAP,KAAK,CAACQ,WAAD,CAAL;AACAR,KAAK,CAACS,gBAAD,CAAL;AACAT,KAAK,CAACU,YAAD,CAAL;AACAV,KAAK,CAACW,aAAD,CAAL;AACAX,KAAK,CAACY,aAAD,CAAL;AACAZ,KAAK,CAACa,gBAAD,CAAL;AACAb,KAAK,CAACc,WAAD,CAAL;AAEArD,WAAW,CAACna,WAAZ,GAA0BA,WAA1B;;;;"}