{"author": "<PERSON><PERSON> <PERSON> Little <<EMAIL>>", "name": "base64-js", "description": "Base64 encoding/decoding in pure JS", "version": "0.0.8", "repository": {"type": "git", "url": "git://github.com/beatgammit/base64-js.git"}, "main": "lib/b64.js", "scripts": {"test": "tape test/*.js"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "chrome/4..latest", "firefox/3..latest", "safari/5.1..latest", "opera/11.0..latest", "iphone/6", "ipad/6"]}, "engines": {"node": ">= 0.4"}, "license": "MIT", "dependencies": {}, "devDependencies": {"tape": "~2.3.2"}}